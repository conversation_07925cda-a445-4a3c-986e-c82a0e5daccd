# ==============================================================================
# CRITICAL STARTUP STANZA - MUST BE FIRST
# Sets essential environment variables before any other shell initialization
# ==============================================================================

# Basic PATH - absolute minimum for system functionality
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin:/opt/homebrew/bin"

# XDG Base Directory Specification - set immediately
export XDG_CONFIG_HOME="${XDG_CONFIG_HOME:-${HOME}/.config}"
export XDG_CACHE_HOME="${XDG_CACHE_HOME:-${HOME}/.cache}"
export XDG_DATA_HOME="${XDG_DATA_HOME:-${HOME}/.local/share}"
export XDG_STATE_HOME="${XDG_STATE_HOME:-${HOME}/.local/state}"
export XDG_BIN_HOME="${XDG_BIN_HOME:-${HOME}/.local/bin}"

# Platform-specific XDG runtime directory
if [[ "$(uname -s)" == "Darwin" ]]; then
    export XDG_RUNTIME_DIR="${XDG_RUNTIME_DIR:-${HOME}/Library/Caches/TemporaryItems}"
else
    export XDG_RUNTIME_DIR="${XDG_RUNTIME_DIR:-/run/user/$(id -u)}"
fi

# Zsh-specific critical directories
export ZDOTDIR="${XDG_CONFIG_HOME}/zsh"
export ZSH_CACHE_DIR="${XDG_CACHE_HOME}/zsh"

# Ensure critical directories exist immediately
mkdir -p "${XDG_CONFIG_HOME}" "${XDG_CACHE_HOME}" "${XDG_DATA_HOME}" "${XDG_STATE_HOME}" "${ZDOTDIR}" "${ZSH_CACHE_DIR}" 2>/dev/null


# Robust SHORT_HOST calculation with fallbacks
if [[ -z "$SHORT_HOST" ]]; then
    if [[ "$OSTYPE" = darwin* ]]; then
        # macOS's $HOST changes with dhcp, etc. Use LocalHostName if possible.
        SHORT_HOST=$(scutil --get LocalHostName 2>/dev/null) || SHORT_HOST="${HOST/.*/}" || SHORT_HOST=$(hostname -s 2>/dev/null) || SHORT_HOST="localhost"
    else
        SHORT_HOST="${HOST/.*/}" || SHORT_HOST=$(hostname -s 2>/dev/null) || SHORT_HOST="localhost"
    fi
fi

# Set robust ZSH_COMPDUMP path
export ZSH_COMPDUMP="${ZSH_CACHE_DIR}/.zcompdump-${SHORT_HOST}-${ZSH_VERSION}"
# Ensure ZSH_COMPDUMP is writable
touch "$ZSH_COMPDUMP" 2>/dev/null || {
    export ZSH_COMPDUMP="${HOME}/.zcompdump-${SHORT_HOST}-${ZSH_VERSION}"
    touch "$ZSH_COMPDUMP" 2>/dev/null || {
        # Final fallback - use a guaranteed safe path
        export ZSH_COMPDUMP="/tmp/.zcompdump-${USER:-$(whoami)}-${ZSH_VERSION}"
        touch "$ZSH_COMPDUMP" 2>/dev/null
    }
}

# Simple, safe PATH - MUST BE FIRST
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin"
[[ -d /run/current-system/sw/bin ]] && PATH="/run/current-system/sw/bin:$PATH"
[[ -d /opt/homebrew/bin ]] && PATH="/opt/homebrew/bin:$PATH"
[[ -d $XDG_BIN_HOME ]] && PATH="$XDG_BIN_HOME:$PATH"
# DISABLE THIS FOR NOW - CAUSING HANGS: typeset -U path PATH

# Essential shell variables for startup
export ZSH_DEBUG="${ZSH_DEBUG:-0}"
export HISTFILE="${ZDOTDIR}/.zsh_history"
export SHELL_SESSIONS_DISABLE=1  # Disable macOS session restore

# Conditionally disable verbose output during normal startup
# Allow debugging when ZSH_DEBUG_VERBOSE is set
if [[ -z "$ZSH_DEBUG_VERBOSE" ]]; then
    setopt NO_VERBOSE
    # setopt NO_XTRACE  # Commented out to allow testing and debugging
    # Also disable function tracing globally
    setopt NO_FUNCTION_ARGZERO
fi

# ==============================================================================
# END CRITICAL STARTUP STANZA
# ==============================================================================

# ==============================================================================
# DISABLE WARP TERMINAL HOOKS (fixes tee issue) - AGGRESSIVE VERSION
# ==============================================================================

# Function to completely remove Warp hooks
_remove_warp_hooks() {
    # Clear the hook arrays completely
    unset precmd_functions 2>/dev/null
    unset preexec_functions 2>/dev/null
    
    # Re-initialize as empty arrays
    typeset -ga precmd_functions
    typeset -ga preexec_functions
    precmd_functions=()
    preexec_functions=()
    
    # Remove specific Warp functions if they exist
    unfunction warp_set_title_idle_on_precmd 2>/dev/null
    unfunction warp_set_title_active_on_preexec 2>/dev/null
    
    # Set a flag to indicate we've cleaned hooks
    typeset -g _WARP_HOOKS_DISABLED=1
}

# Call the removal function immediately
_remove_warp_hooks

# Create a persistent hook remover that runs before every command
# This will catch and remove Warp hooks if they get reinstalled
_persistent_warp_hook_remover() {
    # Only run if we detect Warp hooks have been reinstalled
    if [[ ${#precmd_functions[@]} -gt 0 || ${#preexec_functions[@]} -gt 0 ]]; then
        local has_warp=0
        
        # Check for Warp-specific functions
        for func in "${precmd_functions[@]}" "${preexec_functions[@]}"; do
            if [[ "$func" == *warp* ]]; then
                has_warp=1
                break
            fi
        done
        
        if [[ $has_warp -eq 1 ]]; then
            # Warp has reinstalled hooks, remove them again
            _remove_warp_hooks
        fi
    fi
}

# Install our persistent remover as a precmd hook
# This ensures it runs before every prompt
if [[ -z "$_WARP_REMOVER_INSTALLED" ]]; then
    precmd_functions=(_persistent_warp_hook_remover)
    typeset -g _WARP_REMOVER_INSTALLED=1
fi

# ==============================================================================

# ==============================================================================
# WARP TERMINAL TEE ISSUE RESOLVED
# The tee error was caused by Warp Terminal's precmd/preexec hooks.
# The aggressive hook removal system above prevents this issue.
# Diagnostic logging removed since issue is resolved.
# ==============================================================================

export ZSH_DEBUG=${ZSH_DEBUG:-0}

[[ $ZSH_DEBUG == 1 ]] && {
    printf "# ++++++ %s:zshenv ++++++++++++++++++++++++++++++++++++\n" "$0" >&2
    # Add this check to detect errant file creation:
    if [[ -f "${ZDOTDIR:-$HOME}/2" ]] || [[ -f "${ZDOTDIR:-$HOME}/3" ]]; then
        echo "Warning: Numbered files detected - check for redirection typos" >&2
    fi
}

# Developer tools PATH - these append to the basic PATH set above
[[ -d "/Applications/Xcode.app/Contents/Developer/usr/bin" ]] && PATH="$PATH:/Applications/Xcode.app/Contents/Developer/usr/bin"
[[ -d "/Library/Developer/CommandLineTools/usr/bin" ]] && PATH="$PATH:/Library/Developer/CommandLineTools/usr/bin"

# Export build environment variables IMMEDIATELY
export CC="/usr/bin/cc"
export CXX="/usr/bin/c++"
export CPP="/usr/bin/cpp"
export DEVELOPER_DIR="${DEVELOPER_DIR:-/Applications/Xcode.app/Contents/Developer}"

# Disable zgenom's automatic compinit (add before zgenom load calls)
# TEMPORARILY DISABLED - Re-enabling auto compinit to fix completion issues
# export ZGEN_AUTOLOAD_COMPINIT=0
# export ZGENOM_AUTO_COMPINIT=0

# DISABLED - POTENTIALLY CAUSING HANGS: Prevent duplicate entries in PATH and FPATH arrays
# typeset -x PATH FPATH
# typeset -aUx path fpath

# PATH deduplication disabled for now due to hangs

# Create safe command wrappers
command_exists() { command -v "$1" >/dev/null 2>&1; }
safe_uname() { command_exists uname && uname "$@" || echo "unknown"; }
safe_sed() { command_exists sed && sed "$@" || echo "sed not available"; }

# OS-specific compatibility aliases
case "$(uname -s)" in
    Darwin|*BSD*)
        # macOS/BSD: readlink doesn't have -f flag, use realpath instead
        if command_exists realpath; then
            alias readlink-f='realpath'
        else
            # Fallback for systems without realpath
            readlink-f() {
                local target="$1"
                if [[ -L "$target" ]]; then
                    cd "$(dirname "$target")"
                    target="$(readlink "$(basename "$target")")"
                    # Handle relative symlinks
                    if [[ "$target" != /* ]]; then
                        target="$(pwd)/$target"
                    fi
                fi
                echo "$target"
            }
        fi
        ;;
    Linux*)
        # Linux: readlink has -f flag
        alias readlink-f='readlink -f'
        ;;
    *)
        # Unknown OS: try realpath first, fallback to basic readlink
        if command_exists realpath; then
            alias readlink-f='realpath'
        else
            alias readlink-f='readlink'
        fi
        ;;
esac

## [_path]
{
    function _path_remove() {
        for ARG in "$@"; do
            while [[ ":${PATH}:" == *":${ARG}:"* ]]; do
                # Delete path by parts to avoid accidentally removing sub paths
                [[ "${PATH}" == "${ARG}" ]] && PATH=""
                PATH=${PATH//":${ARG}:"/":"} # delete any instances in the middle
                PATH=${PATH/#"${ARG}:"/}     # delete any instance at the beginning
                PATH=${PATH/%":${ARG}"/}     # delete any instance at the end
            done
        done
        export PATH
    }

    function _path_append() {
        for ARG in "$@"; do
            _path_remove "${ARG}"
            [[ -d "${ARG}" ]] && export PATH="${PATH:+"${PATH}:"}${ARG}"
        done
    }

    function _path_prepend() {
        for ARG in "$@"; do
            _path_remove "${ARG}"
            [[ -d "${ARG}" ]] && export PATH="${ARG}${PATH:+":${PATH}"}"
        done
    }

    # DISABLED: Add user local bin to PATH - XDG_BIN_HOME not defined yet
    # _path_prepend "${XDG_BIN_HOME}"
}

# Debug output when ZSH_DEBUG is enabled
if [[ "$ZSH_DEBUG" == "1" ]]; then
    echo "[DEBUG .zshenv] SHORT_HOST: $SHORT_HOST" >&2
    echo "[DEBUG .zshenv] ZSH_COMPDUMP: $ZSH_COMPDUMP" >&2
    echo "[DEBUG .zshenv] ZSH_CACHE_DIR: $ZSH_CACHE_DIR" >&2
    echo "[DEBUG .zshenv] ZSH_COMPDUMP exists: $([[ -f "$ZSH_COMPDUMP" ]] && echo 'YES' || echo 'NO')" >&2
    echo "[DEBUG .zshenv] ZSH_COMPDUMP writable: $([[ -w "$ZSH_COMPDUMP" ]] && echo 'YES' || echo 'NO')" >&2
fi

typeset -gxa ZSH_AUTOSUGGEST_STRATEGY
ZSH_AUTOSUGGEST_STRATEGY=(history completion)

typeset -gxa GLOBALIAS_FILTER_VALUES
GLOBALIAS_FILTER_VALUES=("sudo" "man" "which" "bob")


# Create required directories
mkdir -p "${ZSH_CACHE_DIR}"

# Plugin manager setup - zgenom configuration
export ZGENOM_PARENT_DIR="${ZDOTDIR}"
export ZGEN_SOURCE="${ZDOTDIR}/.zqs-zgenom"
export ZGENOM_SOURCE_FILE="${ZGEN_SOURCE}/zgenom.zsh"
export ZGEN_DIR="${ZDOTDIR}/.zgenom"
export ZGEN_INIT="${ZGEN_DIR}/init.zsh"
export ZGENOM_BIN_DIR="${ZGEN_DIR}/_bin"

# Oh-My-ZSH configuration for zgenom
export ZGEN_OH_MY_ZSH_REPO="ohmyzsh/ohmyzsh"
export ZGEN_OH_MY_ZSH_BRANCH="master"

# Ensure zgenom functions are in fpath for autoloading
if [[ -d "${ZGEN_SOURCE}/functions" ]]; then
    fpath=("${ZGEN_SOURCE}/functions" $fpath)
fi

# Disable macOS session restore
export SHELL_SESSIONS_DISABLE=1

# History configuration
export HISTDUP=erase
export HISTFILE="${ZDOTDIR}/.zsh_history"
export HISTSIZE=1000000
export HISTTIMEFORMAT='%F %T %z %a %V '
export SAVEHIST=1100000

# Core application settings
export DISPLAY=:0.0
export LANG='en_GB.UTF-8'
export LC_ALL='en_GB.UTF-8'
export TIME_STYLE=long-iso

set_editor() {
    # Set editors if available (with fallback)
    local editors=(nvim hx vim nano)
    for e in $editors; do
        [[ -n "$e" ]] &&
            command -v "$e" >/dev/null 2>&1 &&
            { export EDITOR="$e"; break; }
    done
}

set_visual() {
    # Set editors if available (with fallback)
    set_editor
    local editors=(code-insiders code zed hx "$EDITOR")
    for e in $editors; do
        [[ -n "$e" ]] &&
            command -v "$e" >/dev/null 2>&1 &&
            { export VISUAL="$e"; break; }
    done
}
set_visual

# Java setup (macOS specific)
if [[ -x "/usr/libexec/java_home" ]]; then
    JAVA_HOME="$(/usr/libexec/java_home 2>/dev/null)" && export JAVA_HOME
fi

# Less configuration with color support
export LESS=' --HILITE-UNREAD --LONG-PROMPT --no-histdups --ignore-case --incsearch --no-init --line-numbers --mouse --quit-if-one-screen --squeeze-blank-lines --status-column --tabs=4 --use-color --window=-4 --RAW-CONTROL-CHARS '

# D-Bus session setup (Linux compatibility)
export MY_SESSION_BUS_SOCKET="/tmp/dbus/${USER}.session.usock"
export DBUS_SESSION_BUS_ADDRESS="unix:path=${MY_SESSION_BUS_SOCKET}"

# MCP server allowed directories for file operations
export ALLOWED_DIRECTORIES="/Users/<USER>/Desktop,/Users/<USER>/Downloads,/Users/<USER>/Herd,/Users/<USER>/Library,/Users/<USER>/Projects,/Users/<USER>/Work,/Users/<USER>/.config,/Users/<USER>/dotfiles,/Users/<USER>/.local,/Users/<USER>/.zshenv,/Users/<USER>/.zshrc,/Users/<USER>/.zprofile,/Users/<USER>/.zlogin"


## [_field]
{
    function _field_append() {
        ## SYNOPSIS: field_append varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## Note: Forces fieldVal into the last position, if already present.
        ##             Duplicates are removed, too.
        ## EXAMPLE: field_append PATH /usr/local/bin
        local varName=$1 fieldVal=$2 IFS=${3:-':'}
        read -ra auxArr <<<"${!varName}"
        for i in "${!auxArr[@]}"; do
            [[ ${auxArr[i]} == "$fieldVal" ]] && unset 'auxArr[i]'
        done
        auxArr+=("$fieldVal")
        printf -v "$varName" '%s' "${auxArr[*]}"
    }

    function _field_contains() {
        ## SYNOPSIS: field_contains varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_contains PATH /usr/local/bin
        local varName=$1 fieldVal=$2 IFS=${3:-':'}
        read -ra auxArr <<<"${!varName}"
        for i in "${!auxArr[@]}"; do
            [[ ${auxArr[i]} == "$fieldVal" ]] && return 0
        done
        return 1
    }

    function _field_delete() {
        ## SYNOPSIS: field_delete varName fieldNum [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_delete PATH 2
        local varName=$1 fieldNum=$2 IFS=${3:-':'}
        read -ra auxArr <<<"${!varName}"
        unset 'auxArr[fieldNum]'
        printf -v "$varName" '%s' "${auxArr[*]}"
    }

    function _field_find() {
        ## SYNOPSIS: field_find varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_find PATH /usr/local/bin
        local varName=$1 fieldVal=$2 IFS=${3:-':'}
        read -ra auxArr <<<"${!varName}"
        for i in "${!auxArr[@]}"; do
            [[ ${auxArr[i]} == "$fieldVal" ]] && return 0
        done
        return 1
    }

    function _field_get() {
        ## SYNOPSIS: field_get varName fieldNum [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_get PATH 2
        printf "$# : $@"
        local varName=$1 fieldNum=$2 IFS=${3:-':'}
        read -ra auxArr <<<"${!varName}"
        printf '%s' "${auxArr[fieldNum]}"
    }

    function _field_insert() {
        ## SYNOPSIS: field_insert varName fieldNum fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_insert PATH 2 /usr/local/bin
        local varName=$1 fieldNum=$2 fieldVal=$3 IFS=${4:-':'}
        read -ra auxArr <<<"${!varName}"
        auxArr=("${auxArr[@]:0:fieldNum}" "$fieldVal" "${auxArr[@]:fieldNum}")
        printf -v "$varName" '%s' "${auxArr[*]}"
    }

    function _field_prepend() {
        ## SYNOPSIS: field_prepend varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## Note: Forces fieldVal into the first position, if already present.
        ##             Duplicates are removed, too.
        ## EXAMPLE: field_prepend PATH /usr/local/bin
        local varName=$1 fieldVal=$2 IFS=${3:-':'}
        read -ra auxArr <<<"${!varName}"
        for i in "${!auxArr[@]}"; do
            [[ ${auxArr[i]} == "$fieldVal" ]] && unset 'auxArr[i]'
        done
        auxArr=("$fieldVal" "${auxArr[@]}")
        printf -v "$varName" '%s' "${auxArr[*]}"
    }

    function _field_remove() {
        ## SYNOPSIS: field_remove varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## Note: Duplicates are removed, too.
        ## EXAMPLE: field_remove PATH /usr/local/bin
        local varName=$1 fieldVal=$2 IFS=${3:-':'}
        read -ra auxArr <<<"${!varName}"
        for i in "${!auxArr[@]}"; do
            [[ ${auxArr[i]} == "$fieldVal" ]] && unset 'auxArr[i]'
        done
        printf -v "$varName" '%s' "${auxArr[*]}"
    }

    function _field_replace() {
        ## SYNOPSIS: field_replace varName fieldVal newFieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_replace PATH /usr/local/bin /usr/local/bin2
        local varName=$1 fieldVal=$2 newFieldVal=$3 IFS=${4:-':'}
        read -ra auxArr <<<"${!varName}"
        for i in "${!auxArr[@]}"; do
            [[ ${auxArr[i]} == "$fieldVal" ]] && auxArr[i]="$newFieldVal"
        done
        printf -v "$varName" '%s' "${auxArr[*]}"
    }

    function _field_set() {
        ## SYNOPSIS: field_set varName fieldNum fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_set PATH 2 /usr/local/bin
        local varName=$1 fieldNum=$2 fieldVal=$3 IFS=${4:-':'}
        read -ra auxArr <<<"${!varName}"
        auxArr[fieldNum]="$fieldVal"
        printf -v "$varName" '%s' "${auxArr[*]}"
    }

    function _field_test() {
        ## SYNOPSIS: _field_test
        ## EXAMPLE: _field_test
        ## DESCRIPTION: Test function for the _field library.
        ##              It tests all the functions in the library.
        ##              It is not meant to be called directly.
        ##              It is meant to be called from the _test function.
        local varName=PATH fieldNum=2 fieldVal=/usr/local/bin fieldVal2=/usr/local/bin2
        local auxArr
        printf -v "$varName" '%s' '/usr/bin:/usr/local/bin:/bin:/usr/sbin'
        _field_get "$varName" "$fieldNum"
        _field_set "$varName" "$fieldNum" "$fieldVal"
        _field_insert "$varName" "$fieldNum" "$fieldVal"
        _field_delete "$varName" "$fieldNum"
        _field_find "$varName" "$fieldVal"
        _field_replace "$varName" "$fieldVal" "$fieldVal2"
        _field_contains "$varName" "$fieldVal"
        _field_append "$varName" "$fieldVal"
        _field_prepend "$varName" "$fieldVal"
        _field_append "$varName" "$fieldVal"
        _field_remove "$varName" "$fieldVal"
    }
}


# Load all environment files from .env directory
# This ensures API keys and other environment variables are available for all shell sessions,
# including non-interactive ones used by MCP servers
if [[ -d "${ZDOTDIR}/.env" ]]; then
  for env_file in "${ZDOTDIR}/.env"/*.env; do
    [[ -r "$env_file" ]] && source "$env_file"
  done
  unset env_file
fi

# History management functions
# Disables zsh history recording by overriding the zshaddhistory hook
# shellcheck disable=SC1073
function disablehistory() {
    function zshaddhistory() { return 1 }
}

# Re-enables zsh history recording by removing the zshaddhistory override
function enablehistory() {
    unset -f zshaddhistory
}

typeset -g POWERLEVEL9K_INSTANT_PROMPT=quiet
