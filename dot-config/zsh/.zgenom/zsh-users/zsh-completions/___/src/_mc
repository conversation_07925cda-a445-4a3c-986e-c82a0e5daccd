#compdef mc
# ------------------------------------------------------------------------------
# Copyright (c) 2019 <PERSON>
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for Midnight Commander 4.8.28 (https://midnight-commander.org/)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Sebastian Gniazdowski (https://github.com/psprint)
# ------------------------------------------------------------------------------

setopt localoptions warncreateglobal typesetsilent

local -a opts
opts=(
  {-h,--help}'[show help on options]'
  --help-all'[show help on all options]'
  --help-terminal'[terminal options help]'
  --help-color'[color options help]'
  {-a,--stickchars}'[use stickchars to draw]'
  '(-c --color -b --nocolor)'{-b,--nocolor}'[requests to run in black and white]'
  '(-b --nocolor -c --color)'{-c,--color}'[request to run in color mode]'
  {-C,--colors=}'[specifies a color configuration]:arg:'
  --configure-options'[print configure options]'
  {-d,--nomouse}'[disable mouse support in text version]'
  {-d,--debuglevel=}'[save the debug level for SMB VFS]:debug level:(0 1 2 3 4 5 6 7 8 9 10)'
  {-e,--edit=}'[edit files]:file:_files'
  {-f,--datadir}'[print data directory]'
  {-F,--datadir-info}'[print extended info about used data directories]'
  {-g,--oldmouse}'[tries to use an old highlight mouse tracking]'
  {-k,--resetsoft}'[resets soft keys on HP terminals]'
  {-K,--keymap=}'[load definitions of key bindings from specified file]:file:_files'
  {-l,--ftplog=}'[log ftp dialog to specified file]:file:_files'
  --nokeymap"[don't load key bindings from any file, use default hardcoded keys]"
  {-P,--printwd=}'[print last working directory to specified file]:file:_files'
  {-s,--slow}'[to run on slow terminals]'
  {-S,--skin=}'[show mc with specified skin]:skin:'
  {-t,--termcap}'[tries to use termcap instead of terminfo]'
  '(-U --subshell -u --nosubshell)'{-u,--nosubshell}'[disables subshell support]'
  '(-u --nosubshell -U --subshell)'{-U,--subshell}'[enables subshell support (default)]'
  {-v,--view=}'[launches the file viewer on a file]:file:_files'
  {-V,--version}'[displays the current version]'
  {-x,--xterm}'[forces xterm features]'
  {-X,--no-x11}'[disable X11 support]'
  '*:pane directory:_directories'
)

_arguments -s $opts

# Return value passes through

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
