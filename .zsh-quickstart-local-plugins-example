#!/bin/zsh
#
# Only including a shebang to trigger editors to use shell syntax highlighting
#
# Copyright 2006-2021 <PERSON> <<EMAIL>>
#
# The ZSH Starter Kit allows you to replace the curated plugins list it
# ships with with a custom one by creating a file named .zsh-quickstart-local-plugins.
#
# This is to make it easier to customize without having to maintain a separate
# fork of the starter kit.
#
# This example file duplicates the list of plugins that shipped with the
# kit as of 2020-01-01.

ZGEN_LOADED=()
ZGEN_COMPLETIONS=()

if [[ $(_zqs-get-setting load-omz-plugins true) == 'true' ]]; then
  zgenom oh-my-zsh
fi

warn-about-prompt-change() {
  if [[ ! -f ~/.zsh-quickstart-kit-prompt-switch-warning ]]; then
    echo
    echo "The default ZSH prompt in the quickstart kit has changed from"
    echo "bullet-train to powerlevel10k."
    echo
    echo "If you want to use the old prompt, run zsh-quickstart-select-bullet-train"
    echo
    echo "Run zsh-quickstart-select-powerlevel10k to switch to the new prompt"
    echo
    echo "If you do nothing, you will be switched to powerlevel10k"
    echo
    touch ~/.zsh-quickstart-kit-prompt-switch-warning
  fi
}

# If you want to customize your plugin list, create a file named
# .zsh-quickstart-local-plugins in your home directory. That file will be sourced
# during startup *instead* of running this load-starter-plugin-list function,
# so make sure to include everything from this function that you want to
# keep.
#
# To make customizing easier, there's a .zsh-quickstart-local-plugins-example
# file at the top level of the zsh-quickstart-kit repository that you can copy
# as a starting point. This keeps you from having to maintain a fork of
# the quickstart kit.

# If zsh-syntax-highlighting is bundled after zsh-history-substring-search,
# they break, so get the order right.
zgenom load zdharma-continuum/fast-syntax-highlighting
zgenom load zsh-users/zsh-history-substring-search

# Set keystrokes for substring searching
zmodload zsh/terminfo
bindkey "$terminfo[kcuu1]" history-substring-search-up
bindkey "$terminfo[kcud1]" history-substring-search-down

# Tab complete rakefile targets.
zgenom load unixorn/rake-completion.zshplugin

# Automatically run zgenom update and zgenom selfupdate every 7 days.
zgenom load unixorn/autoupdate-zgenom

# Add my collection of miscellaneous utility functions.
zgenom load unixorn/jpb.zshplugin

# Colorize the things if you have grc installed. Well, some of the
# things, anyway.
zgenom load unixorn/warhol.plugin.zsh

# macOS helpers. This plugin is smart enough to detect when it isn't running
# on macOS and not load itself, so you can safely share the same plugin list
# across macOS and Linux/BSD.
zgenom load unixorn/tumult.plugin.zsh

# Deal with Apple's squirrelly DNS
zgenom load eventi/noreallyjustfuckingstopalready

# Warn you when you run a command that you've set an alias for without
# using the alias.
zgenom load djui/alias-tips

# Add my collection of git helper scripts.
zgenom load unixorn/git-extra-commands

# Add diff-so-fancy so git can use it
if [[ "$(_zqs-get-setting diff-so-fancy true)" == 'true' ]]; then
  zgenom load so-fancy/diff-so-fancy
fi

# Supercharge your history search with fzf
zgenom load unixorn/fzf-zsh-plugin

# Soup up command history search with fzf.
zgen load unixorn/fzf-zsh-plugin

# Add my bitbucket git helpers plugin.
zgenom load unixorn/bitbucket-git-helpers.plugin.zsh

# A collection of scripts that might be useful to sysadmins.
zgenom load skx/sysadmin-util

# Adds aliases to open your current repo & branch on github.
zgenom load peterhurford/git-it-on.zsh

# Tom Limoncelli's tooling for storing private information (keys, etc)
# in a repository securely by encrypting them with gnupg.
zgenom load StackExchange/blackbox

if [[ $(_zqs-get-setting load-omz-plugins true) == 'true' ]]; then
  # Load some oh-my-zsh plugins
  zgenom oh-my-zsh plugins/pip
  zgenom oh-my-zsh plugins/sudo
  zgenom oh-my-zsh plugins/aws
  zgenom oh-my-zsh plugins/chruby
  zgenom oh-my-zsh plugins/colored-man-pages
  zgenom oh-my-zsh plugins/git
  zgenom oh-my-zsh plugins/github
  zgenom oh-my-zsh plugins/python
  zgenom oh-my-zsh plugins/rsync
  zgenom oh-my-zsh plugins/screen
  zgenom oh-my-zsh plugins/vagrant
  if [[ $(uname -a | grep -ci Darwin) = 1 ]]; then
    # Load macOS-specific plugins
    zgenom oh-my-zsh plugins/brew
    zgenom oh-my-zsh plugins/macos
  fi

fi

# A set of shell functions to make it easy to install small apps and
# utilities distributed with pip.
zgenom load sharat87/pip-app

zgenom load chrissicool/zsh-256color

# Load more completion files for zsh from the zsh-lovers github repo.
zgenom load zsh-users/zsh-completions src

# Docker completion
zgenom load srijanshetty/docker-zsh

# Load tab completions for 1Password's op command line tool if it's installed
zgenom load unixorn/1password-op.plugin.zsh

# Load me last
GENCOMPL_FPATH=$HOME/.zsh/complete

# Very cool plugin that generates zsh completion functions for commands
# if they have getopt-style help text. It doesn't generate them on the fly,
# you'll have to explicitly generate a completion, but it's still quite cool.
zgenom load RobSis/zsh-completion-generator

# Add Fish-like autosuggestions to your ZSH.
zgenom load zsh-users/zsh-autosuggestions

# k is a zsh script / plugin to make directory listings more readable,
# adding a bit of color and some git status information on files and
# directories.
zgenom load supercrabtree/k

if [[ $(_zqs-get-setting bullet-train false) == 'true' ]]; then
  # Bullet train prompt setup.
  zgenom load https://github.com/caiogondim/bullet-train.zsh
else
  # p10k is faster and what I'm using now, so it is the new default
  zgenom load romkatv/powerlevel10k powerlevel10k
  warn-about-prompt-change
fi

# Save it all to init script
zgenom save
