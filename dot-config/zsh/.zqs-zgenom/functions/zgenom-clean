#!/usr/bin/env zsh

function zgenom-clean() {
    emulate -L zsh
    setopt localoptions nullglob extendedglob
    local repo

    pushd -q $ZGEN_DIR || return 1

    for repo in ${ZGENOM_PLUGINS[@]}; do
        printf '' > $repo/.zgenom-keep
    done
    for repo in **/*~*/*/*/*(/On); do
        if [[ -z $repo/**/.zgenom-keep(#q) && -d $repo ]] rm -drf "$repo" && __zgenom_out "Removed '$repo'."
    done
    rm **/*/.zgenom-keep

    popd -q
}

zgenom-clean $@
