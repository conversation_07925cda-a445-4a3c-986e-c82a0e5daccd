#!/usr/bin/env zsh

function zgenom-help() {
    __zgenom_out 'usage: `zgenom [command] [options]`'
    __zgenom_out "    commands:"
    __zgenom_out
    printf       '    %s\n' ${"${(@f)$(< $ZGEN_SOURCE/commands.txt)}"//\^/ }
    __zgenom_out
    __zgenom_out "    Please check the zsh completion to find out more about the options of commands."
    if [[ -n $ZGENOM_EXTENSIONS[1] ]]; then
        __zgenom_out
        __zgenom_out 'installed extensions'
        __zgenom_out '    commands:'
        __zgenom_out
        for cmd in $ZGENOM_EXTENSIONS; do
            printf   '    %s\n' ${cmd/:/: }
        done
    fi
}

zgenom-help $@
