#autoload
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completer for language codes.
#
#  Usage: _language_codes ISO-639-1
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * <PERSON> <<EMAIL>>
#
# ------------------------------------------------------------------------------


(( $+functions[_language_codes_iso_639_1] )) ||
_language_codes_iso_639_1() {
  local codes; codes=(
    'aa:Afar'
    'ab:Abkhazian'
    'af:Afrikaans'
    'ak:Akan'
    'sq:Albanian'
    'am:Amharic'
    'ar:Arabic'
    'an:Aragonese'
    'hy:Armenian'
    'as:Assamese'
    'av:Avaric'
    'ae:Avestan'
    'ay:Aymara'
    'az:Azerbaijani'
    'ba:Bashkir'
    'bm:Bambara'
    'eu:Basque'
    'be:Belarusian'
    'bn:Bengali'
    'bh:Bihari languages'
    'bi:Bislama'
    'bo:Tibetan'
    'bs:Bosnian'
    'br:Breton'
    'bg:Bulgarian'
    'my:Burmese'
    'ca:Catalan'
    'cs:Czech'
    'ch:Chamorro'
    'ce:Chechen'
    'zh:Chinese'
    'cu:Church Slavic'
    'cv:Chuvash'
    'kw:Cornish'
    'co:Corsican'
    'cr:Cree'
    'cy:Welsh'
    'cs:Czech'
    'da:Danish'
    'de:German'
    'dv:Divehi'
    'nl:Dutch'
    'dz:Dzongkha'
    'el:Greek, Modern (1453-)'
    'en:English'
    'eo:Esperanto'
    'et:Estonian'
    'eu:Basque'
    'ee:Ewe'
    'fo:Faroese'
    'fa:Persian'
    'fj:Fijian'
    'fi:Finnish'
    'fr:French'
    'fy:Western Frisian'
    'ff:Fulah'
    'ka:Georgian'
    'de:German'
    'gd:Gaelic'
    'ga:Irish'
    'gl:Galician'
    'gv:Manx'
    'gn:Guarani'
    'gu:Gujarati'
    'ht:Haitian'
    'ha:Hausa'
    'he:Hebrew'
    'hz:Herero'
    'hi:Hindi'
    'ho:Hiri Motu'
    'hr:Croatian'
    'hu:Hungarian'
    'hy:Armenian'
    'ig:Igbo'
    'is:Icelandic'
    'io:Ido'
    'ii:Sichuan Yi'
    'iu:Inuktitut'
    'ie:Interlingue'
    'ia:Interlingua (International Auxiliary Language Association)'
    'id:Indonesian'
    'ik:Inupiaq'
    'is:Icelandic'
    'it:Italian'
    'jv:Javanese'
    'ja:Japanese'
    'kl:Kalaallisut'
    'kn:Kannada'
    'ks:Kashmiri'
    'ka:Georgian'
    'kr:Kanuri'
    'kk:Kazakh'
    'km:Central Khmer'
    'ki:Kikuyu'
    'rw:Kinyarwanda'
    'ky:Kirghiz'
    'kv:Komi'
    'kg:Kongo'
    'ko:Korean'
    'kj:Kuanyama'
    'ku:Kurdish'
    'lo:Lao'
    'la:Latin'
    'lv:Latvian'
    'li:Limburgan'
    'ln:Lingala'
    'lt:Lithuanian'
    'lb:Luxembourgish'
    'lu:Luba-Katanga'
    'lg:Ganda'
    'mk:Macedonian'
    'mh:Marshallese'
    'ml:Malayalam'
    'mi:Maori'
    'mr:Marathi'
    'ms:Malay'
    'mk:Macedonian'
    'mg:Malagasy'
    'mt:Maltese'
    'mn:Mongolian'
    'mi:Maori'
    'ms:Malay'
    'my:Burmese'
    'na:Nauru'
    'nv:Navajo'
    'nr:Ndebele, South'
    'nd:Ndebele, North'
    'ng:Ndonga'
    'ne:Nepali'
    'nl:Dutch'
    'nn:Norwegian Nynorsk'
    'nb:Bokmål, Norwegian'
    'no:Norwegian'
    'ny:Chichewa'
    'oc:Occitan (post 1500)'
    'oj:Ojibwa'
    'or:Oriya'
    'om:Oromo'
    'os:Ossetian'
    'pa:Panjabi'
    'fa:Persian'
    'pi:Pali'
    'pl:Polish'
    'pt:Portuguese'
    'ps:Pushto'
    'qu:Quechua'
    'rm:Romansh'
    'ro:Romanian'
    'ro:Romanian'
    'rn:Rundi'
    'ru:Russian'
    'sg:Sango'
    'sa:Sanskrit'
    'si:Sinhala'
    'sk:Slovak'
    'sk:Slovak'
    'sl:Slovenian'
    'se:Northern Sami'
    'sm:Samoan'
    'sn:Shona'
    'sd:Sindhi'
    'so:Somali'
    'st:Sotho, Southern'
    'es:Spanish'
    'sq:Albanian'
    'sc:Sardinian'
    'sr:Serbian'
    'ss:Swati'
    'su:Sundanese'
    'sw:Swahili'
    'sv:Swedish'
    'ty:Tahitian'
    'ta:Tamil'
    'tt:Tatar'
    'te:Telugu'
    'tg:Tajik'
    'tl:Tagalog'
    'th:Thai'
    'bo:Tibetan'
    'ti:Tigrinya'
    'to:Tonga (Tonga Islands)'
    'tn:Tswana'
    'ts:Tsonga'
    'tk:Turkmen'
    'tr:Turkish'
    'tw:Twi'
    'ug:Uighur'
    'uk:Ukrainian'
    'ur:Urdu'
    'uz:Uzbek'
    've:Venda'
    'vi:Vietnamese'
    'vietnamien'
    'vo:Volapük'
    'cy:Welsh'
    'wa:Walloon'
    'wo:Wolof'
    'xh:Xhosa'
    'yi:Yiddish'
    'yo:Yoruba'
    'za:Zhuang'
    'zh:Chinese'
    'zu:Zulu'
  )
  _describe -t language-codes 'ISO 639-1 language code' codes "$@"
}

_language_codes() {
  local command="$argv[$#]" ret=1

  case $command in
    ISO-639-1)
	  _language_codes_${${command//-/_}:l} "${@[1,-2]}" && ret=0
      ;;
    *)
      _message "unknown command: $command"
      ;;
  esac

  return ret    
}

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
