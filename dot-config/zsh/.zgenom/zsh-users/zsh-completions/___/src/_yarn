#compdef yarn
# ------------------------------------------------------------------------------
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for yarn (https://yarnpkg.com/)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

declare -g _yarn_run_cwd

_commands=(
  'access'
  'audit:Checks for known security issues with the installed packages'
  'autoclean:Clean and remove unnecessary files from package dependencies'
  'cache:List or clean every cached package'
  "check:Verify package dependencies against yarn's lock file"
  'config:Manages the yarn configuration files'
  'create:Creates new projects from any create-* starter kits'
  'exec'
  'generate-lock-entry:Generates a lock file entry'
  'global:Install packages globally on your operating system'
  'help:Show information about a command'
  'import:Generate yarn.lock from an existing npm-installed node_modules folder'
  'info:Show information about a package'
  'init:Interactively creates or updates a package.json file'
  'install:Install all the dependencies listed within package.json'
  'licenses:List licenses for installed packages'
  'link:Symlink a package folder during development'
  'login:Store registry username and email'
  'logout:Clear registry username and email'
  'node:Runs Node with the same version that the one used by Yarn itself'
  'outdated:Check for outdated package dependencies'
  'owner:Manage package owners'
  'pack:Create a compressed gzip archive of package dependencies'
  'policies:Defines project-wide policies for your project'
  'publish:Publish a package to the npm registry'
  'run:Run a defined package script'
  'tag:Add, remove, or list tags on a package'
  'team:Maintain team memberships'
  'unlink:Unlink a previously created symlink for a package'
  'unplug:Temporarily copies a package outside of the global cache for debugging purposes'
  'version:Update the package version'
  'versions:Display version information of currently installed Yarn, Node.js, and its dependencies'
  'why:Show information about why a package is installed'
  'workspace'
  'workspaces:Show information about your workspaces'
)

_global_commands=(
  'add:Installs a package and any packages that it depends on'
  'bin:Displays the location of the yarn bin folder'
  'list:List installed packages'
  'remove:Remove installed package from dependencies updating package.json'
  'upgrade:Upgrades packages to their latest version based on the specified range'
  'upgrade-interactive:Interactively upgrade packages'
)

_yarn_find_package_json() {
  local dir=$(builtin cd "$1" && pwd)

  while true
  do
    if [[ -e "${dir}/package.json" ]]; then
      echo "${dir}/package.json"
      return
    fi

    if [[ $dir == '/' ]]; then
      break
    fi

    dir=$(dirname $dir)
  done
}

_yarn_commands_scripts() {
  local -a scripts binaries
  local packageJson

  if [[ -n $opt_args[--cwd] ]]; then
    packageJson=$(_yarn_find_package_json $opt_args[--cwd])
    binaries=($(builtin cd $opt_args[--cwd] && echo node_modules/.bin/*(x:t)))
  else
    packageJson=$(_yarn_find_package_json $pwd)
    binaries=($(echo node_modules/.bin/*(x:t)))
  fi

  if [[ -n $packageJson ]]; then
    scripts=($(cat "$packageJson" | perl -0777 -MJSON::PP -n -E '$r=decode_json($_); do{($k=$_)=~s/:/\\:/g;say $k}for sort keys %{$r->{scripts}}'))
  fi

  _describe 'command or script' _commands -- _global_commands -- scripts -- binaries
}

_yarn_scripts() {
  local -a binaries scripts
  local -a commands
  local packageJson

  if [[ -n $_yarn_run_cwd ]]; then
    packageJson=$(_yarn_find_package_json $_yarn_run_cwd)
    if [[ -d "${_yarn_run_cwd}/node_modules" ]]; then
      binaries=($(builtin cd $_yarn_run_cwd && echo node_modules/.bin/*(x:t)))
    else
      binaries=($(builtin cd $_yarn_run_cwd && yarn bin | perl -wln -e 'm{^[^:]+: (\S+)$} and print $1'))
    fi
  else
    packageJson=$(_yarn_find_package_json $pwd)
    if [[ -d node_modules ]]; then
      binaries=($(echo node_modules/.bin/*(x:t)))
    else
      binaries=($(yarn bin | perl -wln -e 'm{^[^:]+: (\S+)$} and print $1'))
    fi
  fi

  if [[ -n $packageJson ]]; then
    scripts=("${(@f)$(cat ${packageJson} | perl -0777 -MJSON::PP -n -E 'binmode(STDOUT, ":encoding(UTF-8)"); %r=%{decode_json($_)->{scripts}}; do{$k=$_;($e=$k)=~s/:/\\:/g; printf "$e:$r{$k}\n"} for sort keys %r')}")
  fi

  commands=('env' $scripts $binaries)
  _describe 'command' commands
}

_yarn_global_commands() {
  local -a cmds
  cmds=('ls:List installed packages')
  _describe 'command' _global_commands
}

_yarn_commands() {
  _describe 'command' _commands -- _global_commands
}

_yarn_add_files() {
  if compset -P "(file|link):"; then
    _files
  fi
}

_yarn_workspaces() {
  local version=$(yarn --version |sed -n 's|\([0-9]*\).*|\1|p')
  local -a workspaces
  if [[ $version == "1" ]]; then
    workspaces=(${(@f)$(yarn workspaces info |sed -n -e 's/^  "\([^"]*\)": {/\1/p')})
  else
    workspaces=(${(@f)$(yarn workspaces list --json | sed -n 's|.*"name":"\([^"]*\)"}|\1|p')})
  fi
  _describe 'workspace' workspaces
}

_yarn() {
  local context state state_descr line
  typeset -A opt_args

  _arguments \
    '(-h --help)'{-h,--help}'[output usage information]' \
    '(-V --version)'{-V,--version}'[output the version number]' \
    '--verbose[output verbose messages on internal operations]' \
    '--cache-folder=[specify a custom folder to store the yarn cache]:folder:_files -/' \
    '--check-files[install will verify file tree of packages for consistency]' \
    '--cwd=[working directory to use]:path:_files -/' \
    "(--enable-pnp --pnp)--disable-pnp[disable the Plug'n'Play installation]" \
    '(--no-emoji)--emoji=[enable emoji in output(default: false)]:enabled:(true false)' \
    '(--emoji)--no-emoji[disable emoji in output]' \
    '(--disable-pnp)'{--enable-pnp,--pnp}"[enable the Plug'n'Play installation]" \
    '--flat[only allow one version of a package]' \
    '--focus[Focus on a single workspace by installing remote copies of its sibling workspaces]' \
    '--force[install and build packages even if they were built before, overwrite lockfile]' \
    "--frozen-lockfile[don't generate a lockfile and fail if an update is needed]" \
    '--global-folder=[modules folder]:folder:_files -/' \
    '--har[save HAR output of network traffic]' \
    '--https-proxy=[HTTPS proxy]:host:_hosts' \
    '--ignore-engines[ignore engines check]' \
    "--ignore-scripts[don't run lifecycle scripts]" \
    '--ignore-optional[ignore optional dependencies]' \
    '--ignore-platform[ignore platform checks]' \
    '--json[format Yarn log messages as lines of JSON]' \
    '--link-duplicates[create hardlinks to the repeated modules in node_modules]' \
    '--link-folder=[specify a custom folder to store global links]' \
    '--modules-folder=[rather than installing modules into the node_modules folder relative to the cwd, output them here]:folder:_files -/' \
    '--mutex=[use a mutex to ensure only one yarn instance is executing]:type[\:specifier]' \
    '--network-concurrency=[maximum number of concurrent network requests]:number' \
    '--network-timeout=[TCP timeout for network requests]:milliseconds' \
    "--no-bin-links[don't generate bin links when setting up packages]" \
    '--no-default-rc[prevent Yarn from automatically detecting yarnrc and npmrc files]' \
    "--no-lockfile[don't read or generate a lockfile]" \
    '--non-interactive[do not show interactive prompts]' \
    '--no-node-version-check[do not warn when using a potentially unsupported Node version]' \
    '--no-progress[disable progress bar]' \
    '--offline[trigger an error if any required dependencies are not available in local cache]' \
    '--otp=[one-time password for two factor authentication]:otpcode' \
    '--prefer-offline[use network only if dependencies are not available in local cache]' \
    '--preferred-cache-folder=[specify a custom folder to store the yarn cache if possible]:folder:_files -/' \
    '(--prod --production)'{--prod,--production}'[install only production dependencies]' \
    '--proxy=[HTTP proxy]:host:_hosts' \
    "--pure-lockfile[don't generate a lockfile]" \
    '--registry=[override configuration registry]:url:_urls' \
    '(-s --silent)'{-s,--silent}'[skip Yarn console logs, other types of logs (script output) will be printed]' \
    '--scripts-prepend-node-path=[prepend the node executable dir to the PATH in scripts]:bool:(true false)' \
    '--skip-integrity-check[run install without checking if node_modules is installed]' \
    "--strict-semver[don't compare semver loosely]" \
    '--update-checksum[update package checksums from current repository]' \
    '--use-yarnrc=[specifies a yarnrc that Yarn should use]:yarnrc:_files' \
    '1: :_yarn_commands_scripts' \
    '*:: :->command_args'


  case $state in
    command_args)
      case $words[1] in
        help)
          _arguments \
            '1: :_yarn_commands' \
        ;;

        access)
          _arguments \
            '1: :(public restricted grant revoke ls-packages ls-collaborators edit)'
        ;;

        add)
          _arguments \
            '(-D --dev)'{-D,--dev}'[install packages in devDependencies]' \
            '(-P --peer)'{-P,--peer}'[install packages in peerDependencies]' \
            '(-O --optional)'{-O,--optional}'[install packages in optionalDependencies]' \
            '(-E --exact)'{-E,--exact}'[install packages as exact versions]' \
            '(-T --tilde)'{-T,--tilde}'[install the most recent release of the packages that have the same minor version]' \
            '(--ignore-workspace-root-check -W)'{--ignore-workspace-root-check,-W}'[allows a package to be installed at the workspaces root]' \
            '--audit[checks for known security issues with the installed packages]' \
            '*:package-name:_yarn_add_files'
        ;;

        audit)
          _arguments \
            '--verbose[output verbose message]' \
            '--json[format Yarn log messages as lines of JSON]' \
            '--level=[only print advisories with severity greater than or equal to]:level:(info low moderate high critical)' \
            '--groups=[only audit dependencies from listed groups]:groups:->groups_args'
        ;;

        cache)
          _arguments \
            '1: :(list dir clean)' \
            '*:: :->cache_args'
        ;;

        check)
          _arguments \
            '--integrity[Verifies that versions and hashed values of the package contents in package.json]' \
            '--verify-tree[Recursively verifies that the dependencies in package.json are present in node_modules]'
        ;;

        config)
          _arguments \
            '1: :(set get delete list)' \
            '*:: :->config_args'
        ;;

        global)
          _arguments \
            '--prefix=[bin prefix to use to install binaries]' \
            '1: :_yarn_global_commands' \
            '*:: :->command_args'
        ;;

        info)
          _arguments \
            '1:package:' \
            '2:field'
        ;;

        init)
          _arguments \
            '(-y --yes)'{-y,--yes}'[install packages in devDependencies]'
        ;;

        licenses)
          _arguments \
            '1: :(ls generate-disclaimer)' \
        ;;

        link|unlink|outdated)
          _arguments \
            '1:package' \
        ;;

        list)
          _arguments \
            '--depth=[Limit the depth of the shown dependencies]:depth' \
            '--pattern=[filter the list of dependencies by the pattern]'
        ;;

        owner)
          _arguments \
            '1: :(list add rm)' \
            '*:: :->owner_args'
        ;;

        pack)
          _arguments \
            '(-f --filename)'{-f,--filename}':filename:_files'
        ;;

        publish)
          _arguments \
            '--new-version:version:' \
            '--message:message:' \
            '--no-git-tag-version' \
            '--access:access:' \
            '--tag:tag:' \
            '1: :_files'
        ;;

        policies)
          _arguments \
            '1: :(set-version)'
        ;;

        remove|upgrade)
          _arguments \
            '*:package:'
        ;;

        run)
          if [[ -n $opt_args[--cwd] ]]; then
            _yarn_run_cwd=$opt_args[--cwd]
          else
            _yarn_run_cwd=''
          fi
          _arguments \
            '1: :_yarn_scripts' \
            '*:: :_default'
        ;;

        tag)
          _arguments \
            '1: :(lists add rm)' \
            '*:: :->tag_args'
        ;;

        team)
          _arguments \
            '1: :(create destroy add rm list)' \
            '*:: :->team_args'
        ;;

        upgrade-interactive)
          _arguments \
            '--latest[use the version tagged latest in the registry]'
        ;;

        version)
          _arguments \
            '--new-version[create a new version using an interactive session to prompt you]:version:' \
            '--major[creates a new version by incrementing the major version]' \
            '--minor[creates a new version by incrementing the minor version]' \
            '--patch[creates a new version by incrementing the patch version]' \
            '--premajor[creates a new prerelease version by incrementing the major version]' \
            '--preminor[creates a new prerelease version by incrementing the minor version]' \
            '--prepatch[creates a new prerelease version by incrementing the patch version]' \
            '--prerelease[increments the prerelease version number keeping the main version]' \
            '--no-git-tag-version[creates a new version without creating a git tag]' \
            '--no-commit-hooks[bypasses running commit hooks when committing the new version]'
        ;;

        why)
          _arguments \
            '1:query:_files'
        ;;

        workspace)
          _arguments \
            '1:workspace:_yarn_workspaces' \
            '*:: :_yarn_global_commands'
        ;;

        workspaces)
          _arguments \
            '--json[format Yarn log messages as lines of JSON]' \
            '1:commands:(info run)'
        ;;

        *)
          _default
        ;;
      esac
    ;;
  esac

  case $state in
    cache_args)
      if [[ $words[1] == "list" ]]; then
        _arguments \
          '--pattern=[print out every cached package that matches the pattern]:pattern:'
      fi
    ;;
    config_args)
      case $words[1] in
        get|delete)
          _arguments \
            '1:key:'
        ;;

        set)
          _arguments \
            '(-g --global)'{-g,--global} \
            '1:key:' \
            '2:value:'
        ;;
      esac
    ;;
    groups_args)
      local dependency_groups=(devDependencies dependencies optionalDependencies peerDependencies bundledDependencies)
      _values -s ',' 'groups' $dependency_groups
    ;;

    owner_args)
      case $words[1] in
        ls)
          _arguments \
            '1:package:'
        ;;

        add|rm)
          _arguments \
            '1:user:' \
            '2:package:'
        ;;
      esac
    ;;

    tag_args)
      case $words[1] in
        ls)
          _arguments \
            '1:package'
        ;;

        add|rm)
          _arguments \
            '1:package:' \
            '2:tag:'
        ;;
      esac
    ;;

    team_args)
      case $words[1] in
        create|destroy|ls)
          _arguments \
            '1:scope\:team:'
        ;;

        add|rm)
          _arguments \
            '1:scope\:team:' \
            '2:user:'
        ;;
      esac
    ;;
  esac
}

_yarn "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
