#!/usr/bin/env bash

#
# blackbox_removeadmin -- Remove an admin to the system
#  NOTE: Does not remove admin from the keyring.
#

# Example:
#    blackbox_removeadmin <EMAIL>
#

set -e
source "${0%/*}/_blackbox_common.sh"

fail_if_not_in_repo

KEYNAME="$1"
: "${KEYNAME:?ERROR: First argument must be a keyname (email address)}" ;

# Remove the email address from the BB_ADMINS file.
remove_line "$BB_ADMINS" "$KEYNAME"


# remove the admin key from the pubring
$GPG --no-permission-warning --homedir="$KEYRINGDIR" --batch --yes --delete-key "$KEYNAME" || true
pubring_path=$(get_pubring_path)
vcs_add "$pubring_path" "$KEYRINGDIR/trustdb.gpg" "$BB_ADMINS"


# Make a suggestion:
echo
echo
echo 'NEXT STEP: Check these into the repo.  Probably with a command like...'
echo $VCS_TYPE commit -m\'REMOVED ADMIN: $KEYNAME\' "$BLACKBOXDATA/$(basename ${pubring_path})" "$BLACKBOXDATA/trustdb.gpg" "$BLACKBOXDATA/$BB_ADMINS_FILE"
