#!/usr/bin/env zsh

# shameless copy from antigen

# Bulk add many bundles at one go. Empty lines and lines starting with a `#`
# are ignored. Everything else is given to `zgenom-load` as is, no
# quoting rules applied.
function zgenom-loadall() {
    local line

    grep '^[[:space:]]*[^[:space:]#]' | while read line; do
        # Using `eval` so that we can use the shell-style quoting in each line
        # piped to `antigen-bundles`.
        eval "zgenom-load $line"
    done

}

zgenom-loadall $@
