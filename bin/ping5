#!/usr/bin/env bash
#
# Copyright 2022, <PERSON> <<EMAIL>>

set -o pipefail
if [[ -n "$DEBUG" ]]; then
  set -x
fi

function debug() {
  if [[ -n "$DEBUG" ]]; then
        zsh_debug_echo "$@"
  fi
}

function echo-stderr() {
      zsh_debug_echo "$@" 1
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function has() {
  # Check if a command is in $PATH
  which "$@" > /dev/null 2>&1
}

if has grc; then
  exec grc --colour=auto ping -c 5 "$@"
else
  exec ping -c 5 "$@"
fi
