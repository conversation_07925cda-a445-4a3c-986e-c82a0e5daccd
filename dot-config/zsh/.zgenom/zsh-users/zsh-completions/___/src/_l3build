#compdef l3build
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for l3build (https://github.com/latex3/l3build/).
#  Modified from rejected https://github.com/latex3/l3build/pull/267
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * <PERSON> <<EMAIL>>
#
# ------------------------------------------------------------------------------

__l3build() {
  local targets=(
    'check:Run\ all\ automated\ tests'
    'clean:Clean\ out\ directory\ tree'
    'ctan:Create\ CTAN-ready\ archive'
    'doc:Typesets\ all\ documentation\ files'
    'install:Installs\ files\ into\ the\ local\ texmf\ tree'
    'manifest:Creates\ a\ manifest\ file'
    'save:Saves\ test\ validation\ log'
    'tag:Updates\ release\ tags\ in\ files'
    'uninstall:Uninstalls\ files\ from\ the\ local\ texmf\ tree'
    'unpack:Unpacks\ the\ source\ files\ into\ the\ build\ tree'
    'upload:Send\ archive\ to\ CTAN\ for\ public\ release'
  )
  local options=(
    {--config,-c}'[Sets the config(s) used for running tests]':lua_file:'_files -g "*.lua"'
    --date'[Sets the date to insert into sources]':
    --debug'[Runs target in debug mode]'
    --dirty'[Skip cleaning up the test area]'
    --dry-run'[Dry run for install or upload]'
    --email'[Email address of CTAN uploader]':
    {--engine,-e}'[Sets the engine(s) to use for running test]':engine:'(pdftex xetex luatex ptex uptex)'
    --epoch'[Sets the epoch for tests and typesetting]':
    {--file,-F}'[Take the upload announcement from the given file]':file:_files
    --first'[Name of first test to run]':
    {--force,-f}'[Force tests to run if engine is not set up]'
    --full'[Install all files]'
    {--halt-on-error,-H}'[Stops running tests after the first failure]'
    '(- : *)'{--help,-h}'[Print this message and exit]'
    --last'[Name of last test to run]':
    {--message,-m}'[Text for upload announcement message]':
    {--quiet,-q}'[Suppresses TeX output when unpacking]'
    --rerun'[Skip setup\: simply rerun tests]'
    --show-log-on-error'[Show the full log of the failure with '\''halt-on-error'\'']'
    {--show-saves,-S}'[Show the invocation to update failing .tlg files]'
    --shuffle'[Shuffle order of tests]'
    --texmfhome'[Location of user texmf tree]':
    '(- : *)'--version'[Print version information and exit]'
  )
  _arguments -s -S $options "1:target:(($targets))"
}

__l3build

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
