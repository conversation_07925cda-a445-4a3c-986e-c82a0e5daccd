name: build

on: 
  pull_request:
    branches: [ master ]
  push:
    branches: [ master ]

jobs:

  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repo
      uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: ^1.15
    - name: Build binaries
      run: go run build/build.go
    - name: Run unit tests
      run: go test ./...
    - name: Run integration tests
      working-directory: integrationTest
      run: umask 0027 ; rm -rf /tmp/bbhome-* && go test -long -nocleanup
