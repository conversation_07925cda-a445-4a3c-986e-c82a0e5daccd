#compdef shellcheck
# ------------------------------------------------------------------------------
# Copyright (c) 2021 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for shellcheck v0.10.0 (https://github.com/koalaman/shellcheck)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Khue Nguyen (https://github.com/Z5483)
#
# ------------------------------------------------------------------------------

_arguments \
  {-a,--check-sourced}'[include warnings from sourced file]' \
  {-C,--color=}'[specify color]:color:(auto always never)' \
  {-i,--include=}'[consider only given types of warnings]:error code' \
  {-e,--exclude=}'[exclude given types of warnings]:error code' \
  '--extended-analysis=[perform dataflow analysis]:bool:(true false)' \
  {-f,--format=}'[specify output format]:format:(checkstyle diff gcc json json1 quiet tty)' \
  '--list-optional[list checks disabled by default]' \
  "--norc[don't look for .shellcheckrc files]" \
  '--rcfile=[specify configuration file over searching for one]:rcfile:_files' \
  {-o,--enable=}"[give list of optional checks to enable (or 'all')]:error code" \
  {-P,--source-path=}'[specify path when looking for sourced files]:_files -/' \
  {-s,--shell=}'[specify dialect]:dialect:(sh bash dash ksh busybox)' \
  {-S,--severity=}'[specify minimum severity of errors to consider]:severity:(error warning info style)' \
  {-V,--version}'[print version information]' \
  {-W,--wiki-link-count=}'[specify number of wiki links to show, when applicable]:number' \
  {-x,--external-sources}'[allow outside sources]' \
  '(- *)--help[show this usage summary and exit]' \
  '*: :_files'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
