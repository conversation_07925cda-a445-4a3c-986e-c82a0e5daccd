#!/opt/homebrew/bin/zsh
# ==============================================================================
# ZSH Startup Performance Profiler
# ==============================================================================
# Purpose: Comprehensive startup time measurement and analysis tool for ZSH
#          configuration optimization. Provides detailed statistics, component
#          timing, and performance regression detection.
#
# Author: ZSH Configuration Management System
# Created: 2025-08-21
# Version: 1.0
# Usage: ./zsh-profile-startup [options]
# Dependencies: /opt/homebrew/bin/zsh, time command, bc (optional)
# ==============================================================================

# ------------------------------------------------------------------------------
# 0. CONFIGURATION AND SETUP
# ------------------------------------------------------------------------------

# Script configuration
SCRIPT_VERSION="1.0.0"
ZDOTDIR="${ZDOTDIR:-$HOME/.config/zsh}"
ZSH_BINARY="${ZSH_BINARY:-/opt/homebrew/bin/zsh}"

# Performance testing configuration
DEFAULT_ITERATIONS=10
DEFAULT_WARMUP_RUNS=2
PERFORMANCE_LOG_DIR="$ZDOTDIR/logs/performance"
PERFORMANCE_LOG_FILE="$PERFORMANCE_LOG_DIR/startup-profile-$(date -u '+%Y-%m-%d_%H-%M-%S').log"

# Create performance log directory
mkdir -p "$PERFORMANCE_LOG_DIR" 2>/dev/null || true

# ------------------------------------------------------------------------------
# 1. UTILITY FUNCTIONS
# ------------------------------------------------------------------------------

# 1.1. Logging and output functions
log_info() {
    local message="$1"
    local timestamp=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
    echo "[$timestamp] [INFO] $message" | tee -a "$PERFORMANCE_LOG_FILE"
}

log_result() {
    local message="$1"
    echo "$message" | tee -a "$PERFORMANCE_LOG_FILE"
}

log_debug() {
    local message="$1"
    if [[ "$DEBUG" == "true" ]]; then
        local timestamp=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
        echo "[$timestamp] [DEBUG] $message" >> "$PERFORMANCE_LOG_FILE"
    fi
}

# 1.2. Mathematical functions
calculate_average() {
    local -a times=("$@")
    local sum=0
    local count=${#times[@]}

    for time in "${times[@]}"; do
        if command -v bc >/dev/null 2>&1; then
            sum=$(echo "$sum + $time" | bc -l)
        else
            # Fallback for systems without bc
            sum=$(awk "BEGIN {print $sum + $time}")
        fi
    done

    if command -v bc >/dev/null 2>&1; then
        echo "scale=3; $sum / $count" | bc -l
    else
        awk "BEGIN {printf \"%.3f\", $sum / $count}"
    fi
}

calculate_median() {
    local -a times=("$@")
    local count=${#times[@]}

    # Sort the array
    local -a sorted_times=($(printf '%s\n' "${times[@]}" | sort -n))

    if (( count % 2 == 1 )); then
        # Odd number of elements
        local middle=$((count / 2))
        echo "${sorted_times[$middle]}"
    else
        # Even number of elements
        local middle1=$((count / 2 - 1))
        local middle2=$((count / 2))
        if command -v bc >/dev/null 2>&1; then
            echo "scale=3; (${sorted_times[$middle1]} + ${sorted_times[$middle2]}) / 2" | bc -l
        else
            awk "BEGIN {printf \"%.3f\", (${sorted_times[$middle1]} + ${sorted_times[$middle2]}) / 2}"
        fi
    fi
}

calculate_std_deviation() {
    local -a times=("$@")
    local average="$1"
    shift
    local -a values=("$@")

    local sum_squares=0
    local count=${#values[@]}

    for value in "${values[@]}"; do
        local diff
        if command -v bc >/dev/null 2>&1; then
            diff=$(echo "$value - $average" | bc -l)
            sum_squares=$(echo "$sum_squares + ($diff * $diff)" | bc -l)
        else
            diff=$(awk "BEGIN {print $value - $average}")
            sum_squares=$(awk "BEGIN {print $sum_squares + ($diff * $diff)}")
        fi
    done

    if command -v bc >/dev/null 2>&1; then
        echo "scale=3; sqrt($sum_squares / $count)" | bc -l
    else
        awk "BEGIN {printf \"%.3f\", sqrt($sum_squares / $count)}"
    fi
}

# ------------------------------------------------------------------------------
# 2. PERFORMANCE MEASUREMENT FUNCTIONS
# ------------------------------------------------------------------------------

# 2.1. Single startup time measurement
measure_single_startup() {
    local config_file="${1:-$ZDOTDIR/.zshrc}"
    local mode="${2:-interactive}"

    log_debug "Measuring single startup with config: $config_file, mode: $mode"

    local time_output
    if [[ "$mode" == "interactive" ]]; then
        time_output=$(env ZDOTDIR="$ZDOTDIR" time "$ZSH_BINARY" -i -c 'exit' 2>&1)
    else
        time_output=$(env ZDOTDIR="$ZDOTDIR" time "$ZSH_BINARY" -c 'exit' 2>&1)
    fi

    # Extract real time from time output
    # Format: "        0.06 real         0.03 user         0.02 sys"
    local real_time=$(echo "$time_output" | grep "real" | awk '{print $1}')

    # If still no time found, return error
    if [[ -z "$real_time" ]]; then
        echo "ERROR: Could not parse time from: $time_output" >&2
        echo "0"
        return 1
    fi

    # Convert to milliseconds
    if command -v bc >/dev/null 2>&1; then
        echo "scale=1; $real_time * 1000" | bc -l
    else
        awk "BEGIN {printf \"%.1f\", $real_time * 1000}"
    fi
}

# 2.2. Multiple startup measurements with statistics
measure_startup_performance() {
    local iterations="${1:-$DEFAULT_ITERATIONS}"
    local warmup_runs="${2:-$DEFAULT_WARMUP_RUNS}"
    local config_file="${3:-$ZDOTDIR/.zshrc}"
    local mode="${4:-interactive}"

    log_info "Starting performance measurement: $iterations iterations, $warmup_runs warmup runs"
    log_info "Configuration: $config_file"
    log_info "Mode: $mode"

    # Warmup runs
    log_info "Performing $warmup_runs warmup runs..."
    for ((i=1; i<=warmup_runs; i++)); do
        measure_single_startup "$config_file" "$mode" >/dev/null
        log_debug "Warmup run $i completed"
    done

    # Actual measurements
    log_info "Performing $iterations measurement runs..."
    local -a startup_times=()

    for ((i=1; i<=iterations; i++)); do
        local startup_time=$(measure_single_startup "$config_file" "$mode")
        startup_times+=("$startup_time")
        log_debug "Run $i: ${startup_time}ms"

        # Progress indicator
        if (( i % 5 == 0 )) || (( i == iterations )); then
            log_info "Completed $i/$iterations runs"
        fi
    done

    # Calculate statistics
    local average=$(calculate_average "${startup_times[@]}")
    local median=$(calculate_median "${startup_times[@]}")
    local std_dev=$(calculate_std_deviation "$average" "${startup_times[@]}")

    # Find min and max
    local min_time=$(printf '%s\n' "${startup_times[@]}" | sort -n | head -n1)
    local max_time=$(printf '%s\n' "${startup_times[@]}" | sort -n | tail -n1)

    # Output results
    log_result ""
    log_result "========================================================"
    log_result "ZSH Startup Performance Results"
    log_result "========================================================"
    log_result "Configuration: $config_file"
    log_result "Mode: $mode"
    log_result "Iterations: $iterations (after $warmup_runs warmup runs)"
    log_result "ZSH Binary: $ZSH_BINARY"
    log_result "Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
    log_result ""
    log_result "Performance Statistics:"
    log_result "  Average:     ${average}ms"
    log_result "  Median:      ${median}ms"
    log_result "  Minimum:     ${min_time}ms"
    log_result "  Maximum:     ${max_time}ms"
    log_result "  Std Dev:     ${std_dev}ms"
    log_result ""

    # Performance assessment
    local avg_seconds
    if command -v bc >/dev/null 2>&1; then
        avg_seconds=$(echo "scale=3; $average / 1000" | bc -l)
    else
        avg_seconds=$(awk "BEGIN {printf \"%.3f\", $average / 1000}")
    fi

    log_result "Performance Assessment:"
    if (( $(echo "$avg_seconds < 0.1" | bc -l 2>/dev/null || echo "0") )); then
        log_result "  🚀 EXCELLENT: Ultra-fast startup (<100ms)"
    elif (( $(echo "$avg_seconds < 0.3" | bc -l 2>/dev/null || echo "0") )); then
        log_result "  ✅ VERY GOOD: Fast startup (<300ms)"
    elif (( $(echo "$avg_seconds < 1.0" | bc -l 2>/dev/null || echo "0") )); then
        log_result "  ⚠️  ACCEPTABLE: Moderate startup (<1s)"
    else
        log_result "  ❌ SLOW: Startup time needs optimization (>1s)"
    fi

    log_result ""
    log_result "Raw measurements (ms): ${startup_times[*]}"
    log_result "========================================================"

    # Return average for comparison purposes
    echo "$average"
}

# ------------------------------------------------------------------------------
# 3. COMPARISON AND ANALYSIS FUNCTIONS
# ------------------------------------------------------------------------------

# 3.1. Compare two configurations
compare_configurations() {
    local config1="$1"
    local config2="$2"
    local iterations="${3:-$DEFAULT_ITERATIONS}"

    log_info "Comparing configurations:"
    log_info "  Config 1: $config1"
    log_info "  Config 2: $config2"

    log_result ""
    log_result "========================================================"
    log_result "Configuration Comparison"
    log_result "========================================================"

    # Measure first configuration
    log_result ""
    log_result "--- Configuration 1: $(basename "$config1") ---"
    local avg1=$(measure_startup_performance "$iterations" "$DEFAULT_WARMUP_RUNS" "$config1" "interactive")

    # Measure second configuration
    log_result ""
    log_result "--- Configuration 2: $(basename "$config2") ---"
    local avg2=$(measure_startup_performance "$iterations" "$DEFAULT_WARMUP_RUNS" "$config2" "interactive")

    # Calculate improvement
    local improvement
    if command -v bc >/dev/null 2>&1; then
        improvement=$(echo "scale=2; (($avg1 - $avg2) / $avg1) * 100" | bc -l)
    else
        improvement=$(awk "BEGIN {printf \"%.2f\", (($avg1 - $avg2) / $avg1) * 100}")
    fi

    log_result ""
    log_result "========================================================"
    log_result "Comparison Summary"
    log_result "========================================================"
    log_result "Config 1 Average: ${avg1}ms"
    log_result "Config 2 Average: ${avg2}ms"

    if (( $(echo "$improvement > 0" | bc -l 2>/dev/null || echo "0") )); then
        log_result "Improvement: ${improvement}% faster (Config 2 is better)"
        log_result "Time saved: $(echo "scale=3; $avg1 - $avg2" | bc -l 2>/dev/null || awk "BEGIN {printf \"%.3f\", $avg1 - $avg2}")ms per startup"
    else
        local regression=$(echo "scale=2; $improvement * -1" | bc -l 2>/dev/null || awk "BEGIN {printf \"%.2f\", $improvement * -1}")
        log_result "Regression: ${regression}% slower (Config 1 is better)"
        log_result "Time added: $(echo "scale=3; $avg2 - $avg1" | bc -l 2>/dev/null || awk "BEGIN {printf \"%.3f\", $avg2 - $avg1}")ms per startup"
    fi
    log_result "========================================================"
}

# ------------------------------------------------------------------------------
# 4. MAIN EXECUTION FUNCTIONS
# ------------------------------------------------------------------------------

# 4.1. Display help
show_help() {
    cat << EOF
ZSH Startup Performance Profiler v$SCRIPT_VERSION

USAGE:
    $0 [OPTIONS]

OPTIONS:
    -h, --help              Show this help message
    -i, --iterations N      Number of measurement iterations (default: $DEFAULT_ITERATIONS)
    -w, --warmup N          Number of warmup runs (default: $DEFAULT_WARMUP_RUNS)
    -c, --config FILE       ZSH configuration file to test (default: \$ZDOTDIR/.zshrc)
    -m, --mode MODE         Test mode: interactive or non-interactive (default: interactive)
    -C, --compare FILE      Compare current config with another config file
    -d, --debug             Enable debug logging
    -v, --version           Show version information

EXAMPLES:
    $0                                          # Basic performance test
    $0 -i 20 -w 3                             # 20 iterations with 3 warmup runs
    $0 -c ~/.zshrc.fast                        # Test specific configuration
    $0 -C ~/.zshrc.backup                      # Compare current with backup
    $0 -m non-interactive                      # Test non-interactive startup

OUTPUT:
    Results are displayed on screen and logged to:
    $PERFORMANCE_LOG_FILE

EOF
}

# 4.2. Main function
main() {
    local iterations="$DEFAULT_ITERATIONS"
    local warmup_runs="$DEFAULT_WARMUP_RUNS"
    local config_file="$ZDOTDIR/.zshrc"
    local mode="interactive"
    local compare_config=""
    local debug="false"

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -i|--iterations)
                iterations="$2"
                shift 2
                ;;
            -w|--warmup)
                warmup_runs="$2"
                shift 2
                ;;
            -c|--config)
                config_file="$2"
                shift 2
                ;;
            -m|--mode)
                mode="$2"
                shift 2
                ;;
            -C|--compare)
                compare_config="$2"
                shift 2
                ;;
            -d|--debug)
                debug="true"
                shift
                ;;
            -v|--version)
                echo "ZSH Startup Performance Profiler v$SCRIPT_VERSION"
                exit 0
                ;;
            *)
                echo "Unknown option: $1" >&2
                echo "Use -h or --help for usage information" >&2
                exit 1
                ;;
        esac
    done

    # Set debug mode
    DEBUG="$debug"

    # Validate inputs
    if [[ ! -f "$config_file" ]]; then
        echo "ERROR: Configuration file not found: $config_file" >&2
        exit 1
    fi

    if [[ ! -x "$ZSH_BINARY" ]]; then
        echo "ERROR: ZSH binary not found or not executable: $ZSH_BINARY" >&2
        exit 1
    fi

    # Start profiling
    log_info "ZSH Startup Performance Profiler v$SCRIPT_VERSION"
    log_info "Log file: $PERFORMANCE_LOG_FILE"

    if [[ -n "$compare_config" ]]; then
        if [[ ! -f "$compare_config" ]]; then
            echo "ERROR: Comparison configuration file not found: $compare_config" >&2
            exit 1
        fi
        compare_configurations "$config_file" "$compare_config" "$iterations"
    else
        measure_startup_performance "$iterations" "$warmup_runs" "$config_file" "$mode"
    fi

    log_info "Performance profiling completed"
    log_info "Results logged to: $PERFORMANCE_LOG_FILE"
}

# ------------------------------------------------------------------------------
# 5. SCRIPT EXECUTION
# ------------------------------------------------------------------------------

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]] || [[ "${(%):-%N}" == *"zsh-profile-startup"* ]]; then
    main "$@"
fi

# ==============================================================================
# END: ZSH Startup Performance Profiler
# ==============================================================================
