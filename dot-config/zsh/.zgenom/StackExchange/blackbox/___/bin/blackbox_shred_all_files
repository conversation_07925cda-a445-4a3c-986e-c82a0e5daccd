#!/usr/bin/env bash

#
# blackbox_shred_all_files -- shred all decrypted versions of encrypted files
#
# Shred: To securely delete a file.
#
# Typical uses:
#   After running blackbox_edit_start, deciding not to edit the file.
#   A developer that wants to securely clean up a workspace before deleting it.
#   An automated process that doesn't want to leave
#       plaintext (unencrypted) files laying around.
#
# NOTE: The output lists files that were decrypted and are being
# shredded.  For example, if you have many encrypted files but none
# have been decrypted for editing, you will see an empty list.

set -e
source "${0%/*}/_blackbox_common.sh"

change_to_vcs_root

echo '========== FILES BEING SHREDDED:'

exported_internal_shred_file() {
  source "$1/_blackbox_common.sh"
  #unencrypted_file=$(get_unencrypted_filename "$2")
  unencrypted_file="$2"
  if [[ -f "$unencrypted_file" ]]; then
    echo "    SHRED: $unencrypted_file"
    shred_file "$unencrypted_file"
  else
    echo "NOT FOUND: $unencrypted_file"
  fi
}

export -f exported_internal_shred_file

DEREFERENCED_BIN_DIR="${0%/*}"
MAX_PARALLEL_SHRED=10

bash_args=
if bash --help | grep import-functions >/dev/null 2>/dev/null; then
  bash_args=--import-functions
fi

export IFS=
tr '\n' '\0' <"$BB_FILES" | xargs -0 -I{} -P $MAX_PARALLEL_SHRED bash $bash_args -c "exported_internal_shred_file $DEREFERENCED_BIN_DIR \"{}\"" $DEREFERENCED_BIN_DIR/fake

echo '========== DONE.'
