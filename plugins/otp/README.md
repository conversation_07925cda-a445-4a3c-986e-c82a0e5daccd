# otp plugin

This plugin allows you to create one-time passwords using [`oathtool`](https://www.nongnu.org/oath-toolkit/man-oathtool.html),
able to replace MFA devices. The oathtool key is kept in a GPG-encrypted file so the codes
can only be generated by a user able to decrypt it.

To use it, add `otp` to the plugins array in your zshrc file:
```zsh
plugins=(... otp)
```

Provided aliases:

- `otp_add_device`: creates a new encrypted storage for an oathtool key and stores it
  on the disk. For encrypting the key, it will ask for a GPG user ID (your GPG key's
  email address). Then the OTP key needs to be pasted, followed by a CTRL+D character
  inserted on an empty line.

- `ot`: generates a MFA code based on the given key and copies it to the clipboard
  (on Linux it relies on xsel, on MacOS X it uses pbcopy instead).

The plugin uses `$HOME/.otp` to store its internal files.
