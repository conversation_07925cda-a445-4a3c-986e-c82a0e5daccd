#compdef emulator
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for emulator (Android Emulator) 12.0
#  (https://developer.android.com/studio/run/emulator-command).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * <PERSON> <<EMAIL>>
#
# ------------------------------------------------------------------------------


typeset -A opt_args
local context state line curcontext="$curcontext"

_list_avds() {
  local -a _avds=($HOME/.android/avd/*.ini(N.:t:r))
  echo "${_avds[@]}"
}

# TODO All image options are contextual to -sysdir value
# TODO All skin options are contextual to -skindir value
# TODO snapshot options are mutually exclusive
# TODO Use '-snapshot-list' output for snapshot names
# TODO -logcat: use completer from _adb
# TODO Complete options with device values
# TODO Complete -prop
_arguments \
  '(- : *)-version[display emulator version number]' \
  '(- : *)-help[display help information]' \
  '(- : *)-help-disk-images[about disk images]' \
  '(- : *)-help-keys[supported key bindings]' \
  '(- : *)-help-debug-tags[debug tags for -debug <tags>]' \
  '(- : *)-help-char-devices[character <device> specification]' \
  '(- : *)-help-environment[environment variables]' \
  '(- : *)-help-keyset-file[key bindings configuration file]' \
  '(- : *)-help-virtual-device[virtual device management]' \
  '(- : *)-help-sdk-images[about disk images when using the SDK]' \
  '(- : *)-help-build-images[about disk images when building Android]' \
  '(- : *)-help-all[prints all help content]' \
  '(- : *)-help-'{version,list-avds,sysdir,system,writable-system,image,datadir,kernel,ramdisk,initdata,data,partition-size,cache,no-cache,nocache,sdcard,snapstorage,no-snapstorage,snapshot,no-snapshot,no-snapshot-save,no-snapshot-load,snapshot-list,no-snapshot-update-time,wipe-data,avd,skindir,skin,noskin,no-skin,memory,cores,accel,no-accel,netspeed,netdelay,netfast,trace,show-kernel,shell,no-jni,nojni,logcat,noaudio,no-audio,audio,raw-keys,radio,port,ports,onion,onion-alpha,onion-rotation,scale,dpi-device,http-proxy,timezone,dns-server,cpu-delay,no-boot-anim,no-window,report-console,gps,keyset,shell-serial,tcpdump,bootchart,charmap,prop,shared-net-id,nand-limits,memcheck,qemu,verbose}'[print option-specific help]' \
  '-list-avds[list available AVDs]' \
  '-sysdir[search for system disk images in the directory]: :_files -/' \
  '(-system -image)'{-system,-image}'[read initial system image from the file]: :_files -g "*.img"' \
  '-writable-system[make system image writable after '\''adb remount'\'']' \
  '-datadir[write user data into the directory]: :_files -/' \
  '-kernel[use specific emulated kernel]: :_files' \
  '-ramdisk[ramdisk image (default <system>/ramdisk.img]: :_files -g "*.img"' \
  '-initdata[same as '\''-init-data <file>'\'']: :_files' \
  '-data[data image (default <datadir>/userdata-qemu.img]: :_files -g "*.img"' \
  '-partition-size[system/data partition size]:size (in MBs)' \
  '(-no-cache -nocache)-cache[cache partition image (default is temporary file)]: :_files -g "*.img"' \
  '(-cache -no-cache -nocache)'{-no-cache,-nocache}'[disable the cache partition]' \
  '-sdcard[SD card image (default <system>/sdcard.img]: :_files -g "*.img"' \
  '(-no-snapstorage)-snapstorage[file that contains all state snapshots (default <datadir>/snapshots.img)]: :_files -g "*.img"' \
  '(-snapstorage)-no-snapstorage[do not mount a snapshot storage file (this disables all snapshot functionality)]' \
  '-snapshot[name of snapshot within storage file for auto-start and auto-save (default '\''default-boot'\'')]:snapshot name' \
  '-no-snapshot[perform a full boot and do not do not auto-save, but qemu vmload and vmsave operate on snapstorage]' \
  '-no-snapshot-save[do not auto-save to snapshot on exit: abandon changed state]' \
  '-no-snapshot-load[do not auto-start from snapshot: perform a full boot]' \
  '-snapshot-list[show a list of available snapshots]' \
  '-no-snapshot-update-time[do not do try to correct snapshot time on restore]' \
  '-wipe-data[reset the user data image (copy it from initdata)]' \
  '-avd[use a specific android virtual device]:android virtual device name:($(_list_avds))' \
  '-skindir[search skins in <dir> (default <system>/skins)]: :_files -/' \
  '-skin[select a given skin]' \
  '(-noskin -no-skin)'{-noskin,-no-skin}'[don'\''t use any emulator skin]' \
  '-memory[physical RAM size in MBs]:size (in MBs)' \
  '-cores[Set number of CPU cores to emulator]:number' \
  '(-no-accel)-accel[Configure emulation acceleration]:mode' \
  '(-accel)-no-accel[Same as '\''-accel off'\'']' \
  '-netspeed[maximum network download/upload speeds]:speed' \
  '-netdelay[network latency emulation]:delay' \
  '-netfast[disable network shaping]' \
  '-trace[enable code profiling (F9 to start)]:trace name' \
  '-show-kernel[display kernel messages]' \
  '-shell[enable root shell on current terminal]' \
  {-no-jni,-nojni}'[disable JNI checks in the Dalvik runtime]' \
  '-logcat[enable logcat output with given tags]:logcat tags' \
  '(-audio -noaudio -no-audio)'{-noaudio,-no-audio}'[disable audio support]' \
  '(-noaudio -no-audio)-audio[use specific audio backend]:audio backend' \
  '-raw-keys[disable Unicode keyboard reverse-mapping]' \
  '-radio[redirect radio modem interface to character device]:device' \
  '-port[TCP port that will be used for the console]:port number' \
  '-ports[TCP ports used for the console and adb bridge]:console port,adb port' \
  '-onion[use overlay PNG image over screen]: :_files -g "*.(png|PNG)"' \
  '-onion-alpha[specify onion-skin translucency]:percentage' \
  '-onion-rotation[specify onion-skin rotation]:rotation:((1 2 3 4))' \
  '-scale[scale emulator window]:scale' \
  '-dpi-device[specify device'\''s resolution in dpi (default 165)]:dpi' \
  '-http-proxy[make TCP connections through a HTTP/HTTPS proxy]:proxy' \
  '-timezone[use this timezone instead of the host'\''s default]:timezone' \
  '-dns-server[use this DNS server(s) in the emulated system]:DNS servers' \
  '-cpu-delay[throttle CPU emulation]:CPU delay' \
  '-no-boot-anim[disable animation for faster boot]' \
  '-no-window[disable graphical window display]' \
  '-report-console[report console port to remote socket]: :_socket' \
  '-gps[redirect NMEA GPS to character device]:device' \
  '-keyset[specify keyset file name]: :_files' \
  '-shell-serial[specific character device for root shell]:device' \
  '-tcpdump[capture network packets to file]: :_files' \
  '-bootchart[enable bootcharting]:timeout' \
  '-charmap[use specific key character map]: :_files' \
  '*-prop[set system property on boot]:name=value' \
  '-shared-net-id[join the shared network, using IP address 10.1.2.<number>]:number' \
  '-nand-limits[enforce NAND/Flash read/write thresholds]:limits' \
  '-memcheck[enable memory access checking]:flags' \
  '-qemu[pass arguments to qemu]:arguments' \
  '-verbose[same as '\''-debug-init'\'']' \
  '*'{-debug,-debug-,-debug-no-}'[enable/disable specific debug messages]:tag' \
  '1: :->cmds' \
  '*:: :->args' && ret=0

case $state in
  cmds)
    local -a _avds=($(_list_avds))
    for ((i=1; i<=${#_avds[@]}; i++)); do
      _avds[i]="@${_avds[i]}"
    done
    _values 'avds' "${_avds[@]}"
    ;;
esac

return ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
