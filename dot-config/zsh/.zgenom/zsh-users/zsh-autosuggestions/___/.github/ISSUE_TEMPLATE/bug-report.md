---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''

---

### Describe the bug
<!-- A clear and concise description of what the bug is. -->

### To Reproduce
Steps to reproduce the behavior:

<!-- If you are not able to reproduce it by running `zsh -df` and sourcing the plugin manually, it means there that the issue is caused by something in your local config file(s). Temporarily comment out or remove sections of your config and restart `zsh` until you narrow down exactly what is causing the issue. -->

```sh
% zsh -df
% source path/to/zsh-autosuggestions.zsh
% ... # what do you do to reproduce?
```

### Expected behavior
<!-- A clear and concise description of what you expected to happen. -->

### Screenshots
<!-- If applicable, add screenshots to help explain your problem. -->

### Desktop
 - OS + distribution: <!-- e.g. Arch Linux 2019.07.01 -->
 - Zsh version: <!-- `echo $ZSH_VERSION` -->
 - Plugin version: <!-- or git commit hash if installed via git -->

### Additional context
<!-- Add any other context about the problem here. -->
