#compdef do-release-upgrade
# ------------------------------------------------------------------------------
# Copyright (c) 2023 Github zsh-users - https://github.com/zsh-users
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for do-release-upgrade 23.10.10
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

_arguments \
  '(- *)'{-h,--help}'[Show this help message and exit]' \
  '(- *)'{-v,--version}'[Show version and exit]' \
  '(-d --devel-release)'{-d,--devel-release}'[Upgrade to the development release]' \
  '--data-dir=[Directory that contains the data files]:dir:_files -/' \
  '(-p --proposed)'{-p,--proposed}'[Try upgrading to the latest release using the upgrade from \$distro-proposed]' \
  '(-m --mode)'{-m,--mode=}'[Run in a special upgrade mode]:mode:(desktop server)' \
  '(-f --frontend)'{-f,--frontend}'[Run the specified frontend]:frontend' \
  '(- *)--check-dist-upgrade-only[Check only if a new distribution release is available]' \
  '--allow-third-party[Try the upgrade with third party mirrors and repositories]' \
  '(-q --quiet)'{-q,--quiet}'[Quiet mode]' \
  \*{-e,--env=}'[A comma-separated list of environment variables]'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
