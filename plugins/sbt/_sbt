#compdef sbt
#autoload

local -a _sbt_commands
_sbt_commands=(
  'clean:delete files produced by the build'
  'compile:compile sources'
  'console:start the Scala REPL with project classes on the classpath'
  'consoleQuick:start the Scala REPL with project deps on the classpath'
  'consoleProject:start the Scala REPL w/sbt+build-def on the classpath'
  'dist:generate distribution artifacts'
  'dist\:clean:clean distribution artifacts'
  'doc:generate API documentation'
  'genIdea:generate Intellij Idea project files'
  'package:produce the main artifact, such as a binary jar'
  'packageDoc:produce a doc artifact, such as a jar containing API docs'
  'packageSrc:produce a source artifact, such as a jar containing sources'
  'publish:publish artifacts to a repository'
  'publishLocal:publish artifacts to the local repository'
  'publishM2:publish artifacts to the local Maven 2 repository'
  'run:run a main class'
  'runMain:run the main class selected by the first argument'
  'test:execute all tests'
  'testOnly:execute the tests provided as arguments'
  'testQuick:execute previously failed tests'
  'update:resolve and optionally retrieve dependencies'
)

local expl

_arguments \
  '(-help)-h[prints an help message]' \
  '(-h)-help[prints an help message]' \
  '(-verbose)-v[this runner is chattier]' \
  '(-v)-verbose[this runner is chattier]' \
  '(-debug)-d[set sbt log level to debug]' \
  '(-d)-debug[set sbt log level to debug]' \
  '-no-colors[disable ANSI color codes]' \
  '-sbt-create[start even if current dir contains no sbt project]' \
  '-sbt-dir[path to global settings/plugins dir (default: ~/.sbt)]' \
  '-sbt-boot[path to shared boot dir (default: ~/.sbt/boot)]' \
  '-ivy[path to local Ivy repository (default: ~/.ivy2)]' \
  '-mem[set memory options]' \
  '-no-share[use all local caches; no sharing]' \
  '-no-global[use global caches, but do not use global ~/.sbt dir]' \
  '-jvm-debug[turn on JVM debugging, open at the given port]' \
  '-batch[disable interactive mode]' \
  '-sbt-version[use the specified version of sbt]' \
  '-sbt-jar[use the specified jar as the sbt launcher]' \
  '(-sbt-snapshot)-sbt-rc[use an RC version of sbt]' \
  '(-sbt-rc)-sbt-snapshot[use a snapshot version of sbt]' \
  '-java-home[alternate JAVA_HOME]' \
  '*:: :->subcmds' && return 0

_describe -t commands "sbt subcommand" _sbt_commands
return
