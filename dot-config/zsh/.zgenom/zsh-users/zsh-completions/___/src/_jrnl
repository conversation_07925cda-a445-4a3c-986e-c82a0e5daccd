#compdef jrnl
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for jrnl a simple journal application for your command line. (https://maebert.github.io/jrnl/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Jindřich Pilař (https://github.com/JindrichPilar)
#
# ------------------------------------------------------------------------------


_jrnl() {

    _arguments -C \
      '(- 1 *)'-h"[Show help and exit]" \
      '(- 1 *)'-v"[Prints version information and exits]" \
      '(- 1 *)'-ls"[Displays accessible journals]" \
      '(- 1 *)'-d"[Execute in debug mode]" \
      '(- 1 *)'--tags"[Returns a list of all tags and number of occurrences]" \
      "--short[Show only titles or line containing the search]" \
      "-from[View entries after this date]:date:" \
      "-until[View entries before this date]:date:" \
      "-to[View entries before this date]:date:" \
      "-on[View entries on this date]:date:" \
      "-and[Filter by tags using AND (default: OR)]" \
      "-starred[Show only starred entries]" \
      "-n[Shows the last n entries matching the filter. And '-3' have the same effect.]":number: \
      "--export[Export your journal. TYPE can be json, markdown text.]:format:(json markdown text)" \
      "-o[Optionally specifies output file when using --export If OUTPUT is a directory, exports each entry in individual file instead.]:output file:" \
      "--encrypt[Encrypts your existing journal with a new pass]" \
      "--decrypt[Decrypts your journal and stores it in plain text]" \
      "--edit[Opens your editor to edit the selected entries.]" \
}

_jrnl

