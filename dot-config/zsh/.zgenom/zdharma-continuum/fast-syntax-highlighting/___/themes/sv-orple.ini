; https://www.syntaxenvy.com/0073437
;
; comment:
;   #363355 -> 61
; keyword:
;   #dda69f -> 181 (138)
; number, string:
;   #ca887e -> 174 (173)
; title, section, name, selector-id:
;   #b3afd9 -> 146 (146)
; attribute, variable, type:
;   #be85c0 -> 139 (140)
; symbol, link:
;   #d6a2d8 -> 182 (182)
; builtin, deletion:
;   #969c77 -> 108 (108)
; formula-bg:
;   #211f37 -> 16 (17)

[base]
default          = none
unknown-token    = 124
commandseparator = 146
redirection      = none
here-string-tri  = 138
here-string-text = bg:25
here-string-var  = 140,bg:25
exec-descriptor  = 140
comment          = 61
correct-subtle   = bg:55
incorrect-subtle = bg:52
subtle-bg        = bg:17
secondary        = clean
recursive-base   = 186

[command-point]
reserved-word  = 138
subcommand     = 182
alias          = 140
suffix-alias   = 140
global-alias   = bg:17
builtin        = 173
function       = 140
command        = 108
precommand     = 138
hashed-command = 140
single-sq-bracket = 173
double-sq-bracket = 173
double-paren   = 138

[paths]
path          = 182
pathseparator = 
path-to-dir   = 182,underline
globbing      = 138
globbing-ext  = 141

[brackets]
paired-bracket = bg:blue
bracket-level-1 = 173
bracket-level-2 = 177
bracket-level-3 = 220

[arguments]
single-hyphen-option   = 140
double-hyphen-option   = 140
back-quoted-argument   = none
single-quoted-argument = 173
double-quoted-argument = 173
dollar-quoted-argument = 173

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 146
; backslash or $... in "..." (i.e. variable in string)
back-or-dollar-double-quoted-argument = 140

[other]
variable             = none
assign               = none
assign-array-bracket = 182
history-expansion    = blue,bold

[math]
mathvar = 140
mathnum = 173
matherr = 124

[for-loop]
forvar = 140
fornum = 173
; operator
foroper = 147
; separator
forsep = 182

[case]
case-input       = 140
case-parentheses = 17
case-condition   = bg:25
