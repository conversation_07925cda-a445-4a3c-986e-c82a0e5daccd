#compdef uuidgen
# ------------------------------------------------------------------------------
# Copyright (c) 2024 Github zsh-users - https://github.com/zsh-users
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for uuidgen v15.0.2 (https://github.com/util-linux/util-linux)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

case $OSTYPE in
  (darwin*)
    _arguments \
      '-hdr[emit result in form suitable for copying into a header]'
    ;;
  (freebsd*)
    _arguments \
      '-1[instructs uuidgen to not generate them in batch, but one at a time]' \
      '-r[create random UUID version 4]' \
      '-c[create compact UUID without hyphen]' \
      '-n[number of identifiers generated]:num' \
      '-o[redirect output to filename instead of stdout]:file:_files'
    ;;
  (netbsd*)
    _arguments \
      '-1[instructs uuidgen to not generate them in batch, but one at a time]' \
      '-n[number of identifiers generated]:num' \
      '-s[output UUIDs as initialized C structures]'
    ;;
  (dragonfly*)
    _arguments \
      '-1[instructs uuidgen to not generate them in batch, but one at a time]' \
      '-n[number of identifiers generated]:num' \
      '-o[redirect output to filename instead of stdout]:file:_files'
    ;;
  (*)
    _arguments \
      '(- *)'{-h,--help}'[display this help]' \
      '(- *)'{-V,--version}'[display version]' \
      '(-r --random)'{-r,--random}'[generate random-based uuid]' \
      '(-t --time)'{-t,--time}'[generate time-based uuid]' \
      '(-n --namespace)'{-n,--namespace}'[generate hash-based uuid in this namespace]:namespace:(@dns @url @oid @x500)' \
      '(-N --name)'{-n,--name}'[generate hash-based uuid from this name]:name' \
      '(-C --count -s --sha1 -m --md5)'{-m,--md5}'[generate md5 hash]' \
      '(-C --count -s --sha1 -m --md5)'{-C,--count}'[generate more uuids in loop]:count' \
      '(-C --count -s --sha1 -m --md5)'{-s,--sha1}'[generate sha1 hash]' \
      '(-x --hex)'{-h,--hex}'[interpret name as hex string]'
    ;;
esac

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
