#compdef wemux
# ------------------------------------------------------------------------------
# Copyright (c) 2015 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for wemux (https://github.com/zolrath/wemux)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Akira Maeda <https://github.com/glidenote>
#
# ------------------------------------------------------------------------------

_wemux() {

  local -a host_commands client_commands multi_host_commands

  host_commands=(
    'start:Start the wemux server/attach to an existing wemux server.'
    'attach:Attach to an existing wemux server.'
    'stop:Kill the wemux server '\''wemux'\'', delete its socket.'
    'users:List all users currently attached to '\''wemux'\'''
    'kick:Disconnect an SSH user, remove their wemux server.'
    'config:Open the wemux configuration file in your $EDITOR.'
    'help:Display this screen.'
    'version:shows wemux version number'
  )

  client_commands=(
    'mirror:Attach to Host in read-only mode.'
    'pair:Attach to Host in pair mode, which allows editing.'
    'rogue:Attach to Host in rogue mode, which allows editing and switching to windows independently from the host.'
    'logout:Log out of the wemux rogueing session.'
    'users:List the currently attached wemux users.'
  )

  multi_host_commands=(
    'join:Join wemux server with supplied name.'
    'reset:Join default wemux server: wemux'
    'list:List all currently active wemux servers.'
  )

  if (( CURRENT == 2 )); then
    _describe -t host_commands 'HOST COMMANDS' host_commands
    _describe -t client_commands 'CLIENT COMMANDS' client_commands
    _describe -t multi_host_commands 'MULTI-HOST COMMANDS' multi_host_commands
  fi

  return 0
}

_wemux

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
