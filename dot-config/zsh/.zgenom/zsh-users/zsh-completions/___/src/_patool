#compdef patool
# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for patool (https://github.com/wummel/patool).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# * Sergei Eremenko (https://github.com/SmartFinn)
#
# ------------------------------------------------------------------------------

local state line ret=1

_arguments -C \
  '(-h --help)'{-h,--help}'[show help message and exit]' \
  '(--non-interactive)'--non-interactive'[do not query for user input]' \
  '(-v --verbose)'{-v,--verbose}'[verbose operation]' \
  '1:cmd:->cmds' \
  '*:arg:->args' && ret=0

case $state in
  (cmds)
    local -a cmds

    cmds=(
      'create:create an archive from given files'
      'diff:show differences between two archives'
      'extract:extract files from given archives'
      'formats:show all supported archive formats'
      'list:list files in archives'
      'repack:repackage archive to a different format'
      'recompress:recompress an archive to smaller size'
      'search:search in archive contents for given pattern'
      'test:test the given archives'
    )

    _describe -t commands 'patool commands' cmds && ret=0
    ;;
  (args)
    case $line[1] in
      (extract)
        _arguments \
          '--outdir[extract to the given output directory]:select directory:_files -/' \
          '*:files:_files' && ret=0
      ;;
      (formats)
        _message 'no more arguments' && ret=0
      ;;
      (search)
        _arguments \
          '2:search pattern:' \
          '*:files:_files' && ret=0
      ;;
      (*)
        _arguments \
          '*:files:_files' && ret=0
      ;;
    esac
    ;;
esac

return $ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
