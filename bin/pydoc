#!/usr/bin/env bash
#
# Originally from a Hangops slack thread

set -o pipefail
if [[ -n "$DEBUG" ]]; then
  # shellcheck disable=SC2086
  if [[ "$(echo $DEBUG | tr '[:upper:]' '[:lower:]')" == "verbose" ]]; then
    set -x
  fi
fi

function debug() {
  if [[ -n "$DEBUG" ]]; then
        zsh_debug_echo "$@"
  fi
}

function echo-stderr() {
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function has() {
  # Check if a command is in $PATH
  which "$@" > /dev/null 2>&1
}

function get-settings() {
  OPENER=${OPENER:-'open'}
}

function check-dependencies() {
  debug "Checking dependencies..."
  for c in 'xdg-open' 'open'
  do
    if has "$c"; then
      OPENER="$c"
    fi
  done
  # shellcheck disable=SC2041
  for p in "$OPENER"
  do
    if ! has "$p"; then
      fail "Can't find $p in your $PATH"
    else
      debug "- Found $p"
    fi
  done
}

function my-name() {
  basename "$0"
}

function usage() {
      zsh_debug_echo "Usage: $(my-name) THING"
  echo
      zsh_debug_echo "Looks up THING on docs.python.org and opens it in your default web browser"
}

function path-exists() {
  local file="${1}"
  [[ -s "${file}" ]] || fail "$1 is not valid"
  [[ -d "${file}" ]] && return
  [[ -f "${file}" ]] && return
  fail "$1 is not a directory or file"
}

get-settings
check-dependencies
exec $OPENER "https://docs.python.org/$(python --version | cut -d' ' -f2 | cut -d. -f1,2)/search.html?q=$*"
