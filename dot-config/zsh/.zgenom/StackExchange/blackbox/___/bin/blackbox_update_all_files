#!/usr/bin/env bash

#
# blackbox_update_all_files -- Decrypt then re-encrypt all files. Useful after keys are changed.
#

set -e
source "${0%/*}/_blackbox_common.sh"

gpg_agent_notice
disclose_admins
prepare_keychain

echo '========== ENCRYPTED FILES TO BE RE-ENCRYPTED:'
while IFS= read <&99 -r unencrypted_file; do
    echo "    $unencrypted_file.gpg"
done 99<"$BB_FILES"

echo '========== FILES IN THE WAY:'
need_warning=false
while IFS= read <&99 -r unencrypted_file; do
  unencrypted_file=$(get_unencrypted_filename "$unencrypted_file")
  encrypted_file=$(get_encrypted_filename "$unencrypted_file")
  if [[ -f "$unencrypted_file" ]]; then
    need_warning=true
    echo "    $unencrypted_file"
  fi
done 99<"$BB_FILES"
if "$need_warning" ; then
  echo
  echo 'WARNING: This will overwrite any unencrypted files laying about.'
  read -r -p 'Press CTRL-C now to stop. ENTER to continue: '
else
  echo 'All OK.'
fi

echo '========== RE-ENCRYPTING FILES:'
while IFS= read <&99 -r unencrypted_file; do
  unencrypted_file=$(get_unencrypted_filename "$unencrypted_file")
  encrypted_file=$(get_encrypted_filename "$unencrypted_file")
  echo ========== PROCESSING '"'$unencrypted_file'"'
  fail_if_not_on_cryptlist "$unencrypted_file"
  decrypt_file_overwrite "$encrypted_file" "$unencrypted_file"
  encrypt_file "$unencrypted_file" "$encrypted_file"
  shred_file "$unencrypted_file"
done 99<"$BB_FILES"

fail_if_keychain_has_secrets

echo '========== COMMITING TO VCS:'
while IFS= read <&99 -r unencrypted_file; do
    vcs_add "$unencrypted_file.gpg"
done 99<"$BB_FILES"
vcs_commit 'Re-encrypted keys'

echo '========== DONE.'
echo 'Likely next step:'
echo "    $VCS_TYPE push"
