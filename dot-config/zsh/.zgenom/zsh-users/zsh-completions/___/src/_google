#compdef google
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for googlecl (https://code.google.com/p/googlecl/)
#
#  Source: https://raw.github.com/dadrc/zsh-cfg/master/completions/_google
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * dadrc (https://github.com/dadrc)
#  * Ben O'Hara (https://github.com/benohara)
#
# ------------------------------------------------------------------------------

_google() {
  # init variables
  local curcontext="$curcontext" state line
  typeset -A opt_args

  # init state
   _arguments \
    '1: :->service'\
    '2: :->task'

  case $state in
  service)
    _arguments '1:service:(picasa blogger youtube docs contacts calendar finance)'
  ;;
  *)
    case $words[2] in
    picasa)
      compadd "$@" get create list list-albums tag post delete
      ;;
    blogger)
      compadd "$@" post tag list delete
      ;;
    youtube)
      compadd "$@" post tag list delete
      ;;
    docs)
      compadd "$@" edit delete list upload get
      ;;
    contacts)
      compadd "$@" list list-groups add add-groups delete-groups delete
      ;;
    calendar)
      compadd "$@" add list today delete
      ;;
    finance)
      compadd "$@" list-txn delete-pos create-pos delete-txn create create-txn list list-pos delete
      ;;
    *)
    esac
  esac
}

_google "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
