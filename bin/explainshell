#!/usr/bin/env bash
#
# Author: <PERSON> <<EMAIL>>
#
# Explains a command using explainshell.com

set -o pipefail
if [[ -n "$DEBUG" ]]; then
  # shellcheck disable=SC2086
  if [[ "$(echo $DEBUG | tr '[:upper:]' '[:lower:]')" == "verbose" ]]; then
    set -x
  fi
fi

function debug() {
  if [[ -n "$DEBUG" ]]; then
        zsh_debug_echo "$@"
  fi
}

function echo-stderr() {
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function has() {
  # Check if a command is in $PATH
  which "$@" > /dev/null 2>&1
}

# on_exit and add_on_exit from http://www.linuxjournal.com/content/use-bash-trap-statement-cleanup-temporary-files

# Usage:
#   add_on_exit rm -f /tmp/foo
#   add_on_exit     zsh_debug_echo "I am exiting"
#   tempfile=$(mktemp)
#   add_on_exit rm -f "$tempfile"

function on_exit()
{
  for i in "${on_exit_items[@]}"
  do
    # shellcheck disable=SC2086
    eval $i
  done
}

function add_on_exit()
{
  local n=${#on_exit_items[*]}
  # shellcheck disable=SC2004
  on_exit_items[$n]="$*"
  if [[ $n -eq 0 ]]; then
    trap on_exit EXIT
  fi
}

# Set up a working scratch directory
SCRATCH_D=$(mktemp -d)

if [[ ! "$SCRATCH_D" || ! -d "$SCRATCH_D" ]]; then
      zsh_debug_echo "Could not create temp dir"
  exit 1
fi

add_on_exit rm -rf "$SCRATCH_D"

function get-settings() {
  TEXT_WEB_BROWSER=${TEXT_WEB_BROWSER:-'lynx'}
}

function check-dependencies() {
  debug "Checking dependencies..."
  # shellcheck disable=SC2041
  # Placeholders for whatever programs you really need
  for p in "$TEXT_WEB_BROWSER"
  do
    if ! has $p; then
      fail "Can't find $p in your $PATH"
    else
      debug "- Found $p"
    fi
  done
}

function my-name() {
  basename "$0"
}

function usage() {
      zsh_debug_echo "Usage: $(my-name) shell command"
  echo
      zsh_debug_echo "Gets an explanation of a command from explainshell.com"
}

get-settings
check-dependencies
if [[ $# == 0 ]]; then
  usage
  exit 1
fi

set -ue
cmd="$(echo "$@" | tr ' ' '+')";
url="https://explainshell.com/explain?cmd=$cmd"
exec lynx -dump "$url"
