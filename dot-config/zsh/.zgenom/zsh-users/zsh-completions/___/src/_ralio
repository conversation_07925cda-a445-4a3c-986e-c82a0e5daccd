#compdef ralio
# ------------------------------------------------------------------------------
# Copyright (c) 2011 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for ralio (https://github.com/oesmith/ralio)
#  a Rally client
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

_ralio ()
{
  local curcontext="$curcontext" state line
  typeset -A opt_args

  _arguments -C \
    ':command:->command' \
    '*::options:->options'

  case $state in
  (command)
    local -a subcommands=(
      "backlog:Show the product backlog"
      "sprint:Show the current team iteration"
      "show:Show related information for an individual story, defect or task"
      "open:Open a story, defect or task in a web browser"
      "start:Set a task, defect or story state to in-progress and assign it to you"
      "finish:Set a task, defect or story state to completed and assign it to you"
      "abandon:Set a task, defect or story state to defined and clear the owner"
      "block:Set a task, defect or story state to blocked"
      "unblock:Set a task, defect or story state to unblocked"
      "current:Show your current tasks and stories"
      "point:Set the points for a story or defect"
      "task:Allow you to create and delete story tasks."
      "configure:Set your Rally configurations."
    )
    _describe -t commands 'ralio commands' subcommands

    _arguments \
      {-V,--version}"[display version information]" \
      {-h,--help}"[output usage information]"
    ;;
  (options)
    case $line[1] in
      (sprint)
        _arguments \
          "-t[Show tasks]" \
          "-p[Project name]" \
          "-f[Filter results]"
        ;;

      (start | finish)
        _arguments \
          '--pair[Pair programming partner]' \
          "--resolution[Resolution status]" \
          "--rootcause[Root cause]"
        ;;

      (task)
        __ralio-task
        ;;

    esac
    ;;
  esac
}

__ralio-task ()
{
  local curcontext="$curcontext" state line
  typeset -A opt_args

  _arguments \
    ':command:->command' \
    '*::options:->options'

  case $state in
    (command)
      local -a subcommands=(
        "create:Create a new task"
        "delete:Delete a task"
      )
      _describe -t commands 'ralio task' subcommands
      ;;

    (options)
      case $line[1] in
        (create|delete)
          _arguments \
            -n"[Name of the new task]" \
            -t"[Name of the parent task]"
          ;;
      esac
      ;;
  esac
}

_ralio "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
