[base]
default          = none
unknown-token    = 124,bold
commandseparator = none
redirection      = none
here-string-tri  = yellow
here-string-text = bg:19
here-string-var  = 185,bg:19
exec-descriptor  = yellow,bold
comment          = black,bold
correct-subtle   = bg:55
incorrect-subtle = bg:52
subtle-bg        = bg:17
secondary        = zdharma
recursive-base   = 183

[command-point]
reserved-word  = 146
subcommand     = 146
alias          = 109
suffix-alias   = 109
global-alias   = bg:19
builtin        = 109
function       = 109
command        = 109
precommand     = 109
hashed-command = 109
single-sq-bracket = 109
double-sq-bracket = 109
double-paren   = 146

[paths]
path          = 208
pathseparator = 
path-to-dir   = 208,underline
globbing      = 220
globbing-ext  = 225

[brackets]
paired-bracket = bg:blue
bracket-level-1 = 115
bracket-level-2 = 177
bracket-level-3 = 220

[arguments]
single-hyphen-option   = 185
double-hyphen-option   = 185
back-quoted-argument   = none
single-quoted-argument = 147
double-quoted-argument = 147
dollar-quoted-argument = 147

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 185
; backslash or $... in "..." (i.e. variable in string)
back-or-dollar-double-quoted-argument = 185

[other]
variable             = none
assign               = none
assign-array-bracket = 109
history-expansion    = blue,bold

[math]
mathvar = blue,bold
mathnum = 208
matherr = 124

[for-loop]
forvar = none
fornum = 208
; operator
foroper = 147
; separator
forsep = 109

[case]
case-input       = 109
case-parentheses = 116
case-condition   = bg:19
