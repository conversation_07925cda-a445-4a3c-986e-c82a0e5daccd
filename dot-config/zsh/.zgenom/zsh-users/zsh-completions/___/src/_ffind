#compdef ffind
# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for ffind (https://github.com/jaimebuelta/ffind).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# * Sergei Eremenko (https://github.com/SmartFinn)
#
# ------------------------------------------------------------------------------

_arguments -C \
  '(-h --help)'{-h,--help}'[show help message and exit]' \
  '--version[show version number and exit]' \
  '-p[match whole path, not only name of files]' \
  '--nocolor[do not display color]' \
  '--nosymlinks[do not follow symlinks]' \
  '--hidden[do not ignore hidden directories]' \
  '-c[force case sensitive]' \
  '-i[force case insensitive]' \
  '--delete[delete files found]' \
  '--exec[execute the given command with the file found]:command:_command_names' \
  '--module[execute the given module with the file found]:module_name args:' \
  '--command[execute the given python program with the file found]:program:_files' \
  '--ignore-vcs[ignore version control system files and directories]' \
  '-f[experimental fuzzy search]' \
  '--return-results[for testing purposes only]' \
  '1:directory to search:_path_files -/' \
  '*:filepattern:'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
