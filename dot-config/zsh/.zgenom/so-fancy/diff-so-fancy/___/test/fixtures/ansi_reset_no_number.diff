[93mdiff --git a/history.md b/history.md[m
[93mindex f6776e0..a6b4546 100644[m
[93m--- a/history.md[m
[93m+++ b/history.md[m
[1;35m@@ -1,3 +1,3 @@[m
[1;31m-[m[1;31m## History[m
[1;32m+[m[1;32m## Historyz[m
 [m
 [m`diff-so-fancy` started as [a commit in paulirish's dotfiles](https://github.com/paulirish/dotfiles/commit/6743b907ff586c28cd36e08d1e1c634e2968893e#commitcomment-13349456), which grew into a [standalone script](https://github.com/paulirish/dotfiles/blob/63cb8193b0e66cf80ab6332477f1f52c7fbb9311/bin/diff-so-fancy). Later, [@stevemao](https://github.com/stevemao) brought it into its [own repo](https://github.com/so-fancy/diff-so-fancy) (here), and gave it the room to mature. It's quickly grown into a [widely collaborative project](https://github.com/so-fancy/diff-so-fancy/graphs/contributors).[m
