# Aliases in alphabetical order

alias ma='meteor add'                  # Add a package to this project.
alias map='meteor add-platform'        # Add a platform to this project.
alias mad='meteor admin'               # Administrative commands.
alias mau='meteor authorized'          # View or change authorized users and organizations for a site.
alias mb='meteor build'                # Build this project for all platforms.
alias mcl='meteor claim'               # Claim a site deployed with an old Meteor version.
alias mca='meteor configure-android'   # Run the Android configuration tool from Meteor's ADK environment.
alias mc='meteor create'               # Create a new project.
alias mdb='meteor debug'               # Run the project, but suspend the server process for debugging.
alias mde='meteor deploy'              # Deploy this project to Meteor.
alias mis='meteor install-sdk'         # Installs SDKs for a platform.
alias ml='meteor list'                 # List the packages explicitly used by your project.
alias mlp='meteor list-platforms'      # List the platforms added to your project.
alias mls='meteor list-sites'          # List sites for which you are authorized.
alias mli='meteor login'               # Log in to your Meteor developer account.
alias mlo='meteor logout'              # Log out of your Meteor developer account.
alias mlog='meteor logs'               # Show logs for specified site.
alias mm='meteor mongo'                # Connect to the Mongo database for the specified site.
alias mp='meteor publish'              # Publish a new version of a package to the package server.
alias mpa='meteor publish-for-arch'    # Builds an already-published package for a new platform.
alias mpr='meteor publish-release'     # Publish a new meteor release to the package server.
alias mr='meteor remove'               # Remove a package from this project.
alias mrp='meteor remove-platform'     # Remove a platform from this project.
alias mre='meteor reset'               # Reset the project state. Erases the local database.
alias m='meteor run'                   # [default] Run this project in local development mode.
alias ms='meteor search'               # Search through the package server database.
alias msh='meteor shell'               # Launch a Node REPL for interactively evaluating server-side code.
alias msw='meteor show'                # Show detailed information about a release or package.
alias mt='meteor test-packages'        # Test one or more packages.
alias mu='meteor update'               # Upgrade this project's dependencies to their latest versions.
alias mw='meteor whoami'               # Prints the username of your Meteor developer account.
