#compdef dget
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for dget 2.22.2
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

_dget() {
  local context state line expl
  local -A opt_args

  _arguments -A "-*" \
    '(- *)'{-h,--help}'[Show help message]' \
    '(- *)'{-v,--version}'[Print license, copyright, and version information and exit]' \
    '(-a --all)'{-a,--all}'[Package is a source package; download all binary packages]' \
    '(--no-conf -b --backup)'{-b,--backup}'[Move files that would be overwritten to ./backup]' \
    '(--no-conf -q --quiet)'{-q,--quiet}'[Suppress wget/curl output]' \
    '(--no-conf -x --extract -d --download-only --build)'{-d,--download-only}'[Do not extract downloaded source]' \
    '(--no-conf -x --extract -d --download-only --build)'{-x,--extract}'[Unpack downloaded source]' \
    '(--no-conf -u --allow-unauthenticated)'{-u,--allow-unauthenticated}'[Make no attempt to verify source package signature]' \
    '(--no-conf -x --extract -d --download-only --build)--build[Build package with dpkg-buildpackage after download]' \
    '(--no-conf)--path[Check this directory in addition to the apt archive]:DIR:_files -/' \
    '(--no-conf --insecure)--insecure[Do not check SSL certificates when downloading]' \
    '(--no-conf --no-cache)--no-cache[Disable server-side HTTP cache]' \
    "(--no-conf)--no-conf[Don't read devscripts config files]" \
    '(-)*:debian package or URL: _alternative "_deb_packages available" "_urls"'
}

_dget "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
