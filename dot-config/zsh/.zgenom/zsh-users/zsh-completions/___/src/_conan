#compdef conan
# ------------------------------------------------------------------------------
# Copyright (c) 2010-2017 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for conan 2.0.0 (https://conan.io).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#  * Shohei Yoshida <https://github.com/syohex>
#
# ------------------------------------------------------------------------------

_conan_common_options=(
  '(- *)'{-h,--help}'[show help message and exit]'
  '-v[verbose output]'
  '-vquiet[verbose level quiet]'
  '-verror[verbose level error]'
  '-vwarning[verbose level warning]'
  '-vnotice[verbose level notice]'
  '-vstatus[verbose level status]'
  '-vverbose[verbose level verbose]'
  '-vv[more verbose output]'
  '-vdebug[verbose level debug]'
  '-vvv[more and more verbose output]'
  '-vtrace[verbose level trace]'
  '--logger[show the output with log format, with time, type and message]'
)

_conan_package_options=(
  '--name[Provide a package name if not specified in conanfile]:name'
  '--version[Provide a package version if not specified in conanfile]:version'
  '--user[Provide a user]:user'
  '--channel[Provide a channel]:channel'
  '--requires[directly provide requires instead of a conan file]:requires'
  '--tool-requires[directly provide tool-requires instead of a conanfile]:tool_requires'
)

_conan_define_options=(
  \*{-o,--options,'-o\:h','--options\:host'}'[define options of host machine]:option'
  \*{'-o\:b','--options\:build'}'[define options of build machine]:option'
)

_conan_profile_options=(
  '(-pr --profile -pr\:h --profile\:host)'{-pr,--profile,-pr:h,--profile:host}'[apply the specified profile to the host machine]: :_conan_profiles'
  '(-pr\:b --profile\:build)'{-pr:b,--profile:build}'[apply the specified profile to the build machine]: :_conan_profiles'
)

_conan_setting_options=(
  \*{-s,--settings,'-s\:h','--settings\:host'}'[overwrite the defaults of host machine to build]:setting'
  \*{'-s\:b','--settings\:build'}'[overwrite the defaults of build machine to build]:setting'
)

_conan_configuration_options=(
  \*{-c,--conf,'-c\:h','--conf\:host'}'[overwrite the defaults of host machine to configure]:config'
  \*{'-c\:b','--conf\:build'}'[overwrite the defaults of build machine to configure]:config'
)

_conan_lockfile_options=(
  '(-l --lockfile)'{-l,--lockfile}'[path to a lockfile]: :_files'
  '--lockfile-partial[do not raise an error if some dependency is not found in lockfile]'
  '--lockfile-out[filename of the updated lockfile]: :_files'
  '--lockfile-packages[lock package-id and package-revision information]'
  '--lockfile-clean[remove unused entries from the lockfile]'
)

_conan_remote_options=(
  '(-r --remote -nr --no-remote)'{-r,--remote}'[look in the specified remote server]:remote'
  '(-r --remote -nr --no-remote)'{-nr,--no-remote}'[do not use remote, resolve exclusively in the cache]'
)

_conan() {
  local context state state_descr line
  typeset -A opt_args

  _arguments -C \
    '(- : *)'{-h,--help}'[display help information]' \
    '(- : *)'{-v,--version}'[display version information]' \
    '(-h --help)1: :_conan_commands' \
    '(-h --help)*:: :->command_args'

  case $state in
    command_args)
      (( $+functions[_conan_${words[1]}] )) && _conan_${words[1]}
    ;;
  esac
}

(( $+functions[_conan_commands] )) ||
_conan_commands() {
  local -a commands=(
    # consumer commands
    'cache:perform file operations in the local cache'
    'config:manages conan configuration information'
    'graph:obtain information about the dependency graph without fetching binaries'
    'inspect:inspect a conanfile.py to return its public fields'
    'install:installs the requirements specified in a "conanfile.py" or "conanfile.txt"'
    'list:list recipes, revisions or packages in the cache or the remotes'
    'lock:create or manage lockfiles'
    'profile:manage profiles'
    'remote:manage the remote list and the users authenticated on them'
    'remove:remove recipes or packages from local cache or a remote'
    'search:search for package recipes in all the remotes or a remote'

    # create commands
    'build:install dependencies and call the build() method'
    'create:create a package'
    'download:download(without installing) a single conan package from a remote server'
    'editable:allow working with a package that resides in user folder'
    'export:export a recipe to the Conan package cache'
    'export-pkg:create a package directly from pre-compiled binaries'
    'new:create a new example recipe and source files from a template'
    'source:call the source() method'
    'test:test a package from a test_package folder'
    'upload:upload packages to a remote'
  )

  _describe -t 'subcommands' 'subcommands' commands
}

(( $+functions[_conan_cache] )) ||
_conan_cache() {
  local ret=1

  _arguments -C \
   '(- *)'{-h,--help}'[show help message and exit]' \
   '1: :(clean path)' \
   '*:: :->args'

  case $state in
    (args)
      local -a opts=($_conan_common_options[@])
      case $words[1] in
        (clean)
          opts+=(
            '(-s --source)'{-s,--source}'[clean source folders]'
            '(-b --build)'{-b,--build}'[clean build folders]'
            '(-d --download)'{-d,--download}'[clean download folders]'
            \*{-p,--package-query}'[remove only the packages matching a specific query]:query'
            '1:pattern'
          )
          ;;
        (path)
          opts+=(
            '--folder[path to show]::type:(export_source source build)' \
            '1:recipe_or_package_reference:_files'
          )
          ;;
      esac

      _arguments "$opts[@]"
    ;;
  esac

  return $ret
}

(( $+functions[_conan_config] )) ||
_conan_config() {
  _arguments -C \
    '(- : *)'{-h,--help}'[display help information]' \
    '1: :_conan_config_commands' \
    '*:: :->args'

  case $state in
    (args)
      local -a opts=($_conan_common_options[@])
      case $words[1] in
        (install)
          opts+=(
            '--verify-ssl[Verify SSL connection when downloading file]: :(True False)'
            '(-t --type)'{-t,--type}'[Type of remote config]: :(git dir file url)'
            \*{-a,--args}'[String with extra arguments for "git clone"]:arg'
            \*{-sf,--source-folder}'[Install files only from a source subfolder from specified origin]: :_files -/'
            '(-tf --target-folder)'{-tf,--target-folder}'[Install to that path in the conan cache]: :_files -/'
          )
          ;;
        (list)
          opts+=(
            '(-f --format)'{-f,--format}'[select the output format]:format:(json)'
          )
          ;;
      esac

      _arguments "$opts[@]"
    ;;
  esac
}

(( $+functions[_conan_config_commands] )) ||
_conan_config_commands() {
  local -a commands=(
    'home:show the Conan home folder'
    'install:install the configuration into the Conan home folder'
    'list:show all the Conan available configurations:'
  )
  _describe -t 'commands' "command" commands
}

(( $+functions[_conan_graph] )) ||
_conan_graph() {
  _arguments -C \
    '(- : *)'{-h,--help}'[display help information]' \
    '1: :_conan_graph_commands' \
    '*:: :->args'

  local -a common_opts=(
    $_conan_package_options[@]
    \*{-b,--build}'[optional, specify which packages to build from source]:build'
    $_conan_remote_options[@]
    '(-u --update)'{-u,--update}'[will check the remote and in case a newer version]'
    $_conan_define_options[@]
    $_conan_profile_options[@]
    $_conan_setting_options[@]
    $_conan_configuration_options[@]
    $_conan_lockfile_options[@]
  )

  case $state in
    (args)
      local -a opts=($_conan_common_options[@])
      case $words[1] in
        (build-order)
          opts+=(
            '(-f --format)'{-f,--format}'[select the output format]:format:(json)'
            $common_opts[@]
          )
          ;;
        (build-order-merge)
          opts+=(
            '--file[files to be merged]:file:_files'
          )
          ;;
        (info)
          opts+=(
            $common_opts[@]
            '(-f --format)'{-f,--format}'[select the output format]:format:(html json dot)'
            '--check-updates[check if there are recipe updates]'
            '*--filter[show only the specified fields]:filter'
            '*--package-filter[print information only for packages that match the patterns]:package_filter'
            '--deploy[deploy using the provided deployer to the output folder]:deployer'
          )
          ;;
      esac

      _arguments "$opts[@]"
    ;;
  esac
}

(( $+functions[_conan_graph_commands] )) ||
_conan_graph_commands() {
  local -a commands=(
    'build-order:compute the build order of a dependency graph'
    'build-order-merge:merge more than 1 build-order file'
    'info:compute the dependency graph and shows information about it'
  )
  _describe -t 'commands' "command" commands
}

(( $+functions[_conan_inspect] )) ||
_conan_inspect() {
  _arguments \
    "$_conan_common_options[@]" \
    '(-f --format)'{-f,--format}'[select the output format]:format:(json)' \
    '*:recipe_folder:_files -/'
}

(( $+functions[_conan_install] )) ||
_conan_install() {
  local -a generators=(
    cmake cmake_multi cmake_paths cmake_find_package cmake_find_package_multi
    msbuild visual_studio visual_studio_multi visual_studio_legacy xcode compiler_args gcc
    boost-build b2 qbs qmake scons pkg_config virtualenv virtualenv_python virtualbuildenv
    virtualrunenv youcompleteme txt json premake make markdown deploy
  )
  local -a opts=(
    $_conan_common_options[@]
    '(-f --format)'{-f,--format}'[select the output format]:format:(json)'
    \*{-b,--build}'[optional, specify which packages to build from source]:build'
    $_conan_remote_options[@]
    '(-u --update)'{-u,--update}'[will check the remote and in case a newer version]'
    $_conan_define_options[@]
    $_conan_profile_options[@]
    $_conan_setting_options[@]
    $_conan_configuration_options[@]
    $_conan_lockfile_options[@]
    '(-g --generator)'{-g,--generator}"[generators to use]:generator:($generators)"
    '(-of --output)'{-of,--output-folder}'[the root output folder for generated and build files]:dir:_files -/'
    '--deploy[deploy using the provided deployer to the output folder]:deployer'
    '1:recipe_dir_or_conanfile:_files'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_list] )) ||
_conan_list() {
  local -a opts=(
    $_conan_common_options[@]
    '(-f --format)'{-f,--format}'[select the output format]:format:(json html)'
    \*{-p,--package-query}'[remove only the packages matching a specific query]:query'
    '(-r --remote)'{-r,--remote}'[remote names]:remote'
    '(-c --cache)'{-c,--cache}'[search in the local cache]'
    '1:recipe_or_package_reference:_files'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_lock] )) ||
_conan_lock() {
  _arguments -C \
    '(- : *)'{-h,--help}'[display help information]' \
    '1: :_conan_lock_commands' \
    '*:: :->args'

  case $state in
    (args)
      local -a opts=($_conan_common_options)
      case $words[1] in
        (add)
          opts+=(
            '*--requires[add references to lockfile]:requires'
            '*--build-requires[add build-requires to lockfile]:build_requires'
            '*--python-requires[add python-requires to lockfile]'
            '--lockfile-out[file name of the created lockfile]:filename:_files'
            '--lockfile[file name of the input lockfile]:filename:_files'
          )
          ;;
        (create)
          opts+=(
            \*{-b,--build}'[optional, specify which packages to build from source]:build'
            $_conan_remote_options[@]
            '(-u --update)'{-u,--update}'[will check the remote and in case a newer version]'
            $_conan_define_options[@]
            $_conan_profile_options[@]
            $_conan_setting_options[@]
            $_conan_configuration_options[@]
            $_conan_lockfile_options[@]
            '1: :_files'
          )
        ;;
        (merge)
          opts+=(
            '--lockfile-out[file name of the created lockfile]:filename:_files'
            '--lockfile[file name of the input lockfile]:filename:_files'
          )
        ;;
      esac

      _arguments "$opts[@]"
    ;;
  esac
}

(( $+functions[_conan_lock_commands] )) ||
_conan_lock_commands() {
  local -a commands=(
    'add:add requires, build-requires or python requires to an existing or new lockfile'
    'create:create a lockfile from a conanfile or a reference'
    'merge:merge 2 or more lockfiles'
  )
  _describe -t 'commands' "command" commands
}

(( $+functions[_conan_profile] )) ||
_conan_profile() {
  _arguments -C \
    '(- : *)'{-h,--help}'[display help information]' \
    '1: :_conan_profile_commands' \
    '*:: :->args'

  case $state in
    (args)
      local opts=($_conan_common_options[@])
      case $words[1] in
        (detect)
          opts+=(
            '--name[profile name, "default" if not specified]::name'
            '(-f --force)'{-f,--force}'[overwrite if exists]'
          )
          ;;
        (list)
          opts+=(
            '(-f --format)'{-f,--format}'[select the output format]:format:(json)'
          )
          ;;
        (path|show)
          opts+=(
            $_conan_define_options[@]
            $_conan_profile_options[@]
            $_conan_setting_options[@]
            $_conan_configuration_options[@]
          )
          ;;
      esac

      _arguments "$opts[@]"
    ;;
  esac
}

(( $+functions[_conan_profile_commands] )) ||
_conan_profile_commands() {
  local -a commands=(
    'detect:generate a profile using auto-detected values'
    'list:list all profiles in the cache'
    'path:show profile path location'
    'show:show aggregated profiles from the passed arguments'
  )
  _describe -t 'commands' "command" commands
}

(( $+functions[_conan_remote] )) ||
_conan_remote() {
  _arguments -C \
    '(- : *)'{-h,--help}'[display help information]' \
    '1: :_conan_remote_commands' \
    '*:: :->args'

  case $state in
    (args)
      local -a opts=($_conan_common_options[@])
      case $words[1] in
        (add)
          opts+=(
            '--insecure[allow insecure server connections when using SSL]'
            '--index[insert the remote at a specific position in the remote list]:index'
            '(-f --force)'{-f,--force}'[force the definition of the remote even if duplicated]'
            '1:name'
            '2:url'
          )
          ;;
        (auth)
          opts+=(
            '--with-user[only try to auth in those remotes that already have a user name]'
            '1:remote:_conan_remotes'
          )
          ;;
        (list|list-users)
          opts+=('(-f --format)'{-f,--format}'[select the output format]:format:(json)')
          ;;
        (login)
          opts+=(
            '(-f --format)'{-f,--format}'[select the output format]:format:(json)'
            '(-p --password)'{-p,--password}'[user password]:password'
            '1:remote:_conan_remotes'
            '2:username:'
          )
          ;;
        (logout)
          opts+=(
            '(-f --format)'{-f,--format}'[select the output format]:format:(json)'
            '1:remote:_conan_remotes'
          )
          ;;
        (remove)
          opts+=('1:remote:_conan_remotes')
          ;;
        (rename)
          opts+=(
            '1:remote:_conan_remotes'
            '2:new_name'
          )
          ;;
        (set-user)
          opts+=(
            '(-f --format)'{-f,--format}'[select the output format]:format:(json)'
            '1:remote:_conan_remotes'
            '2:user_name'
          )
          ;;
        (update)
          opts+=(
            '--url[new url for the remote]'
            '--secure[do not allow insecure server connections when using SSL]'
            '--insecure[allow insecure server connections when using SSL]'
            '--index[insert the remote at a specific position in the remote list]:index'
            '1:remote:_conan_remotes'
          )
          ;;
      esac
    ;;
  esac
}

(( $+functions[_conan_remote_commands] )) ||
_conan_remote_commands() {
  local -a commands=(
    'add:add a remote'
    'auth:authenticate in the defined remotes'
    'disable:disable all the remotes matching a pattern'
    'enable:enable all the remotes matching a pattern'
    'list:list current remotes'
    'list-users:list the users logged into all the remotes'
    'login:login into the specified remotes matching a pattern'
    'logout:clear the existing credentials for the specified remotes matching a pattern'
    'remove:remove a remote'
    'rename:rename a remote'
    'set-user:associate a username with a remote matching pattern without performing the authentication'
    'update:update a remote'
  )
  _describe -t 'commands' "command" commands
}

(( $+functions[_conan_remove] )) ||
_conan_remove() {
  local -a opts=(
    $_conan_common_options[@]
    '(-c --confirm)'{-c,--confirm}'[remove without requesting a confirmation]'
    \*{-p,--package-query}'[remove only the packages matching a specific query]:query'
    \*{-r,--remote}'[will remove from the specified remote]:remote'
    '*:recipe_or_package_reference:_files'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_search] )) ||
_conan_search() {
  local -a opts=(
    $_conan_common_options[@]
    \*{-r,--remote}'[will remove from the specified remote]:remote'
    '*:recipe_reference:_files'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_build] )) ||
_conan_build() {
  local -a opts=(
    $_conan_common_options[@]
    $_conan_package_options[@]
    \*{-b,--build}'[optional, specify which packages to build from source]:build'
    $_conan_remote_options[@]
    '(-u --update)'{-u,--update}'[will check the remote and in case a newer version]'
    $_conan_define_options[@]
    $_conan_profile_options[@]
    $_conan_setting_options[@]
    $_conan_configuration_options[@]
    $_conan_lockfile_options[@]
    '*: :_files'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_create] )) ||
_conan_create() {
  local -a opts=(
    $_conan_common_options[@]
    $_conan_package_options[@]
    \*{-b,--build}'[optional, specify which packages to build from source]:build'
    $_conan_remote_options[@]
    '(-u --update)'{-u,--update}'[will check the remote and in case a newer version]'
    $_conan_define_options[@]
    $_conan_profile_options[@]
    $_conan_setting_options[@]
    $_conan_configuration_options[@]
    $_conan_lockfile_options[@]
    '--build-require[whether the provided reference is a build-require]'
    '(-tf --test-folder)'{-tf,--test-folder}'[alternative test folder name]:folder:_files -/'
    '*: :_files -/'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_download] )) ||
_conan_download() {
  local -a opts=(
    $_conan_common_options[@]
    '--only-recipe[download only the recipe, not the binary packages]'
    \*{-p,--package-query}'[only download packages matching a specific query]:query'
    '(-r --remote)'{-r,--remote}'[download from the specific remote]:remote:_conan_remotes'
    '*:recipe_or_package_reference:_files'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_editable] )) ||
_conan_editable() {
  _arguments -C \
    '(- : *)'{-h,--help}'[display help information]' \
    '1: :_conan_editable_commands' \
    '*:: :->args'

  case $state in
    (args)
      local opts=($_conan_common_options[@])
      case $words[1] in
        (add)
          opts+=(
            $_conan_package_options[@]
            '(-of --output-folder)'{-of,--output-folder}'[the root output folder]:folder:_files -/'
          )
          ;;
        (list)
          opts+=('(-f --format)'{-f,--format}'[select the output format]:format:(json)')
          ;;
        (remove)
          opts+=('(-r --refs)'{-r,--refs}'[directly provide reference patterns]:refs')
          ;;
      esac

      _arguments "$opts[@]"
    ;;
  esac
}

(( $+functions[_conan_editable_commands] )) ||
_conan_editable_commands() {
  local -a commands=(
    'add:define the given path location as the package reference'
    'list:list all the packages in editable mode'
    'remove:remove the editable mode for this reference'
  )
  _describe -t 'commands' "command" commands
}

(( $+functions[_conan_export] )) ||
_conan_export() {
  local -a opts=(
    $_conan_common_options[@]
    $_conan_package_options[@]
    $_conan_remote_options[@]
    $_conan_lockfile_options[@]
    '--build-require[whether the provided reference is a build-require]'
    '*: :_files -/'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_export-pkg] )) ||
_conan_export-pkg() {
  local -a opts=(
    $_conan_common_options[@]
    $_conan_package_options[@]
    $_conan_remote_options[@]
    $_conan_lockfile_options[@]
    $_conan_define_options[@]
    $_conan_profile_options[@]
    $_conan_setting_options[@]
    $_conan_configuration_options[@]
    '*: :_files -/'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_new] )) ||
_conan_new() {
  local -a templates=(
    basic cmake_lib cmake_exe meson_lib meson_exe msbuild_lib msbuild_exe
    bezel_lib bezel_exe autotools_lib autotools_exe
  )
  local -a opts=(
    $_conan_common_options[@]
    \*{-d,--define}'[define a template argument as key=value]:key_value'
    '(-f --force)'{-f,--force}'[overwrite file if it already exists]'
    "1:template:($templates)"
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_source] )) ||
_conan_source() {
  local -a opts=(
    $_conan_common_options[@]
    $_conan_package_options[@]
    '1: :_files'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_test] )) ||
_conan_test() {
  local -a opts=(
    $_conan_common_options[@]
    $_conan_package_options[@]
    \*{-b,--build}'[optional, specify which packages to build from source]:build'
    $_conan_remote_options[@]
    '(-u --update)'{-u,--update}'[will check the remote and in case a newer version]'
    $_conan_define_options[@]
    $_conan_profile_options[@]
    $_conan_setting_options[@]
    $_conan_configuration_options[@]
    $_conan_lockfile_options[@]
    '1:path:_files -/'
    '2:reference'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_upload] )) ||
_conan_upload() {
  local -a opts=(
    $_conan_common_options[@]
    \*{-p,--package-query}'[only upload packages matching a specific query]:query'
    '(-r --remote)'{-r,--remote}'[upload to this specific remote]:remote:_conan_remotes'
    '--only-recipe[upload only the recipe, not the binary packages]'
    '--force[force the upload of the artifacts]'
    '--check[perform an integrity check before upload]'
    '(-c --confirm)'{-c,--confirm}'[upload all matching recipes without confirmation]'
    '1::reference'
  )

  _arguments "$opts[@]"
}

(( $+functions[_conan_remotes] )) ||
_conan_remotes() {
  local -a remotes=(${(f)"$(_call_program remotes $service remote list)"})
  _describe -t remotes 'remote' remotes "$@"
}

(( $+functions[_conan_profiles] )) ||
_conan_profiles() {
  local -a profiles=(${(f)"$(_call_program profiles $service profile list 2>/dev/null)"})
  _describe -t profiles 'profile' profiles "$@"
}

_conan "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
