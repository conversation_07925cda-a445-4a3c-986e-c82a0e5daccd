#compdef pixz
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for pixz 1.0.7 (https://github.com/vasi/pixz).
#
#  a parallel, indexing version of xz
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Jindřich Pilař (https://github.com/JindrichPilar)
#
# ------------------------------------------------------------------------------

(( $+functions[_pixz_cpus] )) ||
_pixz_cpus() {
  if [[ -n $commands[nproc] ]]; then
    local -a cpus=({1..$(nproc)})
    _values cpus $cpus
  fi
}

_pixz() {
  local compress_opts="-0 -1 -2 -3 -4 -5 -6 -7 -8 -9 -e"

  _arguments \
    '(- 1 *)'-h"[Show help and exit]" \
    '(- 1 *)-l[List tarball contents very fast]:file:_files -g "*.(tpxz|xz)"' \
    '(- 1 *)-x[Extract one file very fast]:filepath' \
    '(- 1 *)-d[Decompress]:file:_files -g "*.(tpxz|xz)' \
    '-i[Input]:file:_files' \
    '-o[Output]:output:_files' \
    '-p[Use a maximum of NUM CPU-intensive threads]:cpu:_pixz_cpus' \
    "-t[Don't assume input is in tar format]" \
    '-k[Keep original input (do not remove it)]' \
    "($compress_opts)"'-e[Use "extreme" compression, which is much slower]' \
    '-f[Set the size of each compression block, relative to the LZMA dictionary size (default is 2.0)]:num' \
    '-q[Set the number of blocks to allocate for the compression queue (default is 1.3 * cores + 2)]:num' \
    "($compress_opts)"-{0,1,2,3,4,5,6,7,8,9}'[Set compression level(highest compression, slowest)]' \
    '1:inputfile:_files' \
    '2:outputfile:_files' \
    '*: :_files'
}

_pixz

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
