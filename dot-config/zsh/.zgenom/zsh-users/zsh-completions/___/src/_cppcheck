#compdef cppcheck
# ------------------------------------------------------------------------------
# Copyright (c) 2019 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for cppcheck -- a tool for static C/C++ code analysis (https://cppcheck.sourceforge.io/)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Georgy Komarov (https://github.com/jubnzv)
#
# ------------------------------------------------------------------------------
# Notes
# -----
#
#  Created for Cppcheck version 2.17.1 (https://github.com/danmar/cppcheck)
#
# ------------------------------------------------------------------------------

_cppcheck_files() {
  _path_files -/ -g "*.(c|cpp|cxx|h|hpp|C)"
}

_cppcheck() {
  local check_ids='(all warning style performance portability information unusedFunction missingInclude)'
  local platforms='(unix32 unix64 win32A win32W win64 avr8 elbrus-e1cp pic8 pic8-enhanced pic16 mips32 native unspecified)'
  local coding_standards='(normal autosar cert-c-2016 cert-cpp-2016 misra-c-2012 misra-c-2023 misra-cpp-2008 misra-cpp-2023)'

  _arguments \
    "--addon=[Execute addon]" \
    "--addon-python=[Specify the python interpreter]: :_files" \
    "--cppcheck-build-dir=[Analysis output directory]:directory:_files -/" \
    "--check-config[Check cppcheck configuration]" \
    "--check-level=[Configure how much valueflow analysis you want]:level:(reduced normal exhaustive)" \
    "--check-library[Show information when library files have incomplete info]" \
    "--checkers-report=[Write a report of all the active checkers to the given file]:file:_files" \
    "--clang=[Use clang parser instead of the builtin Cppcheck parser]: :_files" \
    "--config-exclude=[Path to be excluded from configuration checking]:directory:_files -/" \
    "--config-exclude-files=[A file that contains a list of config-excludes]:file:_files" \
    "--doc[Print a list of all available checks]" \
    "*--disable=[Disable individual checks]:id:$check_ids" \
    "--dump[Dump xml data for each translation unit]" \
    "-D[Define preprocessor symbol]" \
    "-U[Undefine preprocessor symbol]" \
    "-E[Print preprocessor output on stdout and don't do any further processing]" \
    "--enable=[Enable additional checks]:id:$check_ids" \
    "--error-exitcode=[Integer to return if errors are found]" \
    "--errorlist[Print a list of all the error messages in XML format]" \
    "--exitcode-suppressions=[Used when certain messages should be displayed but should not cause a non-zero exitcode]:_files" \
    "*--file-filter=[Analyze only those files matching the given filter str]:filter" \
    "--file-list=[Specify the files to check in a text file]:_files" \
    "(-f --force)"{-f,--force}"[Force checking of all configurations in files]" \
    "(--fsigned-char --funsigned-char)--fsigned-char[Treat char type as signed]" \
    "(--fsigned-char --funsigned-char)--funsigned-char[Treat char type as unsigned]" \
    "(- 1 *)"{-h,--help}"[Print this help]" \
    "-I[A file that contains a list of config-excludes]:directory:_files -/" \
    "--include-file=[Specify directory paths to search for included header files in a text file]:file:_files" \
    "--include=[Force inclusion of a file before the checked file]:file:_files" \
    "-i[Give a source file or source file directory to exclude from the check]:directory or file:_files" \
    "--inconclusive[Report even though the analysis is inconclusive]" \
    "--inline-suppr[Enable inline suppressions]" \
    "-j[Number of threads to do the checking simultaneously]::num" \
    "-l[No new threads should be started if the load average is exceeds this value]::load_avg" \
    {-x,--language=}"[Forces cppcheck to check all files as the given language]:language:(c c++)" \
    "--library=[Load config file that contains information about types and functions]:config:_files" \
    "--max-configs=[Maximum number of configurations to check in a file]" \
    "--max-ctu-depth=[Maximum depth in whole program analysis]:num" \
    "--output-file=[File to save results]:file:_files" \
    "--output-format=[Specify the output format]:format:(text sarif xml)" \
    "--platform=[Specified platform specific types and sizes]:platform:$platforms" \
    "--plist-output=[Generate Clang-plist output files in folder]:_files" \
    "--project=[Run Cppcheck on project]:file:_files" \
    "--project-configuration=[Limit the configuration cppcheck should check]:configuration" \
    "--platform=[Set platform specific types and sizes]:platforms:(unix32 unix64 win32A win32W win64 avr8 elbrus-e1cp pic8 pic8-enhanced pic16 mips32 native unspecified)" \
    "(-q --quiet)"{-q,--quiet}"[Do not show progress reports]" \
    {-rp,--relative-paths}"=[Use relative paths in output (separated with ;)]:_files" \
    "--report-progress[Report progress messages while checking a file]" \
    "--report-type=[Add guideline and classification fields for specified coding standard]:standard:$coding_standards" \
    "--rule=[Match regular expression]:rule" \
    "--rule-file=[Use given rule file]:file:_files" \
    "--showtime=[show timing information]:type:(none file file-total summary top5_file top5_summary top5)" \
    "--std=[Set standard]:std:(c89 c99 c11 c++03 c++11 c++14 c++17 c++20)" \
    "--suppress=[Suppress warnings (format: \[error id\]:\[filename\]:\[line\])]" \
    "--suppressions-list=[Suppress warnings listed in the file]:_files" \
    "--suppress-xml=[Suppress warnings listed in a xml file]:_files" \
    "--template=[Format the error messages]" \
    "--template-location=[Format the error message location]" \
    "*-U[Undefine preprocessor symbol]:symbol" \
    "(-v --verbose)"{-v,--verbose}"[Output more detailed error information]" \
    "--version[Print out version number]" \
    "--xml[Write results in xml format to stderr]" \
    "*: :_cppcheck_files"
}

_cppcheck "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
