#compdef mix
#autoload

# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for Elixir Mix
#
#  Last updated: 18.04.2017
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Han Ngo (https://github.com/tieubao)
#  * Teja Sophista (https://github.com/tejanium)
#
# ------------------------------------------------------------------------------

local -a _1st_arguments
_1st_arguments=(
    'app.start:Starts all registered apps'
    'app.tree:Prints the application tree'
    'archive:Lists installed archives'
    'archive.build:Archives this project into a .ez file'
    'archive.install:Installs an archive locally'
    'archive.uninstall:Uninstalls archives'
    'clean:Deletes generated application files'
    'cmd:Executes the given command'
    'compile:Compiles source files'
    'deps:Lists dependencies and their status'
    "deps.clean:Deletes the given dependencies' files"
    'deps.compile:Compiles dependencies'
    'deps.get:Gets all out of date dependencies'
    'deps.tree:Prints the dependency tree'
    'deps.unlock:Unlocks the given dependencies'
    'deps.update:Updates the given dependencies'
    'do:Executes the tasks separated by comma'
    'ecto:Prints Ecto help information'
    'ecto.create:Creates the repository storage'
    'ecto.drop:Drops the repository storage'
    'ecto.dump:Dumps the repository database structure'
    'ecto.gen.migration:Generates a new migration for the repo'
    'ecto.gen.repo:Generates a new repository'
    'ecto.load:Loads previously dumped database structure'
    'ecto.migrate:Runs the repository migrations'
    'ecto.migrations:Displays the repository migration status'
    'ecto.rollback:Rolls back the repository migrations'
    'escript:Lists installed escripts'
    'escript.build:Builds an escript for the project'
    'escript.install:Installs an escript locally'
    'escript.uninstall:Uninstalls escripts'
    'gettext.extract:Extracts translations from source code'
    'gettext.merge:Merge template files into translation files'
    'help:Prints help information for tasks'
    'hex:Prints Hex help information'
    'hex.build:Builds a new package version locally'
    'hex.config:Reads, updates or deletes Hex config'
    'hex.docs:Fetch or open documentation of a package'
    'hex.info:Prints Hex information'
    'hex.key:Manages Hex API key'
    'hex.outdated:Shows outdated Hex deps for the current project'
    'hex.owner:Manages Hex package ownership'
    'hex.public_keys:Manages Hex public keys'
    'hex.publish:Publishes a new package version'
    'hex.retire:Retires a package version'
    'hex.search:Searches for package names'
    'hex.user:Registers or manages Hex user'
    'loadconfig:Loads and persists the given configuration'
    'local:Lists local tasks'
    'local.hex:Installs Hex locally'
    'local.phoenix:Updates Phoenix locally'
    'local.phx:Updates the Phoenix project generator locally'
    'local.public_keys:Manages public keys'
    'local.rebar:Installs Rebar locally'
    'new:Creates a new Elixir project'
    'phoenix.gen.html:Generates controller, model and views for an HTML based resource'
    'phoenix.server:Starts applications and their servers'
    'phx.digest:Digests and compresses static files'
    'phx.digest.clean:Removes old versions of static assets.'
    'phx.gen.auth:Generates a flexible, pre-built authentication system'
    'phx.gen.channel:Generates a Phoenix channel'
    'phx.gen.context:Generates a context with functions around an Ecto schema'
    'phx.gen.html:Generates controller, views, and context for an HTML resource'
    'phx.gen.live:Generates controller, context and helper for a LiveView resource'
    'phx.gen.json:Generates controller, views, and context for a JSON resource'
    'phx.gen.presence:Generates a Presence tracker'
    'phx.gen.schema:Generates an Ecto schema and migration file'
    'phx.gen.secret:Generates a secret'
    'phx.new:Creates a new Phoenix application'
    'phx.new.ecto:Creates a new Ecto project within an umbrella project'
    'phx.new.web:Creates a new Phoenix web project within an umbrella project'
    'phx.routes:Prints all routes'
    'phx.server:Starts applications and their servers'
    'profile.fprof:Profiles the given file or expression with fprof'
    'run:Runs the given file or expression'
    "test:Runs a project's tests"
    'xref:Performs cross reference checks'
    '--help:Describe available tasks'
    '--version:Prints the Elixir version information'
)

__task_list ()
{
    local expl
    declare -a tasks

    tasks=(
        'app.start'
        'app.tree'
        'archive'
        'archive.build'
        'archive.install'
        'archive.uninstall'
        'clean'
        'cmd'
        'compile'
        'deps'
        'deps.clean'
        'deps.compile'
        'deps.get'
        'deps.tree'
        'deps.unlock'
        'deps.update'
        'do'
        'ecto'
        'ecto.create'
        'ecto.drop'
        'ecto.dump'
        'ecto.gen.migration'
        'ecto.gen.repo'
        'ecto.load'
        'ecto.migrate'
        'ecto.migrations'
        'ecto.rollback'
        'escript'
        'escript.build'
        'escript.install'
        'escript.uninstall'
        'gettext.extract'
        'gettext.merge'
        'format'
        'help'
        'hex'
        'hex.build'
        'hex.config'
        'hex.docs'
        'hex.info'
        'hex.key'
        'hex.outdated'
        'hex.owner'
        'hex.public_keys'
        'hex.publish'
        'hex.retire'
        'hex.search'
        'hex.user'
        'loadconfig'
        'local'
        'local.hex'
        'local.public_keys'
        'local.rebar'
        'new'
        'phoenix.gen.html'
        'phoenix.server'
        'phx.digest'
        'phx.digest.clean'
        'phx.gen.auth'
        'phx.gen.channel'
        'phx.gen.context'
        'phx.gen.html'
        'phx.gen.live'
        'phx.gen.json'
        'phx.gen.presence'
        'phx.gen.schema'
        'phx.gen.secret'
        'phx.routes'
        'phx.server'
        'profile.fprof'
        'run'
        'test'
        'xref'
    )

    _wanted tasks expl 'help' compadd $tasks
}

local expl

local curcontext="$curcontext" state line
typeset -A opt_args

_arguments -C \
    ':command:->command' \
    '*::options:->options'

case $state in
  (command)
      _describe -t commands "mix subcommand" _1st_arguments
      return
  ;;

  (options)
    case $line[1] in
      (help)
         _arguments ':feature:__task_list'
         return
      ;;
      (test|format|run)
         _arguments ':PATH:_files'
         return
      ;;
    esac
  ;;
esac
