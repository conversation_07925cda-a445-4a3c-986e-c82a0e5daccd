#compdef kitchen
# ------------------------------------------------------------------------------
# Copyright (c) 2014 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for Test Kitchen (https://kitchen.ci/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Peter Eisentraut (https://github.com/petere)
#  * Shohei YOSHIDA (https://github.com/syohex)
#
# ------------------------------------------------------------------------------


_kitchen() {
  local curcontext="$curcontext" state line
  typeset -A opt_args

  _arguments '1: :->cmds'\
             '2: :->args'

  case $state in
    cmds)
      _kitchen_commands
      ;;
    args)
      case $line[1] in
        converge|create|destroy|diagnose|list|setup|test|verify)
          compadd 'all'
          _kitchen_instances
          ;;
        login)
          _kitchen_instances
          ;;
      esac
      ;;
  esac
}

_kitchen_commands() {
  local commands

  commands=("${(@f)$(_call_program commands $service help | sed -n 's/^  kitchen \([[:alpha:]]*\) .*# \(.*\)$/\1:\2/p')}")
  _describe -t commands 'kitchen commands' commands
}

_kitchen_instances() {
  if [[ $_kitchen_instances_cache_dir != $PWD ]]; then
    unset _kitchen_instances_cache
  fi
  if [[ ${+_kitchen_instances_cache} -eq 0 ]]; then
    _kitchen_instances_cache=(${(f)"$(_call_program instances $service list -b 2>/dev/null)"})
    _kitchen_instances_cache_dir=$PWD
  fi
  _wanted instances expl 'instance' compadd -a _kitchen_instances_cache
}

_kitchen "$@"
