[base]
default          = none
unknown-token    = 124,bold
commandseparator = none
redirection      = none
here-string-tri  = yellow
here-string-text = underline
here-string-var  = 65,underline
exec-descriptor  = yellow,bold
comment          = black,bold
correct-subtle   = bg:55
incorrect-subtle = bg:52
subtle-bg        = bg:18
secondary        = zdharma
recursive-base   = 183

[command-point]
reserved-word  = 186
subcommand     = 186
alias          = 101
suffix-alias   = 101
global-alias   = bg:55
builtin        = 101
function       = 101
command        = 101
precommand     = 101
hashed-command = 101
single-sq-bracket = 101
double-sq-bracket = 101
double-paren   = 186

[paths]
path          = 107
pathseparator = 
path-to-dir   = 107,underline
globbing      = 114
globbing-ext  = 118

[brackets]
paired-bracket = bg:blue
bracket-level-1 = green,bold
bracket-level-2 = yellow,bold
bracket-level-3 = cyan,bold

[arguments]
single-hyphen-option   = 65
double-hyphen-option   = 65
back-quoted-argument   = none
single-quoted-argument = 186
double-quoted-argument = 186
dollar-quoted-argument = 186

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 65
; backslash or $... in "..."
back-or-dollar-double-quoted-argument = 65

[other]
variable             = none
assign               = none
assign-array-bracket = 101
history-expansion    = 114

[math]
mathvar = 114
mathnum = 107
matherr = 124

[for-loop]
forvar = none
fornum = 107
; operator
foroper = 186
; separator
forsep = 109

[case]
case-input       = 101
case-parentheses = 65
case-condition   = underline
