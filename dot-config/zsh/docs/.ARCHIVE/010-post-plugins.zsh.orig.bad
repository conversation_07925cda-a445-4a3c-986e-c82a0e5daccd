##
## This file is sourced by zsh upon start-up. It should contain commands to set
## up aliases, functions, options, key bindings, etc.
##

# vim: ft=zsh sw=4 ts=4 et nu rnu ai si

echo "" &>2
echo "# ++++++++++++++++++++++++++++++++++++++++++++++" &>2
echo "# $0" &>2
echo "# ++++++++++++++++++++++++++++++++++++++++++++++" &>2

bindkey "^[[3~" delete-char

[[ -n "${commands[composer]}" ]] && _path_prepend "$(composer config --global home)/vendor/bin"

export EDITOR=$commands[nvim]
export VISUAL=$commands[code]

if [[ "$(uname)" == "Darwin" ]]; then
    defaults write -g NSWindowShouldDragOnGesture -bool true
    defaults write NSGlobalDomain AppleHighlightColor -string "0.615686 0.823529 0.454902"
    :
elif [[ "$(expr substr $(uname -s) 1 5)" == "Linux" ]]; then
    ## Do something under GNU/Linux platform
    :
elif [[ "$(expr substr $(uname -s) 1 10)" == "MINGW32_NT" ]]; then
    ## Do something under 32 bits Windows NT platform
    :
elif [[ "$(expr substr $(uname -s) 1 10)" == "MINGW64_NT" ]]; then
    ## Do something under 64 bits Windows NT platform
    :
fi


## [plugins]  ## {{{
## [plugins.zsh-abbr@v6]
builtin source "$ZGEN_DIR/olets/zsh-abbr/v6/zsh-abbr.plugin.zsh"

## [plugins.fast-syntax-highlighting]
[[ -n "${commands[fast - syntax - highlighting]}" ]] && {
    fast-theme q-jmnemonic
}

## [plugins.ssh]
/usr/bin/ssh-add -q --apple-load-keychain --apple-use-keychain "${HOME}/.ssh/id_ed25519"

## [plugins.starship]
[[ -n "${commands[starship]}" ]] && eval "$(starship init zsh)"

## [plugins.zoxide]
[[ -n "${commands[zoxide]}" ]] && eval "$(zoxide init zsh --hook pwd)"

## }}}  ## [plugins]

## [carapace]    ## Carapace shell integration for Zsh. This must be at the top of your zshrc!
[[ -n "${commands[carapace]}" ]] && {
    export CARAPACE_BRIDGES='zsh,fish,bash,inshellisense' # optional
    zstyle ':completion:*' format $'\e[2;37mCompleting %d\e[m'
    builtin source <(carapace _carapace)
}

## [Go] {{{
[ -d ~/go ] && export GOPATH=$HOME/go
[ "$GOPATH" ] && [ -d "$GOPATH/bin" ] && PATH="$PATH:$GOPATH/bin"

if [ -d /usr/local/opt/go/libexec ]
then
    export GOROOT=/usr/local/opt/go/libexec
else
    if [ -d /usr/local/opt/go ]
    then
        export GOROOT=/usr/local/opt/go
    else
        [ -d /usr/local/go ] && export GOROOT=/usr/local/go
    fi
fi
[ -d "${GOROOT}/bin" ] && {
    if [ "$(echo "$PATH" | grep -c "${GOROOT}/bin")" -ne "1" ]; then
        PATH="${GOROOT}/bin:$PATH"
    fi
}
[ -d "$HOME/go/bin" ] && {
    if [ "$(echo "$PATH" | grep -c "$HOME/go/bin")" -ne "1" ]; then
        PATH="$HOME/go/bin:${PATH}"
    fi
}
## }}}

## [jj]
source <(COMPLETE=zsh jj)

## [laravel]
unalias bob

## [rio]
[[ -n "${commands[rio]}" ]] && {
    export RIO_CONFIG="${XDG_CONFIG_HOME}/rio/config"
    export RIO_CACHE="${XDG_CACHE_HOME}/rio"
    if ! infocmp rio &>/dev/null; then
        tempfile=$(mktemp) &&
            curl -o ${tempfile} https://raw.githubusercontent.com/raphamorim/rio/main/misc/rio.terminfo &&
            sudo tic -xe rio ${tempfile} &&
            rm ${tempfile}
    fi
}

## [sqlite] {{{
# sqlite is keg-only, which means it was not symlinked into /opt/homebrew,
# because macOS already provides this software and installing another version in
# parallel can cause all kinds of trouble.

# If you need to have sqlite first in your PATH, run:
#   echo 'export PATH="/opt/homebrew/opt/sqlite/bin:$PATH"' >> /Users/<USER>/.config/zsh/.zshrc
export PATH="/opt/homebrew/opt/sqlite/bin:$PATH"

# For compilers to find sqlite you may need to set:
export LDFLAGS="-L/opt/homebrew/opt/sqlite/lib $LDFLAGS"
export CPPFLAGS="-I/opt/homebrew/opt/sqlite/include $CPPFLAGS"

# For pkg-config to find sqlite you may need to set:
export PKG_CONFIG_PATH="/opt/homebrew/opt/sqlite/lib/pkgconfig"
## }}}

## [wezterm]
[[ -n "${commands[wezterm]}" ]] && {
    if ! infocmp wezterm &>/dev/null; then
        tempfile=$(mktemp) &&
            curl -o ${tempfile} https://raw.githubusercontent.com/wez/wezterm/main/termwiz/data/wezterm.terminfo &&
            sudo tic -xe wezterm ${tempfile} &&
            rm ${tempfile}
    fi
}

## [my_fpath]
[[ -d "${XDG_DATA_HOME}/zsh/functions" ]] && {
    fpath+=("${XDG_DATA_HOME}/zsh/functions")
    #_field_append FPATH "$XDG_DATA_HOME/zsh/functions"
    FPATH="${FPATH}:${XDG_DATA_HOME}/zsh/functions"
}

rm -f "${ZDOTDIR:-${HOME:?No ZDOTDIR or HOME}}/.zcompdump" \
    && rm -f "${ZSH_COMPDUMP}" \
    && compinit -d "${ZSH_COMPDUMP}"

## [update-all]
[[ -s "$XDG_CONFIG_HOME/zsh/update-all.zsh" ]] && source "$XDG_CONFIG_HOME/zsh/update-all.zsh"

eval "$(uv generate-shell-completion zsh)"
source "$HOME/.venv/bin/activate"

defaults write com.apple.desktopservices DSDontWriteNetworkStores true


builtin source "${HOME}/.local/share/cargo/env"


eval "$(brew shellenv)"

export NVM_DIR="$HOME/Library/Application Support/Herd/config/nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion


# Console Ninja
_path_prepend "${HOME}/.console-ninja/.bin"

# Turso
_path_prepend "${HOME}/.turso"


# bun completions
[ -s "${XDG_DATA_HOME}/bun/_bun" ] && source "${XDG_DATA_HOME}/bun/_bun"

# The next line updates PATH for the Google Cloud SDK.
if [ -f "${XDG_DATA_HOME}/google-cloud-sdk/path.zsh.inc" ]; then source "${XDG_DATA_HOME}/google-cloud-sdk/path.zsh.inc"; fi

# The next line enables shell command completion for gcloud.
if [ -f "${XDG_DATA_HOME}/google-cloud-sdk/completion.zsh.inc" ]; then source "${XDG_DATA_HOME}/google-cloud-sdk/completion.zsh.inc"; fi

# pnpm
export PNPM_HOME="/Users/<USER>/.local/share/pnpm"
case ":$PATH:" in
  *":$PNPM_HOME:"*) ;;
  *) export PATH="$PNPM_HOME:$PATH" ;;
esac
# pnpm end

# The following lines have been added by Docker Desktop to enable Docker CLI completions.
fpath=("/Users/<USER>/.docker/completions" $fpath)
autoload -Uz compinit
compinit
# End of Docker CLI completions

# Added by LM Studio CLI (lms)
export PATH="$PATH:/Users/<USER>/.lmstudio/bin"

# MCP Environment Setup for AugmentCode
source ~/.mcp-environment.sh

# Source the Lazyman shell initialization for aliases and nvims selector
# shellcheck source=.config/nvim-Lazyman/.lazymanrc
[ -f ~/.config/nvim-Lazyman/.lazymanrc ] && source ~/.config/nvim-Lazyman/.lazymanrc
# Source the Lazyman .nvimsbind for nvims key binding
# shellcheck source=.config/nvim-Lazyman/.nvimsbind
[ -f ~/.config/nvim-Lazyman/.nvimsbind ] && source ~/.config/nvim-Lazyman/.nvimsbind



# Herd injected PHP 8.2 configuration.
export HERD_PHP_82_INI_SCAN_DIR="/Users/<USER>/Library/Application Support/Herd/config/php/82/"

# Herd injected PHP 8.3 configuration.
export HERD_PHP_83_INI_SCAN_DIR="/Users/<USER>/Library/Application Support/Herd/config/php/83/"

# Herd injected PHP 8.4 configuration.
export HERD_PHP_84_INI_SCAN_DIR="/Users/<USER>/Library/Application Support/Herd/config/php/84/"

# Herd injected PHP 8.5 configuration.
export HERD_PHP_85_INI_SCAN_DIR="/Users/<USER>/Library/Application Support/Herd/config/php/85/"
_path_prepend \
    "$HOME/Library/Application Support/Herd" \
    "$HOME/Library/Application Support/Herd/bin"

eval "$(uv generate-shell-completion zsh)"


eval "$(uvx generate-shell-completion zsh)"

