[1;33mdiff --git setup-a-new-machine.sh setup-a-new-machine.sh[m
[1;33mindex 7b5996c..67eec2a 100755[m
[1;33m--- setup-a-new-machine.sh[m
[1;33m+++ setup-a-new-machine.sh[m
[1;35m@@ -30,6 +30,7 @@[m [mcp -R ~/.gnupg ~/migration/home[m
 cp /Library/Preferences/SystemConfiguration/com.apple.airport.preferences.plist ~/migration  # wifi[m
 [m
 cp ~/Library/Preferences/net.limechat.LimeChat.plist ~/migration[m
[1;32m+[m[1;32mcp ~/Library/Preferences/com.tinyspeck.slackmacgap.plist ~/migration[m
 [m
 cp -R ~/Library/Services ~/migration # automator stuff[m
 [m
[1;35m@@ -215,5 +216,7 @@[m [msh .osx[m
 # symlink it up![m
 ./symlink-setup.sh[m
 [m
[1;32m+[m[1;32m# add manual symlink for .ssh/config and probably .config/fish[m
[1;32m+[m
 ###[m
 ##############################################################################################################[m
