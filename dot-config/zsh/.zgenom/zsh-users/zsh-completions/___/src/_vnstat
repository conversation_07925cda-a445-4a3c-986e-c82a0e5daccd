#compdef vnstat
# ------------------------------------------------------------------------------
# Copyright (c) 2012 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for vnstat 2.10
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

local curcontext="$curcontext" state state_descr line ret=1
typeset -A opt_args

local period="-d --days -h --hours -m --months -w --weeks"

_arguments -C : \
  '--add[create database entry for interface specified]' \
  '(-b --begin)'{-b,--begin}'[begin the list output with a specific date]:date:' \
  '--config[specify alternate configuration file]:file:_files' \
  "($period)"{-d,--days}'[show traffic for days]' \
  "($period)"{-h,--hours}'[show traffic for last 24 hours]' \
  "($period)"{-m,--months}'[show traffic for months]' \
  "($period)"{-w,--weeks}'[show traffic for 7 days]' \
  "($period)"{-y,--years}'[show traffic statistics for the last year]' \
  "($period)"{-5,--fiveminutes}'[show traffic statistic with a 5 minute resolution for the last hours]' \
  '--dbdir[specify database directory]:directory:_files -/' \
  '--dbiflist[List interfaces currently in the database]::mode:->dbiflist' \
  '(-D --debug)'{-D,--debug}'[show additional debug output]' \
  '(-e --end)'{-e,--end}'[end the list output with a specific date]:date:' \
  '--delete[delete database and stop monitoring selected interface]' \
  '--dumpdb[dump database in parseable format]' \
  '(--disable)--enable[enable updates for selected interface]' \
  '(--enable)--disable[disable updates for selected interface]' \
  '(-hg --hoursgraph)'{-hg,--hoursgraph}'[show traffic statistics using a bar graph]' \
  '(-i --iface)'{-i,--iface}'[specify interface for actions]:interface:->interface' \
  '--iflist[list available interfaces]' \
  '--limit[set the maximum number of shown entries]:limit:' \
  '(-l --live)'{-l,--live}'[display current transfer rate]:mode:->live' \
  '--json[show database content in json format]' \
  '--locale[specify locale]:locale:' \
  '--nick[set nickname for alias]:nickname:' \
  '--oneline[traffic summary in one-line, parseable format]' \
  '(-q --query)'{-q,--query}'[force database query mode]' \
  '--remove[delete the database entry for the interface specified]' \
  '--rename[rename the interface specified in the database with new name]' \
  '(-r --reset)'{-r,--reset}'[reset internal counters]' \
  '--setalias[set alias as an alias for the selected interface]:alias:' \
  '(-ru --rateunit)'{-ru,--rateunit}'[swap configured rate unit]' \
  '(-s --short)'{-s,--short}'[use short output mode]' \
  '--showconfig[show current configuration]' \
  '--style[modify content and style of output]:style:->style' \
  '--sync[synchronize internal counters]' \
  '--testkernel[test kernel boot time information]' \
  '(-t --top)'{-t,--top}'[show all time top traffic days]' \
  '(-tr --traffic)'{-tr,--traffic}'[calculate amount of traffic in given time]:seconds:' \
  '--xml[show database content in XML format]' \
  '(-)'{-v,--version}'[show current version]' \
  '(-)'{-\?,--help}'[show command summary]' \
  '(-)--longhelp[show complete options list]' \
  && ret=0

case "$state" in
  (live)
    _values 'live mode' \
      '0[packets per second]' \
      '1[traffic counters]' && ret=0
    ;;
  (style)
    _values 'output style' \
      '0[narrow output]' \
      '1[enable bar column]' \
      '2[bar column with average traffic rate]' \
      '3[enable average traffic rate]' \
      '4[disable use of terminal control characters]' && ret=0
    ;;
  (interface)
    local interfaces="$(_call_program interfaces 'vnstat --iflist')"
    interfaces="${interfaces#*:}"
    _values -s + 'interface' ${(z)interfaces} && ret=0
    ;;
  (dbiflist)
    _values 'mode' \
      '0[one line verbose format]' \
      '1[one interface per line]' \
      && ret=0
    ;;
esac

return ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: filetype=zsh shiftwidth=2 tabstop=2 expandtab
