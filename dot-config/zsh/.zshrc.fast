#!/opt/homebrew/bin/zsh
# ==============================================================================
# Ultra-Fast ZSH Configuration
# ==============================================================================
# Purpose: Complete ZSH configuration optimized for <300ms startup time
# Author: ZSH Configuration Management System
# Created: 2025-08-21
# Version: 1.0
# Target: <300ms startup time with essential functionality
# Usage: Replace .zshrc with this file for maximum performance
# ==============================================================================

# Performance flags
export ZSH_DEBUG=0
export ZSH_DISABLE_COMPFIX=true
export ZSH_PERFORMANCE_MODE=true

# Set ZDOTDIR
export ZDOTDIR="${ZDOTDIR:-$HOME/.config/zsh}"

# ==============================================================================
# CORE ESSENTIALS (Target: <50ms)
# ==============================================================================

# 1. Essential environment variables
export EDITOR="${EDITOR:-vim}"
export PAGER="${PAGER:-less}"
export LANG="${LANG:-en_US.UTF-8}"

# 2. Essential PATH setup
typeset -U path
path=(
    /opt/homebrew/bin
    /opt/homebrew/sbin
    /usr/local/bin
    /usr/bin
    /bin
    /usr/sbin
    /sbin
    $path
)

# 3. History configuration
HISTFILE="$ZDOTDIR/.zsh_history"
HISTSIZE=10000
SAVEHIST=10000
setopt HIST_IGNORE_DUPS
setopt HIST_IGNORE_SPACE
setopt SHARE_HISTORY
setopt APPEND_HISTORY
setopt INC_APPEND_HISTORY

# 4. Essential ZSH options
setopt AUTO_CD
setopt CORRECT
setopt NO_BEEP
setopt EXTENDED_GLOB
setopt PROMPT_SUBST

# ==============================================================================
# MINIMAL COMPLETION SYSTEM (Target: <100ms)
# ==============================================================================

# 5. Fast completion initialization
autoload -Uz compinit
# Skip security check for speed (use -C flag)
if [[ -n ${ZDOTDIR}/.zcompdump(#qN.mh+24) ]]; then
    compinit
else
    compinit -C
fi

# 6. Essential completion settings
zstyle ':completion:*' menu select
zstyle ':completion:*' matcher-list 'm:{a-zA-Z}={A-Za-z}'
zstyle ':completion:*' list-colors ''

# ==============================================================================
# FAST PROMPT (Target: <150ms)
# ==============================================================================

# 7. Simple, fast prompt with git info
autoload -Uz vcs_info
zstyle ':vcs_info:*' enable git
zstyle ':vcs_info:*' formats ' (%b)'
zstyle ':vcs_info:*' actionformats ' (%b|%a)'

precmd() {
    vcs_info
}

PROMPT='%F{blue}%~%f%F{green}${vcs_info_msg_0_}%f %# '

# ==============================================================================
# ESSENTIAL KEY BINDINGS (Target: <200ms)
# ==============================================================================

# 8. Essential key bindings
bindkey '^R' history-incremental-search-backward
bindkey '^[[A' up-line-or-history
bindkey '^[[B' down-line-or-history
bindkey '^[[C' forward-char
bindkey '^[[D' backward-char
bindkey '^[[H' beginning-of-line
bindkey '^[[F' end-of-line

# ==============================================================================
# ESSENTIAL ALIASES (Target: <250ms)
# ==============================================================================

# 9. Essential aliases
alias ls='ls -G'
alias ll='ls -la'
alias la='ls -A'
alias l='ls -CF'
alias grep='grep --color=auto'
alias ..='cd ..'
alias ...='cd ../..'
alias ....='cd ../../..'

# Git aliases
alias g='git'
alias ga='git add'
alias gc='git commit'
alias gco='git checkout'
alias gd='git diff'
alias gl='git log'
alias gp='git push'
alias gs='git status'

# ==============================================================================
# LAZY LOADING SYSTEM (Target: <300ms)
# ==============================================================================

# 10. Lazy loading for heavy features
_lazy_load_nvm() {
    if [[ -f "/opt/homebrew/opt/nvm/nvm.sh" ]]; then
        source "/opt/homebrew/opt/nvm/nvm.sh"
        [[ -f "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm" ]] && \
            source "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm"
    fi
    unfunction _lazy_load_nvm
}

# Override node/npm commands to trigger NVM loading
node() { _lazy_load_nvm; node "$@"; }
npm() { _lazy_load_nvm; npm "$@"; }
npx() { _lazy_load_nvm; npx "$@"; }
nvm() { _lazy_load_nvm; nvm "$@"; }

# 11. Lazy SSH agent setup
_lazy_ssh_agent() {
    if [[ -z "$SSH_AUTH_SOCK" ]] || ! ssh-add -l >/dev/null 2>&1; then
        eval "$(ssh-agent -s)" >/dev/null 2>&1
        if [[ -f "$HOME/.ssh/id_ed25519" ]]; then
            ssh-add "$HOME/.ssh/id_ed25519" >/dev/null 2>&1
        fi
    fi
    unfunction _lazy_ssh_agent
}

# Override SSH commands to trigger agent setup
ssh() { _lazy_ssh_agent; command ssh "$@"; }
scp() { _lazy_ssh_agent; command scp "$@"; }
sftp() { _lazy_ssh_agent; command sftp "$@"; }

# 12. Lazy loading for syntax highlighting and autosuggestions
_lazy_load_plugins() {
    # Load zsh-autosuggestions if available
    if [[ -f "/opt/homebrew/share/zsh-autosuggestions/zsh-autosuggestions.zsh" ]]; then
        source "/opt/homebrew/share/zsh-autosuggestions/zsh-autosuggestions.zsh"
    fi
    
    # Load zsh-syntax-highlighting if available (must be last)
    if [[ -f "/opt/homebrew/share/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh" ]]; then
        source "/opt/homebrew/share/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh"
    fi
    
    unfunction _lazy_load_plugins
}

# Trigger plugin loading after first command
precmd_functions+=(_lazy_load_plugins)

# ==============================================================================
# BACKGROUND LOADING
# ==============================================================================

# 13. Load additional features in background (non-blocking)
{
    sleep 0.1
    
    # Load additional completions if available
    if [[ -d "/opt/homebrew/share/zsh/site-functions" ]]; then
        fpath=(/opt/homebrew/share/zsh/site-functions $fpath)
    fi
    
    # Load any additional custom functions
    if [[ -d "$ZDOTDIR/functions" ]]; then
        fpath=($ZDOTDIR/functions $fpath)
        autoload -Uz $ZDOTDIR/functions/*(:t)
    fi
    
} &

# ==============================================================================
# WELCOME MESSAGE
# ==============================================================================

# 14. Simple welcome message
if [[ -o interactive ]]; then
    echo "🚀 Ultra-fast ZSH loaded! Type 'help' for available commands."
fi

# 15. Help function
help() {
    echo "Ultra-Fast ZSH Configuration"
    echo "============================"
    echo "Essential aliases:"
    echo "  ls, ll, la, l     - File listing"
    echo "  g, ga, gc, gs     - Git shortcuts"
    echo "  .., ..., ....     - Directory navigation"
    echo ""
    echo "Lazy-loaded features:"
    echo "  node, npm, nvm    - Node.js (loads NVM on first use)"
    echo "  ssh, scp, sftp    - SSH (sets up agent on first use)"
    echo ""
    echo "Performance mode: Startup time optimized for <300ms"
}

# ==============================================================================
# END: Ultra-Fast ZSH Configuration
# ==============================================================================
