#compdef bitcoin-cli
# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for bitcoin-cli (https://bitcoin.org).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Ian Ker-Seymer (https://github.com/ianks)
#  * notmike
# ------------------------------------------------------------------------------

_bitcoin-cli() {
  local context state line curcontext="$curcontext"

  _arguments -C \
    -?'[This help message]' \
    -addrinfo'[Get the number of addresses known to the node, per network and total, after filtering for quality and recency. Total number of addresses known to the node may be higher.]' \
    -chain='[Use the chain <chain> (default: main). Allowed values: main, test, signet, regtest]:chain:(main test signet regtest)' \
    -color='[Color setting for CLI output (default: auto). Valid values: always, auto, never]:when:(always auto never)' \
    -conf='[Specify configuration file. Relative paths will be prefixed by datadir location. (default: bitcoin.conf)]:PATH:_files' \
    -datadir='[Specify data directory]:PATH:_directories' \
    -getinfo='[Get general information from the remote server.]' \
    -testnet'[Use the test chain]' \
    -regtest'[Enter regression test mode, which uses a special chain in which blocks can be solved instantly. This is intended for regression testing tools and app development.]' \
    -named'[Pass named instead of positional arguments (default: false)]' \
    -netinfo=-'[Get network peer connection information from the remote server.]::level_or_help:(0 1 2 3 4 help)' \
    -stdin'[Read extra arguments from standard input, one per line until EOF/Ctrl-D (recommended for sensitive information such as passphrases)]' \
    -rpcport='[Connect to JSON-RPC on <port> (default: 8332, testnet: 18332, regtest: 18443)]: :_guard "[[\:digit\:]]#" "PORT"' \
    -rpcwait'[Wait for RPC server to start]' \
    -rpcuser='[Username for JSON-RPC connections]:RPCUSER:()' \
    -rpcpassword='[Password for JSON-RPC connections]:RPCPASSWORD:()' \
    -rpcconnect='[Send commands to node running on <ip> (default: 127.0.0.1)]:RPCCONNECT:_hosts' \
    -rpcclienttimeout='[Timeout during HTTP requests, or 0 for no timeout. (default: 900)]: :_guard "[[\:digit\:]]#" "RPCCLIENTTIMEOUT"' \
    -version'[Print version and exit]' \
    ':subcommand:->subcommand' && ret=0

  case $state in
    subcommand)
    subcommands=(
      'getbestblockhash'
      'getblock'
      'getblockchaininfo'
      'getblockcount'
      'getblockfilter'
      'getblockfrompeer'
      'getblockhash'
      'getblockheader'
      'getblockstats'
      'getchaintips'
      'getchaintxstats'
      'getdeploymentinfo'
      'getdifficulty'
      'getmempoolancestors'
      'getmempooldescendants'
      'getmempoolentry'
      'getmempoolinfo'
      'getrawmempool'
      'gettxout'
      'gettxoutproof'
      'gettxoutsetinfo'
      'gettxspendingprevout'
      'preciousblock'
      'pruneblockchain'
      'savemempool'
      'scantxoutset'
      'verifychain'
      'verifytxoutproof'
      'getmemoryinfo'
      'getrpcinfo'
      'help'
      'logging'
      'stop'
      'uptime'
      'getblocktemplate'
      'getmininginfo'
      'getnetworkhashps'
      'prioritisetransaction'
      'submitblock'
      'submitheader'
      'addnode'
      'clearbanned'
      'disconnectnode'
      'getaddednodeinfo'
      'getconnectioncount'
      'getnettotals'
      'getnetworkinfo'
      'getnodeaddresses'
      'getpeerinfo'
      'listbanned'
      'ping'
      'setban'
      'setnetworkactive'
      'analyzepsbt'
      'combinepsbt'
      'combinerawtransaction'
      'converttopsbt'
      'createpsbt'
      'createrawtransaction'
      'decodepsbt'
      'decoderawtransaction'
      'decodescript'
      'finalizepsbt'
      'fundrawtransaction'
      'getrawtransaction'
      'joinpsbts'
      'sendrawtransaction'
      'signrawtransactionwithkey'
      'testmempoolaccept'
      'utxoupdatepsbt'
      'enumeratesigners'
      'createmultisig'
      'deriveaddresses'
      'estimatesmartfee'
      'getdescriptorinfo'
      'signmessagewithprivkey'
      'validateaddress'
      'verifymessage'
      'abandontransaction'
      'abortrescan'
      'addmultisigaddress'
      'backupwallet'
      'bumpfee'
      'createwallet'
      'dumpprivkey'
      'dumpwallet'
      'encryptwallet'
      'getaddressesbylabel'
      'getaddressinfo'
      'getbalance'
      'getbalances'
      'getnewaddress'
      'getrawchangeaddress'
      'getreceivedbyaddress'
      'getreceivedbylabel'
      'gettransaction'
      'getunconfirmedbalance'
      'getwalletinfo'
      'importaddress'
      'importdescriptors'
      'importmulti'
      'importprivkey'
      'importprunedfunds'
      'importpubkey'
      'importwallet'
      'keypoolrefill'
      'listaddressgroupings'
      'listdescriptors'
      'listlabels'
      'listlockunspent'
      'listreceivedbyaddress'
      'listreceivedbylabel'
      'listsinceblock'
      'listtransactions'
      'listunspent'
      'listwalletdir'
      'listwallets'
      'loadwallet'
      'lockunspent'
      'migratewallet'
      'newkeypool'
      'psbtbumpfee'
      'removeprunedfunds'
      'rescanblockchain'
      'restorewallet'
      'send'
      'sendall'
      'sendmany'
      'sendtoaddress'
      'sethdseed'
      'setlabel'
      'settxfee'
      'setwalletflag'
      'signmessage'
      'signrawtransactionwithwallet'
      'simulaterawtransaction'
      'unloadwallet'
      'upgradewallet'
      'walletcreatefundedpsbt'
      'walletdisplayaddress'
      'walletlock'
      'walletpassphrase'
      'walletpassphrasechange'
      'walletprocesspsbt'
      'getzmqnotifications'
    )

    _describe -t subcommands 'bitcoin-cli subcommands' subcommands && ret=0
  esac

  return ret
}

_bitcoin-cli "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
