#compdef emacsclient
#
# ------------------------------------------------------------------------------
# Copyright (c) 2022 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for emacsclient 29
#  - https://www.gnu.org/software/emacs/manual/html_node/emacs/Invoking-emacsclient.html
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Shohei YOSHIDA (https://github.com/syohex)
#
# ------------------------------------------------------------------------------

_arguments -s -n : \
  '(- *)'{-V,--version}'[Print version information and exit]' \
  '(- *)'{-H,--help}'[Print this usage information message and exit]' \
  '(-nw -t --tty)'{-nw,-t,--tty}'[Open a new Emacs frame on the current terminal]' \
  '(-c --create-frame)'{-c,--create-frame}'[Create a new frame instead of trying to use the current Emacs frame]' \
  '(-r --reuse-frame)'{-r,--reuse-frame}'[Create a new frame if none exists, otherwise use the current Emacs frame]' \
  '(-F --frame-parameters)'{-F,--frame-parameters=}'[Set the parameters of a new frame]:alist' \
  '(-e --eval)'{-e,--eval}'[Evaluate as Emacs Lisp expressions]' \
  '(-n --no-wait)'{-n,--no-wait}'[Returns immediately without waiting for finish]' \
  '(-w --timeout)'{-w,--timeout=}'[Seconds to wait before timing out]:seconds' \
  '(-q --quiet)'{-q,--quiet}"[Don't display messages on success]" \
  '(-u --suppress-output)'{-u,--suppress-output}"[Don't display return values from the server]" \
  '(-d --display)'{-d,--display=}'[Tell the server to display the files on the given display]:display:_x_display' \
  '--parent-id=[Open in parent window ID, via XEmbed]' \
  '(-s --socket-name)'{-s,--socket-name=}'[Set filename of the UNIX socket for communication]: :_files' \
  '(-f --server-file)'{-f,--server-file=}'[Set filename of the TCP authentication file]: :_files' \
  '(-a --alternate-editor)'{-a,--alternate-editor=}'[Editor to fallback to if the server is not running if EDITOR is the empty string]:editor:_files' \
  '(-T --tramp)'{-T,--tramp=}'[PREFIX to prepend to filenames sent by emacsclient for locating files remotely via Tramp]:prefix' \
  '*: :_files'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
