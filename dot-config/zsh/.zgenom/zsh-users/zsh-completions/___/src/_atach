#compdef atach
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for atach (https://github.com/sorin-ionescu/atach).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

local state mode_values existing_sessions ret=1

mode_values=(
  "none:disable redrawing"
  "ctrl_l:use ctrl + l to redraw"
  "winch:use sigwinch to redraw"
)

existing_sessions=($(_call_program session atach))

_arguments -C -s -S \
  '(--list -l)'{--list,-l}'[list sessions]' \
  '(--sockets -L)'{--sockets,-L}'[list sockets]' \
  '(--session -s)'{--session=,-s+}'[set the session name]:session' \
  '(--char -c)'{--char=,-c+}'[set the detach character (default: ^\\)]:char' \
  '(--redraw -r)'{--redraw=,-r+}'[set the redraw method (none, ctrl_l, or winch)]:mode:->mode' \
  '(--detached -d)'{--detached,-d}'[start the session detached]' \
  '(--no-detach -D)'{--no-detach,-D}'[disable detaching]' \
  '(--no-suspend -Z)'{--no-suspend,-Z}'[disable suspending]' \
  '(--version -v)'{--version,-v}'[display version and copyright]' \
  '(--help -h)'{--help,-h}'[display help]' \
  '(-)::args:->session-or-command' && ret=0

case "$state" in
  (mode)
    _describe -t mode 'redraw mode' mode_values && ret=0
  ;;
  (session-or-command)
    _describe -t 'session' 'sessions' existing_sessions && ret=0
    _path_commands && ret=0
  ;;
esac

return $ret

