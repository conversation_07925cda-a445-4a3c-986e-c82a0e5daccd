#compdef pkcon
# ------------------------------------------------------------------------------
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for pkcon (https://www.freedesktop.org/software/PackageKit/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------


local -a command_options
command_options=(
  '--version[Show the program version and exit]'
  '--filter[Set the filter, e.g. installed]'
  "--root[Set the install root, e.g. '/' or '/mnt/ltsp']"
  '(-n --nowait)'{-n,--nowait}'[Exit without waiting for actions to complete]'
  '(-y --noninteractive)'{-g,--noninteractive}'[Install the packages without asking for confirmation]'
  '--background[Run the command using idle network bandwidth and also using less power]'
  '(-p --plain)'{-p,--plain}'[Print to screen a machine readable output, rather than using animated widgets]'
  '(-c --cache-age)'{-c,--cache-age}"[The maximum metadata cache age. Use -1 for 'never'.]"
  '(-h --help)'{-h,--help}'[Show help options.]'
  '(-v --verbose)'{-v,--verbose}'[Show debugging information for all files]'
)

local -a actions
actions=(
  'accept-eula'
  'get-roles'
  'get-distro-upgrades'
  'get-categories'
  'get-actions'
  'get-groups'
  'get-filters'
  'get-transactions'
  'get-time'
  'search'
  'install'
  'install-local'
  'download'
  'remove'
  'update'
  'refresh'
  'resolve'
  'get-updates'
  'get-depends'
  'get-requires'
  'get-details'
  'get-files'
  'get-update-detail'
  'get-packages'
  'repo-list'
  'repo-enable'
  'repo-disable'
  'repo-set-data'
  'what-provides'
  'upgrade-system'
)

local context state line expl cmd
local -A opt_args

integer i=2
while (( i < $#words )); do
  case "$words[$i]" in
    -*)
      # skip option
      (( i++ ))
      continue
    ;;
  esac

  if [[ -z "$cmd" ]]; then
    cmd="$words[$i]"
    words[$i]=()
    (( CURRENT-- ))
  fi
  (( i++ ))
done

if [[ -z "$cmd" ]]
then
  _arguments -s -w : $command_options \
    ":action:($actions)"
  return
fi

case "$cmd" in
  search)
    _arguments : $command_options \
      ':type:(name details group file)' \
      ':data: :'
  ;;
  refresh)
    _arguments -s -w : $command_options \
      '--force'
  ;;
  *)
    _arguments -s -w : $command_options
  ;;
esac
return 1

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
