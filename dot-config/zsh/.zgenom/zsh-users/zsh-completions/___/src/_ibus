#compdef ibus
# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for ibus 1.5.27 (https://github.com/ibus/ibus).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Tomo Kazahaya <https://github.com/tomonacci>
#
# ------------------------------------------------------------------------------

# The completion of "ibus emoji --lang=" depends on _language_codes from
# https://github.com/zsh-users/zsh-completions.

local context state state_descr line
typeset -A opt_args
local curcontext=$curcontext

_arguments -C \
  ":command:(($(ibus help|sed -ne 's/^  \(\S\+\) \+\(.*\)/"\1\\:\2"/p')))" \
  '*:: :->args' \
  && return

case $state in
  args)
    case $line[1] in
      (engine)
        _arguments \
          ":engine:(($(ibus list-engine|sed -ne 's/:/\\\\:/g' -e 's/^  \(\S\+\) - \(.*\)$/"\1:\2"/p')))" \
          && return
        ;;
      (start|restart)
        _arguments \
          '--type=[start or restart daemon type with direct or systemd type]: :(direct systemd)' \
          '--file=[start or restart daemon with SYSTEMD_SERVICE file]: :_files' \
          '--verbose[Show debug message]' \
          '(- *)--help[Show help message]' \
          && return
        ;;
      (read-cache)
        _arguments \
          '--system[show the content of the system registry cache]' \
          '--file=[custom registry cache to show]:registry cache:_files' \
          '(- *)--help[Show help message]' \
          && return
        ;;
      (write-cache)
        _arguments \
          '--system[save the system registry cache]' \
          '--file=[custom registry cache to save]:registry cache:_files' \
          '(- *)--help[Show help message]' \
          && return
        ;;
      (emoji)
        _arguments \
          '--font=[emoji font]:emoji font: ' \
          '--lang=[language of emoji annotations]:language:_language_codes ISO-639-1' \
          '--partial-match[match annotations with a partial string]' \
          '(- *)--help[Show help message]' \
          && return
        ;;
      (im-module)
        _arguments \
          '--type=[Set im-module TYPE]: :(gtk2 gtk3 gtk4)' \
          '(- *)--help[Show help message]' \
          && return
        ;;
    esac
    ;;
esac

return 1

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
