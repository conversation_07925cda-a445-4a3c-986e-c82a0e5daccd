# catimg

Plugin for displaying images on the terminal using the `catimg.sh` script provided by
[posva](https://github.com/posva/catimg)

To use it, add `catimg` to the plugins array in your zshrc file:

```zsh
plugins=(... catimg)
```

## Requirements

- `magick convert` (ImageMagick)

## Functions

| Function | Description                              |
| -------- | ---------------------------------------- |
| `catimg` | Displays the given image on the terminal |

## Usage examples

[![asciicast](https://asciinema.org/a/204702.png)](https://asciinema.org/a/204702)
