#!/usr/bin/env zsh

function __zgenom_prezto_option() {
    local module=${1}
    shift
    local option=${1}
    shift
    local params
    params=${@}
    if [[ ${module} =~ "^:" ]]; then
        module=${module[1,]}
    fi
    if [[ ! $module =~ "^(\*|module|prezto:module):" ]]; then
        module="module:$module"
    fi
    if [[ ! $module =~ "^(prezto):" ]]; then
        module="prezto:$module"
    fi
    local cmd="zstyle ':${module}' $option ${params}"

    # execute in place
    eval $cmd

    if [[ ! "${ZGEN_PREZTO_OPTIONS[@]}" =~ "${cmd}" ]]; then
        ZGEN_PREZTO_OPTIONS+=("${cmd}")
    fi
}

function zgenom-prezto() {
    local repo="$ZGEN_PREZTO_REPO"
    local file="${1:-init.zsh}"

    # load prezto itself
    if [[ $# == 0 ]]; then
        ZGEN_USE_PREZTO=1
        zgenom-load "${repo}" "${file}" "${ZGEN_PREZTO_BRANCH}"
        if [[ ${ZGEN_PREZTO_LOAD_DEFAULT} != 0 ]]; then
            __zgenom_prezto_load "'environment' 'terminal' 'editor' 'history' 'directory' 'spectrum' 'utility' 'completion' 'prompt'"
        fi

    # this is a prezto module
    elif [[ $# == 1 ]]; then
        local module=${file}
        if [[ -z ${file} ]]; then
            __zgenom_err 'Please specify which module to load using `zgenom prezto <name of module>`'
            return 1
        fi
        __zgenom_prezto_load "'$module'"

    # this is a prezto option
    else
        shift
        __zgenom_prezto_option ${file} ${(qq)@}
    fi
}

zgenom-prezto $@
