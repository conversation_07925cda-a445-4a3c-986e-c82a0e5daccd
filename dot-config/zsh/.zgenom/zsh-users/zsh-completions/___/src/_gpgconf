#compdef gpgconf
# Copyright (c) 2021 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

# Description:
#     Completions for gpgconf (configuration utility provided with GnuPG 2.4.4
#     <https://gnupg.org>)
#
# Authors: <AUTHORS>

_gpgconf_component() {
  local values=(
    "${(@f)$(gpgconf --list-components | awk -F: '{printf "%s[%s]\n", $1, $2}')}"
  )
  if [ "${action[2]}" = all ]; then
    values+=('all[All daemon components]')
  fi

  _values 'component' "${(o)values[@]}"
}

_arguments \
    '--help[print help text]' \
    '--list-components[list all components]' \
    '--check-programs[check all programs]' \
    '--apply-defaults[apply global default values]' \
    '--list-dirs[get the configuration directories for gpgconf]' \
    '--list-config[list global configuration file]' \
    '--check-config[check global configuration file]' \
    '--query-swdb[query the software version database]' \
    '--reload[reload all or a given component]:component:_gpgconf_component all' \
    '--launch[launch a given component]:component:_gpgconf_component all' \
    '--kill[kill a given component]:component:_gpgconf_component all' \
    '--create-socketdir[create a directory for sockets below /run/user or /var/run/user]' \
    '--remove-socketdir[remove a directory created with command --create-socketdir]' \
    '--list-options[list options]:component:_gpgconf_component' \
    '--change-options[change options]:component:_gpgconf_component' \
    '--check-options[check options]:component:_gpgconf_component' \
    '--apply-profile[update configuration files using the specified file]:configuration file:_path_files' \
    '--status-fd[write status info to the specified file descriptor]:file descriptor' \
    '--homedir[specify an alternative gnupg configuration home directory]:directory:_directories' \
    '--chuid[change the current user to uid which may either be a number or a name]:uid' \
    '(--lock --unlock)--unlock[remove a stale lock file hold for file]:name' \
    '(--lock --unlock)--lock[name of file protected by the lock]:name' \
    '(-o --output)'{-o,--output}'[write output to the specified file]:output file:_path_files' \
    '(-v --verbose)'{-v,--verbose}'[verbose]' \
    '(-q --quiet)'{-q,--quiet}'[quiet]' \
    '(-n --dry-run)'{-n,--dry-run}'[do not make any changes]' \
    '(-r --runtime)'{-r,--runtime}'[activate changes at runtime, if possible]'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
