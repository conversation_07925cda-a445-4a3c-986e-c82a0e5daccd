name: Release to docker hub

on:
  release: { types: [published] }
  workflow_dispatch:
    inputs:
        version:
          description: 'Version to simulate for deploy'
          required: true

jobs:
    dockerhub:
        runs-on: ubuntu-latest
        steps:
        - uses: actions/checkout@v2
        - id: version
          run: |
            EXPECTED_VERSION=${{ github.event.inputs.version }}
            TAG_VERSION=${GITHUB_REF#refs/tags/v} # refs/tags/v1.2.3 -> 1.2.3
            echo ::set-output name=version::${EXPECTED_VERSION:-$TAG_VERSION}
        -
          name: Set up QEMU
          uses: docker/setup-qemu-action@v1
        -
          name: Login to DockerHub
          uses: docker/login-action@v1 
          with:
            username: ${{ secrets.DOCKER_USERNAME }}
            password: ${{ secrets.DOCKER_PASSWORD }}
        -
          name: Set up Docker Buildx
          id: buildx
          uses: docker/setup-buildx-action@v1
        - uses: docker/build-push-action@v2
          with:
            platforms: linux/amd64,linux/arm64,linux/ppc64le,linux/s390x,linux/386,linux/arm/v7,linux/arm/v6
            tags: ${{ secrets.DOCKER_USERNAME }}/bats:${{ steps.version.outputs.version }},${{ secrets.DOCKER_USERNAME }}/bats:latest
            push: true
