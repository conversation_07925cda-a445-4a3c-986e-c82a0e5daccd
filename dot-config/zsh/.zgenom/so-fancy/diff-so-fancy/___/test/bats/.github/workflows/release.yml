name: Release

on:
  release: { types: [published] }
  workflow_dispatch:

jobs:
  npmjs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          registry-url: "https://registry.npmjs.org"
      - run: npm publish --ignore-scripts
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  github-npm:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          registry-url: "https://npm.pkg.github.com"
      - name: scope package name as required by GHPR
        run: npm init -y --scope ${{ github.repository_owner }}
      - run: npm publish --ignore-scripts
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
