#!/bin/zsh
#
# Only including a shebang to trigger editors shell syntax highlighting
#
# Copyright 2006-2022 <PERSON> <<EMAIL>>
#
# BSD licensed, see LICENSE.txt
#
# If you want to customize these, the best thing to do is override them
# with a shell fragment in ~/.zshrc.d, then you don't have to maintain
# your own fork of the quickstart kit

function exists() {
  if (( $+commands[$1] )); then return 0; else return 1; fi
}

# from <EMAIL>
function ff() {
  find . -type f -iname '*'$@'*' -ls
}

function hgrep-full() {
  if can_haz egrep; then
    history | egrep --color -i "$@"
  else
    history | grep -i "$@"
  fi
}

# hgrep-full is easier to type, leaving hgrep_full for backwards compatibility
function hgrep_full() {
  echo "hgrep_full is deprecated, use hgrep-full"
  hgrep-full "$@"
}

function envgrep() {
  printenv | grep -i "$@"
}

# From <PERSON>'s blog - http://danryan.co/using-antigen-for-zsh.html
function man() {
  env \
    LESS_TERMCAP_mb=$(printf "\e[1;31m") \
    LESS_TERMCAP_md=$(printf "\e[1;31m") \
    LESS_TERMCAP_me=$(printf "\e[0m") \
    LESS_TERMCAP_se=$(printf "\e[0m") \
    LESS_TERMCAP_so=$(printf "\e[1;44;33m") \
    LESS_TERMCAP_ue=$(printf "\e[0m") \
    LESS_TERMCAP_us=$(printf "\e[1;32m") \
      man "$@"
}

if ! can_haz watch; then
  # From commandlinefu.com
  function watch () {
    t="$1"
    shift
    while test :
    do
      clear
      date=$(date)
      echo -e "Every $ts: $@ \t\t\t\t $date"
      $@
      sleep "$t"
    done
  }
fi

# scp file to machine you're sshing into this machine from
function mecp() {
  scp "$@" ${SSH_CLIENT%% *}:~/Downloads/;
}

function calc() {
  awk "BEGIN{ print $* }" ;
}

function get-nr-jobs() {
  jobs | wc -l
}
alias get_nr_jobs="get-nr-jobs"

function get_load() {
  uptime | awk '{print $11}' | tr ',' ' '
}

function mtr-url() {
  if can_haz mtr; then
    host=$(ruby -ruri -e "puts (URI.parse('$1').host or '$1')")
    sudo mtr -t "$host"
  else
    echo 'Cannot find mtr in your PATH - install it and try again'
  fi
}
alias mtr_url="mtr-url"

function fix_tmux_ssh_agent() {
  if can_haz tmux; then
    for key in SSH_AUTH_SOCK SSH_CONNECTION SSH_CLIENT; do
      if (tmux show-environment | grep "^${key}" > /dev/null); then
        value=$(tmux show-environment | grep "^${key}" | sed -e "s/^[A-Z_]*=//")
        export ${key}="${value}"
      fi
    done
  else
    echo "Can't find tmux in your PATH. Install it and try again."
  fi
}

function scan24() {
  if can_haz nmap; then
    # Probe a /24 for hosts
    nmap -sP ${1}/24
  else
    echo "Can't find nmap in your PATH - install it and try again."
  fi
}

if ! can_haz nj; then
  # Netjoin - Block until a network connection is obtained.
  # Originally from https://github.com/bamos/dotfiles/blob/master/.funcs
  function nj() {
    while true; do
      ping -c 1 ******* &> /dev/null && break
      sleep 1
    done
  }
fi

# lists zombie processes
function zombie() {
  ps aux | awk '{if ($8=="Z") { print $2 }}'
}
alias zombies=zombie

# get the content type of an http resource
# from https://github.com/jleclanche/dotfiles/blob/master/.zshrc
function htmime() {
  if [[ -z $1 ]]; then
    print "USAGE: htmime <URL>"
    return 1
  fi
  if can_haz curl; then
    mime=$(curl -sIX HEAD $1 | sed -nr "s/Content-Type: (.+)/\1/p")
  else
    echo "Can't find curl in your PATH"
  fi
  print $mime
}

# Start an HTTP server from a directory, optionally specifying the port
function httpserver() {
  local port="${1:-8000}";
  sleep 1 && open "http://localhost:${port}/" &
  # Set the default Content-Type to `text/plain` instead of `application/octet-stream`
  # And serve everything as UTF-8 (although not technically correct, this doesn’t break anything for binary files)
  python -c $'import SimpleHTTPServer;\nmap = SimpleHTTPServer.SimpleHTTPRequestHandler.extensions_map;\nmap[""] = "text/plain";\nfor key, value in map.items():\n\tmap[key] = value + ";charset=UTF-8";\nSimpleHTTPServer.test();' "$port";
}

# Honor old .zsh_functions.local customizations, but print depecation warning.
if [ -f ~/.zsh_functions.local ]; then
  source .zsh_functions.local
  echo ".zsh_functions.local is deprecated and will stop working after June 1st, 2022."
  echo "Make entries in ~/.zshrc.d instead. See https://github.com/unixorn/zsh-quickstart-kit#zshrcd for more details."
fi
