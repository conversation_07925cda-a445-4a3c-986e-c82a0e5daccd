#compdef ipcrm
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
# Completion script for ipcrm, util-linux 2.40.2 (https://github.com/util-linux/util-linux/)
# - remove System V IPC objects
#
# Author:
#   * Aditi Sharma (https://github.com/Aditi76117)
# ------------------------------------------------------------------------------

_arguments -s \
  '(-a --all)'{-a,--all}'-[Remove all resources]:resource:(shm msg sem)' \
  '(-M --shmem-key)'{-M,--shmem-key}'[Remove the shared memory segment created with shemkey]:shmkey' \
  '(-m --shmem-id)'{-m,--shmem-id}'[Remove the shared memory segment identified by shmid]:shmid' \
  '(-Q --queue-key)'{-Q,--queue-key}'[Remove the message queue created with msgkey]:msgkey' \
  '(-q --queue-id)'{-q,--queue-id}'[Remove the message queue identified by msgid]:msgid' \
  '(-S --semaphore-key)'{-S,--semaphore-key}'[Remove the semaphore created with semkey]:semkey' \
  '(-s --semaphore-id)'{-s,--semaphore-id}'[Remove the semaphore identified by semid]' \
  '(- *)'{-h,--help}'[Display help text and exit]' \
  '(- *)'{-V,--version}'[Print version and exit]' \
  '1:type:(shm msg sem)' \
  '*:id'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
