name: CI
on:
  pull_request:
    types:
      - opened
      - synchronize
    branches:
      - master
  push:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read # to fetch code (actions/checkout)

jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    if: github.repository == 'ohmyzsh/ohmyzsh'
    steps:
      - name: Set up git repository
        uses: actions/checkout@v5
      - name: Install zsh
        run: sudo apt-get update; sudo apt-get install zsh
      - name: Check syntax
        run: |
          for file in ./oh-my-zsh.sh \
                      ./lib/*.zsh \
                      ./plugins/*/*.plugin.zsh \
                      ./plugins/*/_* \
                      ./themes/*.zsh-theme; do
            zsh -n "$file" || return 1
          done
