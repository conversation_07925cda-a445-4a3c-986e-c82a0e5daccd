Blackbox Command List
=====================

## Global Flags
### `--vcs`
### `--crypto`
### `--config`
### `--team`
### `--editor`
### `--umask`
### `--debug`
### `--help`
### `--help`
### `--version`
## User Commands
### `blackbox decrypt`
### `blackbox encrypt`
### `blackbox edit`
### `blackbox cat`
### `blackbox diff`
### `blackbox shred`
### `blackbox help`
## User Commands
### `blackbox init`
### `blackbox admin`
### `blackbox file`
### `blackbox status`
### `blackbox reencrypt`
## Debug
### `blackbox info`
## Integration Test (secret menu)
### `blackbox testing_init`

TODO(tlim): Can we automatically generate this?  The data is all in cli.go
