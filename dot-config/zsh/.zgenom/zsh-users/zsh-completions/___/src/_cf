#compdef cf
# ------------------------------------------------------------------------------
#
# Copyright 2015 <PERSON><PERSON><PERSON> & <PERSON>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# ------------------------------------------------------------------------------
#
# Description
# -----------
#
#  Completion script for Cloud Foundry CLI (https://github.com/cloudfoundry/cli#downloads)
#
# ------------------------------------------------------------------------------
#
# Authors
# -------
#
#  * Ferran <PERSON>enas (https://github.com/frodenas)
#  * Danny <PERSON> (https://github.com/dannyzen)
#
# ------------------------------------------------------------------------------

# ----------------------
# ----- Helper functions
# ----------------------

# Output a selectable list of organizations
__cf_orgs() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf orgs | awk 'NR>3{print $1}'))
    if [[ 'X$cont_cmd' != 'X' ]]
        _describe 'ORG' cont_cmd
}

# Output a selectable list of spaces
__cf_spaces() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf spaces | awk 'NR>3{print $1}'))
    if [[ 'X$cont_cmd' != 'X' ]]
        _describe 'SPACE' cont_cmd
}

# Output a selectable list of applications
__cf_apps() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf apps | awk 'NR>4{print $1}'))
    if [[ 'X$cont_cmd' != 'X' ]]
        _describe 'APP' cont_cmd
}

# Output a selectable list of stacks
__cf_stacks() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf stacks | awk 'NR>4{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'STACK' cont_cmd
}

# Output a selectable list of services
__cf_marketplace_services() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf marketplace | awk 'NR>4{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'SERVICE' cont_cmd
}

# Output a selectable list of services
__cf_services() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf services | awk 'NR>4{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'SERVICE' cont_cmd
}

# Output a selectable list of domains
__cf_domains() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf domains | grep -v shared | awk 'NR>2{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'DOMAIN' cont_cmd
}

# Output a selectable list of shared domains
__cf_shared_domains() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf domains | grep -v owned | awk 'NR>2{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'SHARED-DOMAIN' cont_cmd
}

# Output a selectable list of hostnames
__cf_hostnames() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf routes | awk 'NR>3{print $2}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'ROUTE' cont_cmd
}

# Output a selectable list of buildpacks
__cf_buildpacks() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf buildpacks | awk 'NR>3{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'BUILDPACK' cont_cmd
}

# Output a selectable list of feature flags
__cf_feature_flags() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf feature-flags | awk 'NR>4{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'FEATURE-FLAG' cont_cmd
}

# Output a selectable list of plugin repos
__cf_repo_plugins() {
    declare -a cont_cmd
    cont_cmd=($(CF_COLOR=false CF_TRACE=false cf list-plugin-repos | awk 'NR>3{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'REPO-PLUGIN' cont_cmd
}

# Output a selectable list of plugins
__cf_plugins() {
    declare -a cont_cmd
    cont_cmd=($(cf plugins | awk 'NR>4{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'PLUGIN' cont_cmd
}

# Output a selectable list of targets (requires cf-targets plugin)
__cf_targets() {
    declare -a cont_cmd
    cont_cmd=($(cf targets | awk '{print $1}'))
    if [[  'X$cont_cmd' != 'X' ]]
        _describe 'TARGET' cont_cmd
}


# --------------------------
# ----- end Helper functions
# --------------------------

# --------------
# ----- Commands
# --------------

__login() {
  _arguments \
    '-a=[API endpoint (e.g. https://api.example.com)]:api endpoint:' \
    '-u=[Username]:username:' \
    '-p=[Password]:password:' \
    '-o=[Organization]:organization name:__cf_orgs' \
    '-s=[Space]:space name:__cf_spaces' \
    '--sso[Use a one-time password to login]' \
    '--skip-ssl-validation[Skip SSL validation]'
}

__logout() {
    # no arguments
}

__passwd() {
  _arguments \
    '1:password:'
}

__target() {
  _arguments \
    '-o=[Organization]:organization name:__cf_orgs' \
    '-s=[Space]:space name:__cf_spaces'
}

__api() {
  _arguments \
    '1:API url:' \
    '--unset[Remove all api endpoint targeting]' \
    '--skip-ssl-validation[Skip SSL validation]'
}

__auth() {
  # no arguments
}

__apps() {
  # no arguments
}

__app() {
  _arguments \
    '1:application name:__cf_apps' \
    '--guid[Retrieve and display the given app guid. All other health and status output for the app is suppressed]'
}

__push() {
  _arguments \
    '1:application name:__cf_apps' \
    '-b=[Custom buildpack by name (e.g. my-buildpack) or GIT URL or GIT BRANCH URL]:buildpack name:__cf_buildpacks' \
    '-c=[Startup command, set to null to reset to default start command]:startup command:' \
    '-d=[Domain (e.g. example.com)]:domain (e.g. example.com):__cf_domains' \
    '-f=[Path to manifest]:file:_files:' \
    '-i=[Number of instances]:number of instances:' \
    '-k=[Disk limit (e.g. 256M, 1024M, 1G)]:disk limit (e.g. 256M, 1024M, 1G):' \
    '-m=[Memory limit (e.g. 256M, 1024M, 1G)]:memory limit (e.g. 256M, 1024M, 1G):' \
    '-n=[Hostname (e.g. my-subdomain)]:hostname (e.g. my-subdomain):' \
    '-p=[Path to app directory or to a zip file of the contents of the app directory]:file:_files' \
    '-s=[Stack to use (a stack is a pre-built file system, including an operating system, that can run apps)]:stack name:__cf_stacks:' \
    '-t=[Maximum time (in seconds) for CLI to wait for application start, other server side timeouts may apply]:maximum time (in seconds):' \
    '--no-hostname[Map the root domain to this app]' \
    '--no-manifest[Ignore manifest file]' \
    '--no-route[Do not map a route to this app and remove routes from previous pushes of this app]' \
    '--no-start[Do not start an app after pushing]' \
    '--random-route[Create a random route for this app]'
}

__scale() {
  _arguments \
    '1:application name:__cf_apps' \
    '-i=[Number of instances]:number of instances:' \
    '-k=[Disk limit (e.g. 256M, 1024M, 1G)]:disk limit (e.g. 256M, 1024M, 1G):' \
    '-m=[Memory limit (e.g. 256M, 1024M, 1G)]:memory limit (e.g. 256M, 1024M, 1G):' \
    '-f[Force restart of app without prompt]'
}

__delete() {
  _arguments \
    '1:application name:__cf_apps' \
    '--f[Force deletion without confirmation]' \
    '--r[Also delete any mapped routes]'
}

__rename() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:application name:'
}

__start() {
  _arguments \
    '1:application name:__cf_apps'
}

__stop() {
  _arguments \
    '1:application name:__cf_apps'
}

__restart() {
  _arguments \
    '1:application name:__cf_apps'
}

__restage() {
  _arguments \
    '1:application name:__cf_apps'
}

__restart-app-instance() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:application index:'
}

__events() {
  _arguments \
    '1:application name:__cf_apps'
}

__files() {
  _arguments \
    '1:application name:__cf_apps' \
    '2::path:' \
    '-i=[instance]'
}

__logs() {
  _arguments \
    '1:application name:__cf_apps' \
    '--recent[Dump recent logs instead of tailing]'
}

__env() {
  _arguments \
    '1:application name:__cf_apps'
}

__set-env() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:env var name:' \
    '3:env var value:'
}

__unset-env() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:env var name:'
}

__stacks() {
  # no arguments
}

__stack() {
  _arguments \
    '1:stack name:__cf_stacks' \
    '--guid[Retrieve and display the given stack guid. All other output for the stack is suppressed]'
}

__copy-source() {
  _arguments \
    '1:source application name:__cf_apps' \
    '2:target application name:' \
    '-o=[Org that contains the target application]:organization name:__cf_orgs' \
    '-s=[Space that contains the target application]:space name:__cf_spaces' \
    '--no-restart[Override restart of the application in target environment after copy-source completes]'
}

__create-app-manifest() {
  _arguments \
    '1:application name:__cf_apps' \
    '-p=[Specify a path for file creation. If path not specified, manifest file is created in current working directory]:path:_files'
}

__marketplace() {
  _arguments \
    '-s=[Show plan details for a particular service offering]'
}

__services() {
  # no arguments
}

__service() {
  _arguments \
    '1:service name:__cf_services' \
    '--guid[Retrieve and display the given service guid. All other output for the service is suppressed]'
}

__create-service() {
  _arguments \
    '1:service:__cf_marketplace_services' \
    '2:plan:' \
    '3:service name:' \
    '-c=[Valid JSON object containing service-specific configuration parameters, provided either in-line or in a file]' \
    '-t=[User provided tags]'
}

__update-service() {
  _arguments \
    '1:service name:__cf_services' \
    '-p=[Change service plan for a service instance]' \
    '-c=[Valid JSON object containing service-specific configuration parameters, provided either in-line or in a file]' \
    '-t=[User provided tags]'
}

__rename-service() {
  _arguments \
    '1:service name:__cf_services' \
    '2:service name:'
}

__delete-service() {
  _arguments \
    '1:service name:__cf_services' \
    '-f[Force deletion without confirmation]'
}

__create-service-key() {
  _arguments \
    '1:service name:__cf_services' \
    '2:service key:' \
    '-c=[Valid JSON object containing service-specific configuration parameters, provided either in-line or in a file]'
}

__service-keys() {
  _arguments \
    '1:service name:__cf_services'
}

__service-key() {
  _arguments \
    '1:service name:__cf_services' \
    '2:service key:'
}

__delete-service-key() {
  _arguments \
    '1:service name:__cf_services' \
    '2:service key:' \
    '-f[Force deletion without confirmation]'
}

__bind-service() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:service name:__cf_services' \
    '-c=[Valid JSON object containing service-specific configuration parameters, provided either in-line or in a file]'
}

__unbind-service() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:service name:__cf_services'
}

__create-user-provided-service() {
  _arguments \
    '1:service name:' \
    '-p=[Credentials]' \
    '-l=[Syslog Drain Url]'
}

__update-user-provided-service() {
  _arguments \
    '1:service name:__cf_services' \
    '-p=[Credentials]' \
    '-l=[Syslog Drain Url]'
}

__orgs() {
  # no arguments
}

__org() {
  _arguments \
    '1:organization name:__cf_orgs' \
    '--guid[Retrieve and display the given org guid. All other output for the org is suppressed]'
}

__create-org() {
  _arguments \
    '1:organization name:' \
    '-q=[Quota to assign to the newly created org (excluding this option results in assignment of default quota)]'
}

__delete-org() {
  _arguments \
    '1:organization name:__cf_orgs' \
    '-f[Force deletion without confirmation]'
}

__spaces() {
    # no arguments
}

__space() {
  _arguments \
    '1:space name:__cf_spaces' \
    '--guid[Retrieve and display the given space guid. All other output for the space is suppressed]' \
    '--security-group-rules[Retrieve the rules for all the security groups associated with the space]'
}

__create-space() {
  _arguments \
    '1:space name:' \
    '-o=[Org that contains the target application]:organization name:__cf_orgs' \
    '-q=[Quota to assign to the newly created space (excluding this option results in assignment of default quota)]'
}

__delete-space() {
  _arguments \
    '1:space name:__cf_spaces' \
    '-f[Force deletion without confirmation]'
}

__domains() {
  # no arguments
}

__create-domain() {
  _arguments \
    '1:organization name:__cf_orgs' \
    '2:domain:'
}

__delete-domain() {
  _arguments \
    '1:domain:__cf_domains' \
    '-f[Force deletion without confirmation]'
}

__create-shared-domain() {
  _arguments \
    '1:domain:'
}

__delete-shared-domain() {
  _arguments \
    '1:domain:__cf_shared_domains' \
    '-f[Force deletion without confirmation]'
}

__routes() {
  _arguments \
    '--orglevel[List all the routes for all spaces of current organization]'
}

__create-route() {
  _arguments \
    '1:space name:__cf_spaces' \
    '2:domain:__cf_domains' \
    '-n=[Hostname]'
}

__check-route() {
  _arguments \
    '1:hostname:__cf_hostnames' \
    '2:domain:__cf_domains'
}

__map-route() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:domain:__cf_domains' \
    '-n=[Hostname]:hostname:__cf_hostnames:'
}

__unmap-route() {
  _arguments \
    '1:application name:__cf_apps' \
    '2:domain:__cf_domains' \
    '-n=[Hostname]:hostname:__cf_hostnames:'
}

__delete-route() {
  _arguments \
    '1:domain:__cf_domains' \
    '-n=[Hostname]:hostname:__cf_hostnames:' \
    '-f[Force deletion without confirmation]'
}

__delete-orphaned-routes() {
  _arguments \
    '-f[Force deletion without confirmation]'
}

__buildpacks() {
  # no arguments
}

__create-buildpack() {
  _arguments \
    '1:buildpack name:' \
    '2:path:_files' \
    '3:position:' \
    '--enable[Enable the buildpack to be used for staging]' \
    '--disable[Disable the buildpack from being used for staging]'
}

__update-buildpack() {
  _arguments \
    '1:buildpack name:__cf_buildpacks' \
    '-p=[Path to directory or zip file]:file:_files' \
    '-i=[The order in which the buildpacks are checked during buildpack auto-detection]' \
    '--enable[Enable the buildpack to be used for staging]' \
    '--disable[Disable the buildpack from being used for staging]' \
    '--lock[Lock the buildpack to prevent updates]' \
    '--unlock[Unlock the buildpack to enable updates]'
}

__rename-buildpack() {
  _arguments \
    '1:buildpack name:__cf_buildpacks' \
    '2:new buildpack name:'
}

__delete-buildpack() {
  _arguments \
    '1:buildpack name:__cf_buildpacks' \
    '-f[Force deletion without confirmation]'
}

__running-environment-variable-group() {
  # no arguments
}

__staging-environment-variable-group() {
  # no arguments
}

__set-staging-environment-variable-group() {
  # no arguments
}

__set-running-environment-variable-group() {
  # no arguments
}

__feature-flags() {
  # no arguments
}

__feature-flag() {
  _arguments \
    '1:feature name:__cf_feature_flags'
}

__enable-feature-flag() {
  _arguments \
    '1:feature name:__cf_feature_flags'
}

__disable-feature-flag() {
  _arguments \
    '1:feature name:__cf_feature_flags'
}

__curl() {
  _arguments \
    '1:path:' \
    '-i[Include response headers in the output]' \
    '-v[Enable CF_TRACE output for all requests and responses]' \
    '-X=[HTTP method]:http method:(GET POST PUT DELETE)' \
    '-h=[Custom headers to include in the request, flag can be specified multiple times]' \
    '-d=[HTTP data to include in the request body]' \
    '--output[Write curl body to FILE instead of stdout]'
}

__config() {
  _arguments \
    '--async-timeout=[Timeout for async HTTP requests]' \
    '--trace=[Trace HTTP requests]:trace:(true false)' \
    '--color=[Enable or disable color]:color:(true false)' \
    '--locale=[Set default locale. If LOCALE is CLEAR, previous locale is deleted]'
}

__oauth-token() {
  # no arguments
}

__add-plugin-repo() {
  _arguments \
    '1:repo name:' \
    '2:url:'
}

__remove-plugin-repo() {
  _arguments \
    '1:repo name:__cf_repo_plugins' \
    '2:url:'
}

__list-plugin-repos() {
  # no arguments
}

__repo-plugins() {
  _arguments \
    '-r=[Repo Name]:repo name:__cf_repo_plugins'
}

__plugins() {
  _arguments \
    '-checksum[Compute and show the sha1 value of the plugin binary file]'
}

__install-plugin() {
  _arguments \
    '1:plugin URL or path:_files' \
    '-r=[repo name where the plugin binary is located]:repo name:__cf_repo_plugins'
}

__uninstall-plugin() {
  _arguments \
    '1:plugin name:__cf_plugins'
}

__save-target() {
  _arguments \
    '1:target-name:' \
    '-f[Force save even if current target is already saved under another name]'
}

__set-target() {
  _arguments \
    '1:target-name:__cf_targets' \
    '-f[Force target change even if current target is unsaved]'
}

__delete-target() {
  _arguments \
    '1:target-name:__cf_targets'
}

# ------------------
# ----- end Commands
# ------------------

# -------------------
# ----- 1st Arguments
# -------------------

local -a _1st_arguments
_1st_arguments=(
  "login":"Log user in"
  "logout":"Log user out"
  "passwd":"Change user password"
  "target":"Set or view the targeted org or space"
  "api":"Set or view target api url"
  "auth":"Authenticate user non-interactively"
  "apps":"List all apps in the target space"
  "app":"Display health and status for app"
  "push":"Push a new app or sync changes to an existing app"
  "scale":"Change or view the instance count, disk space limit, and memory limit for an app"
  "delete":"Delete an app"
  "rename":"Rename an app"
  "start":"Start an app"
  "stop":"Stop an app"
  "restart":"Restart an app"
  "restage":"Restage an app"
  "restart-app-instance":"Terminate the running application Instance at the given index and instantiate a new instance of the application with the same index"
  "events":"Show recent app events"
  "files":"Print out a list of files in a directory or the contents of a specific file"
  "logs":"Tail or show recent logs for an app"
  "env":"Show all env variables for an app"
  "set-env":"Set an env variable for an app"
  "unset-env":"Remove an env variable"
  "stacks":"List all stacks"
  "stack":"Show information for a stack"
  "copy-source":"Make a copy of app source code from one application to another.  Unless overridden, the copy-source command will restart the application"
  "create-app-manifest":"Create an app manifest for an app that has been pushed successfully"
  "marketplace":"List available offerings in the marketplace"
  "services":"List all service instances in the target space"
  "service":"Show service instance info"
  "create-service":"Create a service instance"
  "update-service":"Update a service instance"
  "delete-service":"Delete a service instance"
  "rename-service":"Rename a service instance"
  "create-service-key":"Create key for a service instance"
  "service-keys":"List keys for a service instance"
  "service-key":"Show service key info"
  "delete-service-key":"Delete a service key"
  "bind-service":"Bind a service instance to an app"
  "unbind-service":"Unbind a service instance from an app"
  "create-user-provided-service":"Make a user-provided service instance available to cf apps"
  "update-user-provided-service":"Update user-provided service instance name value pairs"
  "orgs":"List all orgs"
  "org":"Show org info"
  "create-org":"Create an org"
  "delete-org":"Delete an org"
  "rename-org":"Rename an org"
  "spaces":"List all spaces in an org"
  "space":"Show space info"
  "create-space":"Create a space"
  "delete-space":"Delete a space"
  "rename-space":"Rename a space"
  "domains":"List domains in the target org"
  "create-domain":"Create a domain in an org for later use"
  "delete-domain":"Delete a domain"
  "create-shared-domain":"Create a domain that can be used by all orgs (admin-only)"
  "delete-shared-domain":"Delete a shared domain"
  "routes":"List all routes in the current space or the current organization"
  "create-route":"Create a url route in a space for later use"
  "check-route":"Perform a simple check to determine whether a route currently exists or not"
  "map-route":"Add a url route to an app"
  "unmap-route":"Remove a url route from an app"
  "delete-route":"Delete a route"
  "delete-orphaned-routes":"Delete all orphaned routes (e.g.: those that are not mapped to an app)"
  "buildpacks":"List all buildpacks"
  "create-buildpack":"Create a buildpack"
  "update-buildpack":"Update a buildpack"
  "rename-buildpack":"Rename a buildpack"
  "delete-buildpack":"Delete a buildpack"
  "running-environment-variable-group":"Retrieve the contents of the running environment variable group"
  "staging-environment-variable-group":"Retrieve the contents of the staging environment variable group"
  "set-staging-environment-variable-group":"Pass parameters as JSON to create a staging environment variable group"
  "set-running-environment-variable-group":"Pass parameters as JSON to create a running environment variable group"
  "feature-flags":"Retrieve list of feature flags with status of each flag-able feature"
  "feature-flag":"Retrieve an individual feature flag with status"
  "enable-feature-flag":"Enable the use of a feature so that users have access to and can use the feature"
  "disable-feature-flag":"Disable the use of a feature so that users have access to and can use the feature"
  "curl":"Executes a raw request, content-type set to application/json by default"
  "config":"write default values to the config"
  "oauth-token":"Retrieve and display the OAuth token for the current session"
  "add-plugin-repo":"Add a new plugin repository"
  "remove-plugin-repo":"Remove a plugin repository"
  "list-plugin-repos":"list all the added plugin repository"
  "repo-plugins":"List all available plugins in all added repositories"
  "plugins":"list all available plugin commands"
  "install-plugin":"Install the plugin defined in command argument"
  "uninstall-plugin":"Uninstall the plugin defined in command argument"
  "targets":"List all saved targets (requires cf-targets plugin)"
  "save-target":"Save the current target under a given name (requires cf-targets plugin)"
  "set-target":"Restore a previously saved target (requires cf-targets plugin)"
  "delete-target":"Delete a saved target (requires cf-targets plugin)" 
)

# -----------------------
# ----- end 1st Arguments
# -----------------------

# ----------
# ----- Main
# ----------

_arguments '*:: :->command'

if (( CURRENT == 1 )); then
  _describe -t commands "cf command" _1st_arguments
  return
fi

local -a _command_args
case "$words[1]" in
  login)
    __login ;;
  logout)
    __logout ;;
  passwd)
    __passwd ;;
  target)
    __target ;;
  api)
    __api ;;
  auth)
    __auth ;;
  apps)
    __apps ;;
  app)
    __app ;;
  push)
    __push ;;
  scale)
    __scale ;;
  delete)
    __delete ;;
  rename)
    __rename ;;
  start)
    __start ;;
  stop)
    __stop ;;
  restart)
    __restart ;;
  restage)
    __restage ;;
  restart-app-instance)
    __restart-app-instance ;;
  events)
    __events ;;
  files)
    __files ;;
  logs)
    __logs ;;
  env)
    __env ;;
  set-env)
    __set-env ;;
  unset-env)
    __unset-env ;;
  stacks)
    __stacks ;;
  stack)
    __stack ;;
  copy-source)
    __copy-source ;;
  create-app-manifest)
    __create-app-manifest ;;
  marketplace)
    __marketplace ;;
  services)
    __services ;;
  service)
    __service ;;
  create-service)
    __create-service ;;
  update-service)
    __update-service ;;
  rename-service)
    __rename-service ;;
  delete-service)
    __delete-service ;;
  create-service-key)
    __create-service-key ;;
  service-keys)
    __service-keys ;;
  service-key)
    __service-key ;;
  delete-service-key)
    __delete-service-key ;;
  bind-service)
    __bind-service ;;
  unbind-service)
    __unbind-service ;;
  create-user-provided-service)
    __create-user-provided-service ;;
  update-user-provided-service)
    __update-user-provided-service ;;
  orgs)
    __orgs ;;
  org)
    __org ;;
  create-org)
    __create-org ;;
  delete-org)
    __delete-org ;;
  spaces)
    __spaces ;;
  space)
    __space ;;
  create-space)
    __create-space ;;
  delete-space)
    __delete-space ;;
  domains)
    __domains ;;
  create-domain)
    __create-domain ;;
  delete-domain)
    __delete-domain ;;
  create-shared-domain)
    __create-shared-domain ;;
  delete-shared-domain)
    __delete-shared-domain ;;
  routes)
    __routes ;;
  create-route)
    __create-route ;;
  check-route)
    __check-route ;;
  map-route)
    __map-route ;;
  unmap-route)
    __unmap-route ;;
  delete-route)
    __delete-route ;;
  delete-orphaned-routes)
    __delete-orphaned-routes ;;
  buildpacks)
    __buildpacks ;;
  create-buildpack)
    __create-buildpack ;;
  update-buildpack)
    __update-buildpack ;;
  rename-buildpack)
    __rename-buildpack ;;
  delete-buildpack)
    __delete-buildpack ;;
  running-environment-variable-group)
    __running-environment-variable-group ;;
  staging-environment-variable-group)
    __staging-environment-variable-group ;;
  set-staging-environment-variable-group)
    __set-staging-environment-variable-group ;;
  set-running-environment-variable-group)
    __set-running-environment-variable-group ;;
  feature-flags)
    __feature-flags ;;
  feature-flag)
    __feature-flag ;;
  enable-feature-flag)
    __enable-feature-flag ;;
  disable-feature-flag)
    __disable-feature-flag ;;
  curl)
    __curl ;;
  config)
    __config ;;
  oauth-token)
    __oauth-token ;;
  add-plugin-repo)
    __add-plugin-repo ;;
  remove-plugin-repo)
    __remove-plugin-repo ;;
  list-plugin-repos)
    __list-plugin-repos ;;
  repo-plugins)
    __repo-plugins ;;
  plugins)
    __plugins ;;
  install-plugin)
    __install-plugin ;;
  uninstall-plugin)
    __uninstall-plugin ;;
  save-target)
    __save-target ;;
  set-target)
    __set-target ;;
  delete-target)
    __delete-target ;;
esac
