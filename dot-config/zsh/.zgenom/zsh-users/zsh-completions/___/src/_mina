#compdef mina
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for Mina (http://nadarei.co/mina/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Kazuya Takeshima (https://github.com/mitukiii)
#
# ------------------------------------------------------------------------------


local curcontext="$curcontext" state line cmds ret=1

_arguments -C \
  {-h,--help}'[Show help]' \
  {-V,--version}'[Show program version]' \
  {-v,--verbose}'[Show commands as they happen]' \
  {-s,--simulate}'[Run in simulation mode]' \
  {-t,--trace}'[Show backtraces when errors occur]' \
  -f'[Use FILE as the deploy configuration]:file:_files' \
  '*: :->cmds' && ret=0

case $state in
  cmds)
    cmds=( ${(f)"$(_call_program commands mina -T 2> /dev/null | sed -e 's/:/\\:/g; s/\[/\\[/g; s/\]/\\]/g; s/mina \([^ ]*\) .*# /\1:/g')"} )
    _describe -t commands 'mina command' cmds && ret=0
    ;;
esac

return ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
