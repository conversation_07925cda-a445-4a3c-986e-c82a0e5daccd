#!/usr/bin/env bash

#
# blackbox_register_new_file -- Enroll new file(s) in the blackbox system.
#
# Takes previously unencrypted file(s) and enrolls them into the blackbox
# system.  Each file will be kept in the repo as an encrypted file.  On deployment
# to systems that need the plaintext (unencrypted) versions, run
# blackbox_postdeploy.sh to decrypt all the files.

set -e
source "${0%/*}/_blackbox_common.sh"

function register_new_file() {
  unencrypted_file=$(get_unencrypted_filename "$1")
  encrypted_file=$(get_encrypted_filename "$1")

  if [[ "$1" == "$encrypted_file" ]]; then
    echo ERROR: Please only register unencrypted files.
    exit 1
  fi

  echo "========== PLAINFILE $unencrypted_file"
  echo "========== ENCRYPTED $encrypted_file"

  fail_if_not_exists "$unencrypted_file" "Please specify an existing file."
  fail_if_exists "$encrypted_file" "Will not overwrite."

  prepare_keychain
  encrypt_file "$unencrypted_file" "$encrypted_file"
  add_filename_to_cryptlist "$unencrypted_file"
  vcs_ignore "$unencrypted_file"

  # Is the unencrypted file already in HG? (ie. are we correcting a bad situation)
  SECRETSEXPOSED=$(is_in_vcs "${unencrypted_file}")
  echo "========== CREATED: ${encrypted_file}"
  echo "========== UPDATING REPO:"
  shred_file "$unencrypted_file"

  if [[ "$SECRETSEXPOSED" == "true" ]] ; then
    vcs_remove "$unencrypted_file"
    vcs_add "$encrypted_file"
  fi

  echo 'NOTE: "already tracked!" messages are safe to ignore.'
  vcs_add "$BB_FILES" "$encrypted_file"
  vcs_commit "registered in blackbox: ${unencrypted_file}" "$BB_FILES" "$encrypted_file" "$(vcs_ignore_file_path)"
}

for target in "$@"; do
  register_new_file "$target"
done

echo "========== UPDATING VCS: DONE"
if [[ $VCS_TYPE = "svn" ]]; then
	echo "Local repo updated and file pushed to source control (unless an error was displayed)."
else
	echo "Local repo updated.  Please push when ready."
	echo "    $VCS_TYPE push"
fi
