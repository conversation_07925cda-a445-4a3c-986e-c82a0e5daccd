#!/usr/bin/env bash
#
# Show submodules and what commit they're at
#
# Copyright 2024, <PERSON> <<EMAIL>>

set -o pipefail
if [[ -n "$DEBUG" ]]; then
  # shellcheck disable=SC2086
  if [[ "$(echo $DEBUG | tr '[:upper:]' '[:lower:]')" == "verbose" ]]; then
    set -x
  fi
fi

function debug() {
  if [[ -n "$DEBUG" ]]; then
        zsh_debug_echo "$@"
  fi
}

function echo-stderr() {
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function has() {
  # Check if a command is in $PATH
  which "$@" > /dev/null 2>&1
}

function check-dependencies() {
  debug "Checking dependencies..."
  # shellcheck disable=SC2041
  # Placeholders for whatever programs you really need
  for p in 'git'
  do
    if ! has $p; then
      fail "Can't find $p in your $PATH"
    else
      debug "- Found $p"
    fi
  done
}

check-dependencies

exec git submodule foreach 'echo $sm_path `git rev-parse HEAD`'
