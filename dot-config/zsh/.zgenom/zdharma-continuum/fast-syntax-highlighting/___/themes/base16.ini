[base]
default          = none
unknown-token    = 1,bold
commandseparator = none
redirection      = none
here-string-tri  = 14
here-string-text = bg:11
here-string-var  = 1,bg:11
exec-descriptor  = 9,bold
comment          = 8
correct-subtle   = 12
incorrect-subtle = 1
subtle-separator = 12
subtle-bg        = bg:10
; secondary        =
; recursive-base   =

[command-point]
reserved-word  = 5
subcommand     = 6
alias          = 4
suffix-alias   = 4
global-alias   = 4,bg:11
builtin        = 4
function       = 4
command        = 4
precommand     = 6
hashed-command = 4
single-sq-bracket = 4
double-sq-bracket = 4
double-paren   = 5

[paths]
path          = 9
pathseparator =
path-to-dir   = 9,underline
globbing      = 6
globbing-ext  = 6,bold

[brackets]
paired-bracket = bg:8
bracket-level-1 = 3,bold
bracket-level-2 = 6,bold
bracket-level-3 = 2,bold

[arguments]
single-hyphen-option   = 3
double-hyphen-option   = 3
back-quoted-argument   = none
single-quoted-argument = 2
double-quoted-argument = 2
dollar-quoted-argument = 2

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 6
; backslash or $... in "..."
back-or-dollar-double-quoted-argument = 1

[other]
variable             = 1
assign               = none
assign-array-bracket = 5
history-expansion    = 6,bold

[math]
mathvar = 1
mathnum = 9
matherr = 1,bold

[for-loop]
forvar = 1
fornum = 9
; operator
foroper = none
; separator
forsep = none

[case]
case-input       = 1
case-parentheses = 5
case-condition   = bg:10
