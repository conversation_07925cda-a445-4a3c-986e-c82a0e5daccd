#!/usr/bin/env zsh
#
# Only including a shebang to trigger editors to use shell syntax highlighting
#
# <AUTHOR> <EMAIL>
#
# BSD licensed, see LICENSE.txt in this repository.
#
# If you want to customize these, the best thing to do is override them
# with a shell fragment in ~/.zshrc.d. Files there are automatically sourced
# by the quickstart kit, so if you do that, you don't have to maintain your
# own fork of the quickstart kit.

if [[ -d ~/gocode ]]; then
  export GOPATH=~/gocode
fi

# Yes, these tests are ugly. They do however, work.
if [[ "$(uname -s)" == "Darwin" ]]; then
  # do macOS specific things
  alias top="TERM=vt100 top"
else
  alias cputop="top -o cpu"
  alias l-d="ls -lad"
  alias l="ls -lah"
  alias ll="ls -lah | less"
fi

if [[ "$(uname -s)" == "Linux" ]]; then
  # we're on linux
  alias l-d="ls -lFad"
  alias l="ls -laF"
  alias ll="ls -lFa | TERM=vt100 less"
fi

export CVS_RSH=ssh

# shellcheck disable=SC2142
alias historysummary="history | awk '{a[\$2]++} END{for(i in a){printf \"%5d\t%s\n\",a[i],i}}' | sort -rn | head"

if [ -x /bin/vim ]; then
    alias vi="/bin/vim"
    alias vim="/bin/vim"
    export EDITOR="/bin/vim"
fi

if [ -x /usr/bin/vim ]; then
    alias vi="/usr/bin/vim"
    alias vim="/usr/bin/vim"
    export EDITOR="/usr/bin/vim"
fi

# MacPorts has a newer vim than Apple ships
if [ -x /opt/local/bin/vim ]; then
    alias vim="/opt/local/bin/vim"
    alias vi="/opt/local/bin/vim"
    export EDITOR="/opt/local/bin/vim"
fi

# So does brew
if can_haz brew; then
  brew_prefix="$(brew --prefix)/bin"
  if [[ -x "$brew_prefix/vim" ]]; then
      alias vim="$brew_prefix/vim"
      alias vi="$brew_prefix/vim"
      export EDITOR="$brew_prefix/vim"
  fi
  unset brew_prefix
fi

# Clearly, I really like vim.

export VISUAL=${EDITOR}

# Clean up files that have the wrong line endings
alias mac2unix="tr '\015' '\012'"
alias unix2mac="tr '\012' '\015'"

# A couple of different external IP lookups depending on which is down.
alias external_ip="curl -s icanhazip.com"
alias myip="dig +short myip.opendns.com @resolver1.opendns.com"

# Show laptop's IP addresses
# shellcheck disable=2139
alias ips="ifconfig -a | perl -nle'/(\d+\.\d+\.\d+\.\d+)/ && print $1'"

alias reattach="screen -r"

# SSH stuff
# Pass our credentials by default
alias ssh='ssh -A'
alias sshA='ssh -A'
alias ssh-A='ssh -A'
alias ssh-unkeyed='/usr/bin/ssh'
alias ssh_unkeyed='/usr/bin/ssh'

alias scp-no-hostchecks='scp -o UserKnownHostsFile=/dev/null -o GlobalKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
alias ssh-no-hostchecks='ssh -A -o UserKnownHostsFile=/dev/null -o GlobalKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
alias scp_no_hostchecks='scp -o UserKnownHostsFile=/dev/null -o GlobalKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
alias ssh_no_hostchecks='ssh -A -o UserKnownHostsFile=/dev/null -o GlobalKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'

# Set up even more shortcuts because I am that lazy a typist.
alias nh-scp=scp-no-hostchecks
alias nh-ssh=ssh-no-hostchecks
alias nh_scp=scp-no-hostchecks
alias nh_ssh=ssh-no-hostchecks
alias nhscp=scp-no-hostchecks
alias nhssh=ssh-no-hostchecks

# Strip color codes from commands that insist on spewing them so we can
# pipe them into files cleanly.
alias stripcolors='sed -r "s/\x1B\[([0-9]{1,2}(;[0-9]{1,2})?)?[mGK]//g"'

# On the rare occasions I don't want to continue an interrupted download
# I can always delete the incomplete fragment explicitly. I usually just
# want to complete it.
alias wget="wget -c"

# Dump the last 20 history entries to make !number more convenient to use.
alias zh="fc -l -d -D"

# My typical tyops.
alias gerp='grep'
alias grep-i='grep -i'
alias grep='GREP_COLORS="1;37;41" LANG=C grep --color=auto'
alias grepi='grep -i'
alias knfie='knife'
alias maek='make'
alias psax='ps ax'
alias pswax='ps wax'
alias psxa='ps ax'
alias raek='rake'
alias tartvf='tar tvf'
alias tartvzf='tar tvzf'
alias tarxvf='tar xvf'
alias tarxvjf='tar xvjf'

# Deal with various stupidities.

# Thanks so much for breaking ldapsearch, fink
if [ -x /sw/bin/ldapsearch ];then
  # At least the stale version Apple ships actually works.
  alias ldapsearch=/usr/bin/ldapsearch
fi

# Use brew versions if present
if can_haz brew; then
  brew_prefix=$(brew --prefix)
  if [ -x "${brew_prefix}/bin/mysql/bin/mysql" ]; then
    alias mysql="${brew_prefix}/mysql/bin/mysql"
  fi

  if [ -x "${brew_prefix}/bin/mysql/bin/mysqladmin" ]; then
    alias mysqladmin="${brew_prefix}/mysql/bin/mysqladmin"
  fi
fi

# Help the lazy typists again.
alias ..="cd .."
alias ...="cd ../.."

# Honor old .zsh_aliases.local customizations, but print depecation warning.
if [ -f ~/.zsh_aliases.local ]; then
  # shellcheck disable=SC1090
  source ~/.zsh_aliases.local
  echo ".zsh_aliases.local is deprecated and will stop working after June 1st, 2022."
  echo "Make entries in ~/.zshrc.d instead. See https://github.com/unixorn/zsh-quickstart-kit#zshrcd for more details."
fi
