#!/opt/homebrew/bin/zsh
# Load source/execute detection utils if present (optional)
{
    DETECTION_SCRIPT="${ZDOTDIR:-$HOME/.config/zsh}/.zshrc.d/00-core/01-source-execute-detection.zsh"
    if [ -r "$DETECTION_SCRIPT" ]; then
        export ZSH_SOURCE_EXECUTE_TESTING=false
        source "$DETECTION_SCRIPT"
    fi
}
#=============================================================================
# File: 05-lazy-git-config.zsh
# Purpose: 2.2.3 Lazy loading wrapper for git configuration with caching
# Dependencies: git (if available)
# Author: Configuration management system
# Last Modified: 2025-08-20
#=============================================================================

[[ "$ZSH_DEBUG" == "1" ]] && {
    printf "# ++++++ %s ++++++++++++++++++++++++++++++++++++\n" "$0" >&2
}

# 2.2.3 git configuration lazy loading and caching
if command -v git >/dev/null 2>&1; then
    # ******* Working Directory Management - Save current working directory
    local original_cwd="$(pwd)"
    
    # ******* Configuration and logging setup
    local config_base="/Users/<USER>/.config/zsh"
    local cache_dir="$config_base/.cache"
    local git_cache_file="$cache_dir/git-config-cache"
    local log_date=$(date -u +%Y-%m-%d)
    local log_time=$(date -u +%H-%M-%S)
    local log_dir="$config_base/logs/$log_date"
    local log_file="$log_dir/lazy-git-config_$log_time.log"
    
    # ******* Setup cache and logging directories
    mkdir -p "$cache_dir" "$log_dir"
    
    # ******* Initialize lazy loading state
    typeset -g _GIT_CONFIG_LOADED=0
    
    # ******* Git configuration caching function
    _cache_git_config() {
        # Skip if already loaded
        [[ $_GIT_CONFIG_LOADED -eq 1 ]] && return 0
        
        # Log initialization
        {
            echo "=============================================================================="
            echo "Lazy Git Configuration with Caching"  
            echo "Started: $(date -u +%Y-%m-%dT%H:%M:%SZ)"
            echo "Cache file: $git_cache_file"
            echo "=============================================================================="
            echo ""
            
            local needs_refresh=false
            local cache_reason=""
            
            # Check if cache exists and is recent
            if [[ -f "$git_cache_file" ]]; then
                # Check if cache is less than 1 hour old
                local cache_timestamp=$(stat -f %m "$git_cache_file" 2>/dev/null || echo "0")
                local current_timestamp=$(date +%s)
                local age_seconds=$((current_timestamp - cache_timestamp))
                local age_minutes=$((age_seconds / 60))
                
                if [[ $age_minutes -gt 60 ]]; then
                    needs_refresh=true
                    cache_reason="Cache expired ($age_minutes minutes old)"
                else
                    cache_reason="Using cached config ($age_minutes minutes old)"
                fi
            else
                needs_refresh=true
                cache_reason="No cache file found"
            fi
            
            echo "🔍 Cache status: $cache_reason"
            
            if [[ "$needs_refresh" == "true" ]]; then
                echo "🔄 Refreshing git configuration cache..."
                
                # Get git configuration values
                local git_name git_email
                git_name=$(git config --get user.name 2>/dev/null || echo 'Unknown')
                git_email=$(git config --get user.email 2>/dev/null || echo '<EMAIL>')
                
                # Write to cache file
                cat > "$git_cache_file" << EOF
# Git configuration cache - generated $(date -u +%Y-%m-%dT%H:%M:%SZ)
export GIT_AUTHOR_NAME='$git_name'
export GIT_AUTHOR_EMAIL='$git_email'
export GIT_COMMITTER_NAME='$git_name'
export GIT_COMMITTER_EMAIL='$git_email'
EOF
                echo "✅ Git configuration cached"
                echo "   Name: $git_name"
                echo "   Email: $git_email"
            fi
            
            # Source the cache file
            if [[ -f "$git_cache_file" ]]; then
                source "$git_cache_file"
                _GIT_CONFIG_LOADED=1
                echo "✅ Git configuration loaded from cache"
            else
                echo "❌ Failed to load git configuration"
                return 1
            fi
            
            echo ""
            echo "=============================================================================="
            echo "Lazy git configuration completed at $(date -u +%Y-%m-%dT%H:%M:%SZ)"
            echo "=============================================================================="
            
        } 2>&1 | tee -a "$log_file"
        
        return 0
    }
    
    # 2.2.3.6 Lazy git wrapper functions (for common git commands that need config)
    _lazy_git_wrapper() {
        local cmd="$1"
        shift
        
        # Load git config on first use
        _cache_git_config || return 1
        
        # Call the real git command
        command git "$cmd" "$@"
    }
    
    # 2.2.3.7 Override common git commands that benefit from cached config
    git() {
        case "$1" in
            commit|log|show|config)
                _lazy_git_wrapper "$@"
                ;;
            *)
                command git "$@"
                ;;
        esac
    }
    
    # 2.2.3.8 Function to manually refresh git config cache
    git-refresh-config() {
        [[ -f "$git_cache_file" ]] && rm "$git_cache_file"
        _GIT_CONFIG_LOADED=0
        _cache_git_config
    }
    
    # ******* Working Directory Restoration
    if ! cd "$original_cwd" 2>/dev/null; then
        echo "⚠️  Warning: Could not restore original directory: $original_cwd" >&2
        return 1
    fi
    
    [[ "$ZSH_DEBUG" == "1" ]] && echo "# [lazy-git-config] Lazy git configuration wrapper initialized" >&2
else
    [[ "$ZSH_DEBUG" == "1" ]] && echo "# [lazy-git-config] git not found, skipping" >&2
fi
