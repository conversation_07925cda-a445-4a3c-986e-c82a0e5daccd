{"name": "bats-assert", "version": "2.0.0", "description": "Common assertions for Bats", "homepage": "https://github.com/jasonkarns/bats-assert-1", "license": "CC0-1.0", "author": "<PERSON><PERSON><PERSON> (https://github.com/ztombol)", "contributors": ["<PERSON> <<EMAIL>> (http://sstephenson.us/)", "<PERSON> <<EMAIL>> (http://jason.karns.name)", "<PERSON><PERSON> <mislav.ma<PERSON>hn<PERSON>@gmail.com> (http://mislav.net/)", "<PERSON> (https://github.com/tpope)"], "repository": "github:j<PERSON><PERSON><PERSON><PERSON>/bats-assert-1", "bugs": "https://github.com/jasonkarns/bats-assert-1/issues", "directories": {"lib": "src", "test": "test"}, "files": ["load.bash", "src"], "scripts": {"test": "bats ${CI+-t} test", "postversion": "npm publish", "prepublishOnly": "npm run publish:github", "publish:github": "git push --follow-tags"}, "devDependencies": {"bats": "^1", "bats-support": "^0.3"}, "peerDependencies": {"bats": "0.4 || ^1", "bats-support": "^0.3"}, "keywords": ["bats", "bash", "shell", "test", "unit", "assert", "assertion", "helper"]}