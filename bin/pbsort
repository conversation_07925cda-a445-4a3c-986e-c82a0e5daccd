#!/bin/bash
#
# sorts the contents of the clipboard

set -o pipefail

function echo-stderr() {
  # shellcheck disable=SC2317
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function check-dependencies() {
  debug "Checking dependencies..."
  # shellcheck disable=SC2041
  # Placeholders for whatever programs you really need
  for dep in "$@"
  do
    if ! has "$dep"; then
      fail "Can't find $dep in your $PATH"
    else
      debug "- Found $dep"
    fi
  done
}

function only-run-on() {
  # shellcheck disable=SC2086
  if [[ "$(uname -s | tr '[:upper:]' '[:lower:]')" != "$(echo $1 | tr '[:upper:]' '[:lower:]')" ]]; then
    fail "This script only runs on $1, this machine is running $(uname -s)"
  else
    debug "OS ($(uname -s)) is valid..."
  fi
}

only-run-on darwin
check-dependencies pbcopy pbpaste

pbpaste | sort | pbcopy
exit $?
