#!/usr/bin/env zsh

function __zgenom_write() {
    __zgenom_out "$*" >> "${ZGEN_INIT}"
}

function zgenom-save() {
    __zgenom_err 'Creating `'"${ZGEN_INIT}"'` ...'

    # Make sure the folder where ZGEN_INIT is stored exists
    local init_dir=${ZGEN_INIT:h}
    [[ -d $init_dir ]] || mkdir -p $init_dir

    __zgenom_out "# {{{" >! "${ZGEN_INIT}"
    __zgenom_write "# Generated by zgenom."
    __zgenom_write "# This file will be overwritten the next time you run zgenom save!"
    __zgenom_write
    __zgenom_write "# Don't source twice or when zgenom was invoked otherwise."
    __zgenom_write '[[ -z $ZGENOM_PLUGINS ]] || return 0'

    # Check for file changes
    if [[ ! -z "${ZGEN_RESET_ON_CHANGE}" ]]; then
        __zgenom_write
        __zgenom_write "# ### Recompilation triggers"

        local ages="$(command stat -Lc "%Y" 2>/dev/null $ZGEN_RESET_ON_CHANGE || \
                      command stat -Lf "%m" 2>/dev/null $ZGEN_RESET_ON_CHANGE)"
        local shas="$(cksum ${ZGEN_RESET_ON_CHANGE})"

        __zgenom_write "local ages shas"
        __zgenom_write "read -rd '' ages <<AGES; read -rd '' shas <<SHAS"
        __zgenom_write "$ages"
        __zgenom_write "AGES"
        __zgenom_write "$shas"
        __zgenom_write "SHAS"

        __zgenom_write 'if [[ -n "$ZGEN_RESET_ON_CHANGE" \'
        __zgenom_write '   && "$(command stat -Lc "%Y" 2>/dev/null $ZGEN_RESET_ON_CHANGE || \'
        __zgenom_write '         command stat -Lf "%m"             $ZGEN_RESET_ON_CHANGE)" != "$ages" \'
        __zgenom_write '   && "$(cksum                             $ZGEN_RESET_ON_CHANGE)" != "$shas" ]]; then'
        __zgenom_write '   printf %s\\n '\''-- zgenom: Files in $ZGEN_RESET_ON_CHANGE changed; resetting `init.zsh`...'\'
        __zgenom_write '   zgenom reset'
        __zgenom_write '   # Early exit to not source twice'
        __zgenom_write '   return 0'
        __zgenom_write 'fi'
    fi

    __zgenom_write
    __zgenom_write "export PMSPEC=$PMSPEC"
    __zgenom_write "export ZPFX=${(q)ZPFX}"

    if [[ -n $_ZGENOM_COMPDEF ]]; then
        __zgenom_write
        __zgenom_write 'autoload -Uz zgenom-compdef && zgenom-compdef'
    fi

    __zgenom_write
    __zgenom_write "ZGENOM_PLUGINS=(${(@qOa)ZGENOM_PLUGINS})"
    if [[ -n $ZSH ]]; then
        __zgenom_write
        __zgenom_write "ZSH=${(q)ZSH}"
    fi
    if [[ ${ZGEN_USE_PREZTO} == 1 && $#ZGEN_PREZTO_OPTIONS -gt 0 ]]; then
        __zgenom_write
        __zgenom_write "# ### Prezto initialization"
        for option in "${ZGEN_PREZTO_OPTIONS[@]}"; do
            __zgenom_write "${option}"
        done
    fi

    # Set up fpath, load completions
    # NOTE: This *intentionally* doesn't use ${ZGEN_COMPINIT_FLAGS}; the only
    #       available flags are meaningless in the presence of `-C`.
    if [[ $#ZGEN_COMPLETIONS -gt 0 ]]; then
        __zgenom_write
        __zgenom_write "# ### Plugins & Completions"
        __zgenom_write 'fpath=('"${(@qOa)ZGEN_COMPLETIONS}"' ${fpath})'
    fi

    if [[ $#ZGEN_LOADED -gt 0 ]]; then
        local i file
        __zgenom_write
        __zgenom_write "# ### General modules"
        __zgenom_write "typeset -ga zsh_loaded_plugins"
        for i in {1.."${#ZGEN_LOADED}"}; do
            file="${ZGEN_LOADED[$i]}"
            __zgenom_write "zsh_loaded_plugins+=( ${(qqq)ZGENOM_LOADED[$i]} )"
            __zgenom_write "set -- && ZERO=${(qqq)file} source ${(qqq)file}"
        done
    fi

    if [[ ${ZGEN_AUTOLOAD_COMPINIT} == 1 ]]; then
        __zgenom_write
        __zgenom_write 'autoload -Uz compinit && \'
        __zgenom_write '   compinit -C '"${ZGEN_COMPINIT_DIR_FLAG}"
    fi

    if [[ -n $_ZGENOM_COMPDEF ]]; then
        __zgenom_write
        __zgenom_write '__zgenom_compdef_apply'
    fi

    if [[ -d $ZGENOM_SOURCE_BIN ]]; then
        __zgenom_write
        __zgenom_write "# ### Bins"
        __zgenom_write 'path=('$ZGENOM_SOURCE_BIN' ${path})'
    fi

    # load prezto modules
    if [[ ${ZGEN_USE_PREZTO} == 1 ]]; then
        __zgenom_write
        __zgenom_write "# ### Prezto modules"
        printf %s "pmodload" >> "${ZGEN_INIT}"
        for module in "${ZGEN_PREZTO_LOAD[@]}"; do
            printf %s " ${module}" >> "${ZGEN_INIT}"
        done
    fi

    __zgenom_write
    __zgenom_write "# }}}"

    zgenom-apply

    __zgenom_err "Compiling files ..."
    zgenom-compile -U $ZGEN_SOURCE
    if [[ $ZGEN_DIR != $ZGEN_SOURCE ]] && [[ $ZGEN_DIR != $ZGEN_SOURCE/* ]]; then
        # Compile ZGEN_DIR if not subdirectory of ZGEN_SOURCE
        zgenom-compile -U $ZGEN_DIR
    fi

    if [[ -n $ZGEN_CUSTOM_COMPDUMP ]]; then
        zgenom-compile -U $ZGEN_CUSTOM_COMPDUMP
    else
        setopt localoptions nullglob
        for compdump in $HOME/.zcompdump*; do
            if [[ $compdump = *.zwc ]] || [[ ! -r $compdump ]]; then
                continue
            fi
            zgenom-compile -U $compdump
        done
    fi
}

zgenom-save $@
