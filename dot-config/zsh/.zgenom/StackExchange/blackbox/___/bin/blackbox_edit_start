#!/usr/bin/env bash

#
# blackbox_edit_start -- Decrypt a file for editing.
#

set -e
source "${0%/*}/_blackbox_common.sh"

if [ $# -eq 0 ]; then
  echo >&2 "Please provide at least one file to start editing"
  exit 1
fi

for param in "$@" ; do

  unencrypted_file=$(get_unencrypted_filename "$param")
  encrypted_file=$(get_encrypted_filename "$param")

  echo >&2 ========== PLAINFILE '"'$unencrypted_file'"'

  fail_if_not_on_cryptlist "$unencrypted_file"
  fail_if_not_exists "$encrypted_file" "This should not happen."
  if [[ ! -s "$unencrypted_file" ]]; then
    rm -f "$unencrypted_file"
  fi
  if [[ -f "$unencrypted_file" ]]; then
    echo >&2 SKIPPING: "$1" "Will not overwrite non-empty files."
    continue
  fi

  prepare_keychain
  # FIXME(tlim): prepare_keychain only needs to run once, outside of the loop.
  decrypt_file "$encrypted_file" "$unencrypted_file"

done
