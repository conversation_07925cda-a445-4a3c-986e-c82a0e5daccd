; Light theme with colors of a Sahara oasis

[base]
default          = none
unknown-token    = red,bold
commandseparator = none
redirection      = none
here-string-tri  = yellow
here-string-text = bg:19
here-string-var  = 153,bg:19
exec-descriptor  = yellow,bold
comment          = black,bold
correct-subtle   = bg:55
incorrect-subtle = bg:52
subtle-bg        = bg:18
secondary        = zdharma
recursive-base   = 183

[command-point]
reserved-word  = 150
subcommand     = 150
alias          = 185
suffix-alias   = 185
global-alias   = bg:19
builtin        = 185
function       = 185
command        = 185
precommand     = 185
hashed-command = 185
single-sq-bracket = 185
double-sq-bracket = 185
double-paren   = 150

[paths]
path          = 187
pathseparator = 
path-to-dir   = 187,underline
globbing      = 180
globbing-ext  = 184

[brackets]
paired-bracket = bg:blue
bracket-level-1 = 178
bracket-level-2 = 148
bracket-level-3 = 141

[arguments]
single-hyphen-option   = 152
double-hyphen-option   = 152
back-quoted-argument   = none
single-quoted-argument = 151
double-quoted-argument = 151
dollar-quoted-argument = 151

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 153
; backslash or $... in "..." (i.e. variable inside a string)
back-or-dollar-double-quoted-argument = 153

[other]
variable             = none
assign               = none
assign-array-bracket = 185
history-expansion    = blue,bold

[math]
mathvar = blue,bold
mathnum = 187
matherr = red

[for-loop]
forvar = none
fornum = 187
; operator
foroper = 151
; separator
forsep = 109

[case]
case-input       = 185
case-parentheses = 116
case-condition   = bg:19
