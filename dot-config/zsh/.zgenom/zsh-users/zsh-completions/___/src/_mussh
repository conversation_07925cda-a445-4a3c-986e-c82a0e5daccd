#compdef mussh
# ------------------------------------------------------------------------------
# Copyright (c) 2011 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for MUltihost SSH Wrapper (https://sourceforge.net/projects/mussh/)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Mario Fernandez (https://github.com/sirech)
#
# ------------------------------------------------------------------------------

local curcontext="$curcontext" state line ret=1
typeset -A opt_args

# TODO:
# -i: admit more than one use
# -d, -v, -t: control input
# -m: Accept number (but also empty)
# -h: Accept multiple hosts, also more than one use
# -H: Accept multiple files, also more than one use

_arguments -C \
  '--help[display this help message]' \
  '-V[print version info and exit]' \
  '-d[Verbose debug]:level (from 0 to 2)' \
  '-v[SSH debug level]:level (from 0 to 3)' \
  '-m[Run concurrently]' \
  '(-b -B)-b[Print each hosts output in a block without mingling with other hosts output]' \
  '(-b -B)-B[Allow hosts output to mingle. (default)]' \
  '(-a -A)-a[Force loading ssh-agent]' \
  '(-a -A)-A[Do NOT load ssh-agent]' \
  '(-u -U)-u[Unique. Eliminate duplicate hosts. (default)]' \
  '(-u -U)-U[Do NOT make host list unique]' \
  '-P[Do NOT fall back to passwords on any host. This will skip hosts where keys fail]' \
  '-i[Load an identity file. May be used more than once]:identity' \
  '-o[Args to pass to ssh with -o option]:ssh-args' \
  '(-l -L)-l[Use _login_ when no other is specified with the hostname]:login' \
  '(-l -L)-L[Force use of _login_ on all hosts]:login' \
  '-s[Path to shell on remote host]:shell' \
  '-t[Timeout setting for each session]:timeout' \
  '-p[Host to use as proxy]:[user@]host' \
  '-po[Args to pass to ssh on proxy with -o option]:ssh-args' \
  '(-h -H)-h[Add a host to list of hosts]:[user@]host' \
  '(-h -H)-H[Add contents of file to list of hosts]:host file:_files' \
  '(-c -C)-c[Add a command or quoted list of commands to list of commands to be executed on each host]:command' \
  '(-c -C)-C[Add file contents to list of commands to be executed on each host]:commands file:_files' \
  '(-q)-q[No output unless necessary]' && ret=0

return ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
