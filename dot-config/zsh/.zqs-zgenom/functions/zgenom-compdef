#!/usr/bin/env zsh

if ! typeset -f compdef &>/dev/null; then
    _ZGENOM_COMPDEF=1
    _ZGENOM_COMPDEFS=()

    function compdef() {
        _ZGENOM_COMPDEFS+=("$*")
    }

    function __zgenom_compdef_apply() {
        local comp
        for comp in $_ZGENOM_COMPDEFS; do eval "compdef $comp"; done
        unset _ZGENOM_COMPDEF _ZGENOM_COMPDEFS
        unfunction __zgenom_compdef_apply
    }
fi

function zgenom-compdef() {
    __zgenom_err 'Only call zgenom compdef once.'
    return 1
}
