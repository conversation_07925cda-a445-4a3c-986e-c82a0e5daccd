#compdef rfkill
# ------------------------------------------------------------------------------
# Copyright (c) 2014 <PERSON> <<EMAIL>>
# Copyright (c) 2014 Github zsh-users - https://github.com/zsh-users
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
# ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
# ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
# OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
# Completion script for rfkill 2.38.1 (https://wireless.wiki.kernel.org/en/users/Documentation/rfkill)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#  * Shohei YOSHIDA <https://github.com/syohex>
#
# ------------------------------------------------------------------------------

_rfkill_types() {
  local -a devicetypes=(all
    "wifi:Wireless LAN" "wlan:Wireless LAN"
    "bluetooth:Bluetooth"
    "uwb:Ultrawide Band"
    "ultrawideband:Ultrawide Band"
    "wimax:Wimax"
    "wwan:3G"
    "gps:GPS"
    "fm:FM Radio"
    "nfc:NFC")
  _describe -t device-types "device types" devicetypes
}

_rfkill_devices() {
  local -a devices=(${(M)${(f)"$(rfkill list)"}:#[0-9]*})
  _rfkill_types
  _describe -t devices "devices" devices
}

_rfkill_columns() {
  local -a columns=(DEVICE ID TYPE TYPE-DESC SOFT HARD)
  _values -s ',' 'column' $columns
}

_rfkill_command() {
  local -a commands=(
    'help:display help text and exit'
    'event:listen for rfkill events and display them on stdout'
    'list:list the current state of all available devices'
    'block:disable the corresponding device'
    'unblock:enable the corresponding device'
    'toggle:enable or disable the corresponding device'
  )
  _describe -t commands 'command' commands "$@"
}

_rfkill_subcommand() {
  case "$words[1]" in
    (list)
      _arguments ':types:_rfkill_types'
      ;;
    (block|unblock|toggle)
      _arguments ':device:_rfkill_devices'
      ;;
  esac
}

_rfkill () {
  local curcontext="$curcontext" state line
  typeset -A opt_args

  _arguments -C \
    '(* -)'{-h,--help}'[display help]' \
    '(* -)'{-V,--version}'[get version]' \
    '(-J --json -r --raw)'{-J,--json}'[use JSON output format]' \
    '(-n --noheadings)'{-n,--noheadings}"[don't print headings]" \
    '(-o --output --output-all)'{-o,--output}'[define which output columns to use]:column_list:_rfkill_columns' \
    '(-o --output --output-all)--output-all[output all columns]' \
    '(-J --json -r --raw)'{-r,--raw}'[use the raw output format]' \
    '(-): :_rfkill_command' \
    '(-)*:: :->arguments'

  case $state in
    (arguments)
      curcontext=${curcontext%:*:*}:rfkill-$words[1]:
      _rfkill_subcommand
      ;;
  esac
}

_rfkill "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
