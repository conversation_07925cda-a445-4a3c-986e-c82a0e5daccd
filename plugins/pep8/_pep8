#compdef pep8
#
# this is zsh completion function file.
# generated by genzshcomp(ver: 0.5.1)
#

typeset -A opt_args
local context state line

_arguments -s -S \
  "--help[show this help message and exit]:" \
  "-h[show this help message and exit]:" \
  "--version[show program's version number and exit]:" \
  "--verbose[print status messages, or debug with -vv]" \
  "-v[print status messages, or debug with -vv]" \
  "--quiet[report only file names, or nothing with -qq]" \
  "-q[report only file names, or nothing with -qq]" \
  "--repeat[(obsolete) show all occurrences of the same error]" \
  "-r[(obsolete) show all occurrences of the same error]" \
  "--first[show first occurrence of each error]" \
  "--exclude[exclude files or directories which match these comma separated patterns (default: .svn,CVS,.bzr,.hg,.git,__pycache__)]::patterns:_files" \
  "--filename[when parsing directories, only check filenames matching these comma separated patterns (default: *.py)]::patterns:_files" \
  "--select[select errors and warnings (e.g. E,W6)]::errors:_files" \
  "--ignore[skip errors and warnings (e.g. E4,W)]::errors:_files" \
  "--show-source[show source code for each error]" \
  "--show-pep8[show text of PEP 8 for each error (implies --first)]" \
  "--statistics[count errors and warnings]" \
  "--count[print total number of errors and warnings to standard error and set exit code to 1 if total is not null]" \
  "--max-line-length[set maximum allowed line length (default: 79)]::n:_files" \
  "--format[set the error format \[default|pylint|<custom>\]]::format:_files" \
  "--diff[report only lines changed according to the unified diff received on STDIN]" \
  "--benchmark[measure processing speed   are read from the \[pep8\] section of the tox.ini fg file located in any parent folder of the path(s) allowed options are: exclude, filename, select, ngth, count, format, quiet, show-pep8, show-source, .]" \
  "--config[user config file location (default: /home/<USER>/.config/pep8)]::path:_files" \
  "*::args:_files"
