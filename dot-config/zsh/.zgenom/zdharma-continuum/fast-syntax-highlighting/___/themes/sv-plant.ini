; https://www.syntaxenvy.com/0854668
;
; comment:
;   #5b4e3f -> 58
; keyword:
;   #a1f2b2 -> 157
; number, string:
;   #91cf9e -> 115 (114)
; title, section, name, selector-id:
;   #dadff0 -> 189
; attribute, variable, type:
;   #debb91 -> 180
; symbol, link:
;   #f1dcc6 -> 224
; builtin, deletion:
;   #95cbc1 -> 115
; formula:
;   #3f352a -> 16

[base]
default          = none
unknown-token    = 124
commandseparator = 189
redirection      = none
here-string-tri  = 157
here-string-text = bg:25
here-string-var  = 180,bg:25
exec-descriptor  = 180
comment          = 58
correct-subtle   = bg:55
incorrect-subtle = bg:52
subtle-bg        = bg:17
secondary        = zdharma
recursive-base   = 183

[command-point]
reserved-word  = 157
subcommand     = 224
alias          = 180
suffix-alias   = 180
global-alias   = bg:58
builtin        = 115
function       = 180
command        = 180
precommand     = 157
hashed-command = 180
single-sq-bracket = 115
double-sq-bracket = 115
double-paren   = 157

[paths]
path          = 224
pathseparator = 
path-to-dir   = 224,underline
globbing      = 157
globbing-ext  = 159

[brackets]
paired-bracket = bg:blue
bracket-level-1 = 115
bracket-level-2 = 177
bracket-level-3 = 220

[arguments]
single-hyphen-option   = 180
double-hyphen-option   = 180
back-quoted-argument   = none
single-quoted-argument = 114
double-quoted-argument = 114
dollar-quoted-argument = 114

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 189
; backslash or $... in "..." (i.e. variable in string)
back-or-dollar-double-quoted-argument = 180

[other]
variable             = none
assign               = none
assign-array-bracket = 224
history-expansion    = blue,bold

[math]
mathvar = 180
mathnum = 114
matherr = 124

[for-loop]
forvar = 180
fornum = 114
; operator
foroper = 147
; separator
forsep = 224

[case]
case-input       = 180
case-parentheses = 58
case-condition   = bg:25
