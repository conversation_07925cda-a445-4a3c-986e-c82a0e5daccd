os: linux
language: perl
perl:
  - blead
  - dev
  - 5.30
  - 5.28
  - 5.26
  - 5.24
  - 5.22
  - 5.20
  - 5.18
  - 5.14

matrix:
  include:
    - os: osx
      osx_image: xcode12.2
      language: generic
      perl: 5.30
  allow_failures:
    - perl: "blead"

addons:
  homebrew:
    packages:
      - perl
      - cpanminus
    update: true

before_install:
  - git submodule sync
  - git submodule update --init
  - if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then
      mkdir -p ~/perl5;
    fi
  - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then
      eval $(curl https://travis-perl.github.io/init) --perl;
      sudo add-apt-repository "deb http://archive.ubuntu.com/ubuntu/ trusty-backports restricted main universe";
      sudo apt-get -y update;
    fi
install:
  - cpanm --quiet --notest --local-lib=~/perl5 local::lib
  - eval $(perl -I ~/perl5/lib/perl5/ -Mlocal::lib)
  - cpanm --quiet --notest Test::Perl::Critic Perl::Critic::Freenode
script:
  - perlcritic -1 -q --theme freenode diff-so-fancy
  - ./test/bats/bin/bats test
