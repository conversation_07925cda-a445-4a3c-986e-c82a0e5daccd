#compdef pygmentize
# ------------------------------------------------------------------------------
# Copyright (c) 2012 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for pygmentize 2.13.0 (https://github.com/pygments/pygments)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

_pygmentize() {
  local context state line

  _arguments -s -S \
    '(-g)-l[Specify the lexer to use]:LEXER:_pygmentize_get_lexers' \
    '(-l)-g[Guess the lexer from the file contents]' \
    '-F[Add a filter to the token stream]:STYLE:_pygmentize_get_filters' \
    '-f[Specify the formatter to use]:FORMATTER:_pygmentize_get_formatters' \
    '-O[Give options to the lexer and formatter as a comma-separated list of key-value pairs]' \
    '-P[Give a single option to the lexer and formatter]' \
    '-o[output file]:FILENAME:_files' \
    '-v[Print a detailed traceback on unhandled exceptions]' \
    '-s[Process lines one at a time until EOF]' \
    '-x[Allow custom lexers and formatters to be loaded from a .py file]' \
    '--json[Output as JSON]' \
    '(-L -o -F -H -g -l -N)-S[Print style definitions for STYLE for a formatter given with -f]:STYLE:_pygmentize_get_styles' \
    '-L[lists lexers, formatters, styles or filters]:args:(lexers formatters styles filters)' \
    '(-L -f -o -S -F -H -g -l)-N[guesses and prints out a lexer name based solely on given filename]:FILENAME:_files' \
    '-C[Like -N, but print out a lexer name based solely on a given content from standard input]' \
    '-H[prints detailed help for the object <name> of type <type>]:' \
    '-a[Formatter-specific additional argument for the -S mode]:' \
    '(* -)-V[prints the package version]' \
    '(* -)'{-h,--help}'[prints help]' \
    '*:args:_files'
}

_pygmentize_get_filters() {
  local cache_policy
  zstyle -s ":completion:${curcontext}:" cache-policy cache_policy
  if [[ -z "$cache_policy" ]]; then
    zstyle ":completion:${curcontext}:" cache-policy _pygmentize_caching_policy
  fi

  if ( [[ ${+_pygmentize_filters} -eq 0 ]] || _cache_invalid pygmentize_filters ) \
      && ! _retrieve_cache pygmentize_filters; then
    _pygmentize_filters=(${${(f)"$(pygmentize -L filters | grep '* ' | cut -c3- | sed -e 's/:$//')"}})
    _store_cache pygmentize_filters _pygmentize_filters
  fi

  local expl
  _wanted pygmentize_filters expl 'pygmentize filters' compadd -a _pygmentize_filters
}

_pygmentize_get_formatters() {
  local cache_policy
  zstyle -s ":completion:${curcontext}:" cache-policy cache_policy
  if [[ -z "$cache_policy" ]]; then
    zstyle ":completion:${curcontext}:" cache-policy _pygmentize_caching_policy
  fi

  if ( [[ ${+_pygmentize_formatter} -eq 0 ]] || _cache_invalid pygmentize_formatter ) \
      && ! _retrieve_cache pygmentize_formatter; then
    _pygmentize_formatter=(${${(f)"$(pygmentize -L formatters | grep '* ' | cut -c3- | sed -e 's/, /\n/g' -e 's/:$//')"}})
    _store_cache pygmentize_formatter _pygmentize_formatter
  fi

  local expl
  _wanted pygmentize_formatter expl 'pygmentize formatters' compadd -a _pygmentize_formatter
}

_pygmentize_get_lexers() {
  local cache_policy
  zstyle -s ":completion:${curcontext}:" cache-policy cache_policy
  if [[ -z "$cache_policy" ]]; then
    zstyle ":completion:${curcontext}:" cache-policy _pygmentize_caching_policy
  fi

  if ( [[ ${+_pygmentize_lexer} -eq 0 ]] || _cache_invalid pygmentize_lexer ) \
      && ! _retrieve_cache pygmentize_lexer; then
    _pygmentize_lexer=(${${(f)"$(pygmentize -L lexers | grep '* ' | cut -c3- | sed -e 's/, /\n/g' -e 's/:$//')"}})
    _store_cache pygmentize_lexer _pygmentize_lexer
  fi

  local expl
  _wanted pygmentize_lexer expl 'pygmentize lexers' compadd -a _pygmentize_lexer
}

_pygmentize_get_styles() {
  local cache_policy
  zstyle -s ":completion:${curcontext}:" cache-policy cache_policy
  if [[ -z "$cache_policy" ]]; then
    zstyle ":completion:${curcontext}:" cache-policy _pygmentize_caching_policy
  fi

  if ( [[ ${+_pygmentize_style} -eq 0 ]] || _cache_invalid pygmentize_style ) \
      && ! _retrieve_cache pygmentize_style; then
    _pygmentize_style=(${${(f)"$(pygmentize -L styles | grep '* ' | cut -c3- | sed -e 's/:$//')"}})
    _store_cache pygmentize_style _pygmentize_style
  fi

  local expl
  _wanted pygmentize_style expl 'pygmentize styles' compadd -a _pygmentize_style
}

_pygmentize_caching_policy() {
  local -a oldp
  oldp=( "$1"(Nmh+24) )     # 24 hour
  (( $#oldp ))
}

_pygmentize "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
