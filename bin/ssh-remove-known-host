#!/usr/bin/env bash
#
# Helper script to remove entries for ~/.ssh/known hosts
#
# I can never remember the command to remove a host, and since the format update, you can't just edit `~/.ssh/known_hosts` and search for the IP/hostname.
# Not sure why they switched to a new format that's more pain in the ass.
#
# Copyright 2023, <PERSON> <<EMAIL>>
# License: BSD

set -o pipefail
if [[ -n "$DEBUG" ]]; then
  # shellcheck disable=SC2086
  if [[ "$(echo $DEBUG | tr '[:upper:]' '[:lower:]')" == "verbose" ]]; then
    set -x
  fi
fi

function debug() {
  if [[ -n "$DEBUG" ]]; then
        zsh_debug_echo "$@"
  fi
}

function echo-stderr() {
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function has() {
  # Check if a command is in $PATH
  which "$@" > /dev/null 2>&1
}

function check-dependencies() {
  debug "Checking dependencies..."
  # shellcheck disable=SC2041
  for p in 'ssh-keygen'
  do
    if ! has $p; then
      fail "Can't find $p in your $PATH"
    else
      debug "- Found $p"
    fi
  done
}

function my-name() {
  basename "$0"
}

function usage() {
      zsh_debug_echo "Usage: $(my-name) dns_or_ip"
      zsh_debug_echo "Deletes a host or IP from your known_hosts file"
}

check-dependencies

exec ssh-keygen -f ~/.ssh/known_hosts -R "$@"
