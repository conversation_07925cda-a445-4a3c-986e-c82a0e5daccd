#compdef fossil

function _fossil_get_command_list () {
  fossil help -a | grep -v "Usage|Common|This is"
}

function _fossil () {
  local context state state_descr line
  typeset -A opt_args

  _arguments \
    '1: :->command'\
    '2: :->subcommand'

  case $state in
    command)
      local _OUTPUT=$(fossil branch 2>&1 | grep "use --repo")
      if [[ -z "$_OUTPUT" ]]; then
        compadd "$(_fossil_get_command_list)"
      else
        compadd clone init import help version
      fi ;;
    subcommand)
      case "$words[2]" in
      help) compadd "$(_fossil_get_command_list)" ;;
      add) compadd "$(fossil extra)" ;;
      *) compcall -D ;;
      esac ;;
  esac
}

_fossil "$@"
