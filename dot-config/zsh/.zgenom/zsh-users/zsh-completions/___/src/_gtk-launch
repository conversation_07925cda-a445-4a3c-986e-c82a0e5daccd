#compdef gtk-launch gtk4-launch
# ------------------------------------------------------------------------------
# Copyright (c) 2011 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for gtk-launch on gtk+-3.24.37, gtk4-launch-4.10.4
#  (https://www.gtk.org/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

_gtk-launch() {
  local ret=1

  if [[ $service == "gtk-launch" ]]; then
    _arguments -S -A "-*" \
      '(- *)'{-h,--help}'[Show help options]' \
      '(- *)'{-v,--version}'[Show program version]' \
      '(- *)'--help-all'[Show all help options]' \
      '(- *)'--help-gtk'[Show GTK+ Options]' \
      '(- *)'--display='[X display to use]:display:_x_display' \
      '--class=[Program class as used by the window manager]:class' \
      '--name=[Program name as used by the window manager]:name' \
      '--gtk-module=[Load additional GTK+ modules]:module' \
      '--g-fatal-warnings[Make all warnings fatal]' \
      '1: :_gtk-launch-apps' \
      '*:: :_files' && ret=0
  else
    # gtk4-launch
    _arguments -S -A "-*" \
      '(- *)'{-h,--help}'[Show help options]' \
      '(- *)'{-v,--version}'[Show program version]' \
      '1: :_gtk-launch-apps' \
      '*:: :_files' && ret=0
  fi

  return ret
}

(( $+functions[_gtk-launch-apps] )) ||
_gtk-launch-apps() {
  local -a apps=(/usr/share/applications/*.desktop(:r:t))
  _values 'applications' $apps
}

_gtk-launch "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
