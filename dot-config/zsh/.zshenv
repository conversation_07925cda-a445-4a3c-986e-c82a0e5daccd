# ==============================================================================
# CRITICAL STARTUP STANZA - MUST BE FIRST
# Sets essential environment variables before any other shell initialization
# ==============================================================================

# Debug zsh_debug_echo function: always echoes, conditionally logs
zsh_debug_echo() {
    echo "$@"
    if [[ "${ZSH_DEBUG:-0}" == "1" ]]; then
        print -r -- "$@" >> "$ZSH_DEBUG_LOG"
    fi
}

# EMERGENCY IFS PROTECTION - Prevent corruption during startup
# This must be the very first thing to run
if [[ "$IFS" != $' \t\n' ]]; then
    unset IFS
    IFS=$' \t\n'
    export IFS
fi

# Emergency PATH fix if corrupted with $sep
if [[ "$PATH" == *'$sep'* ]]; then
    PATH="${PATH//\$sep/:}"
    export PATH
fi

# Set a minimal, safe PATH immediately to avoid command-not-found errors
export PATH="/opt/homebrew/bin:/run/current-system/sw/bin:/usr/local/bin:/usr/bin:/bin:/usr/local/sbin:/usr/sbin:/sbin"

# Unified prioritized PATH (Homebrew first if present, then XDG user bin, then standard system paths)
export PATH_BASE=$PATH

# XDG Base Directory Specification - set immediately (must precede PATH build so XDG_BIN_HOME can be included)
export XDG_CONFIG_HOME="${XDG_CONFIG_HOME:-${HOME}/.config}"
export XDG_CACHE_HOME="${XDG_CACHE_HOME:-${HOME}/.cache}"
export XDG_DATA_HOME="${XDG_DATA_HOME:-${HOME}/.local/share}"
export XDG_STATE_HOME="${XDG_STATE_HOME:-${HOME}/.local/state}"
export XDG_BIN_HOME="${XDG_BIN_HOME:-${HOME}/.local/bin}"
# Ensure critical directories exist immediately
mkdir -p "${XDG_CONFIG_HOME}" "${XDG_CACHE_HOME}" "${XDG_DATA_HOME}" "${XDG_STATE_HOME}" "${XDG_BIN_HOME}" 2>/dev/null || true
# XDG user bin (prepend if exists)
[[ -d ${XDG_BIN_HOME} ]] && PATH_BASE="${XDG_BIN_HOME}:${PATH_BASE}"
export PATH="${PATH_BASE}"

# Set ZDOTDIR, ZSH_CACHE_DIR, and ZSH_LOG_DIR as early as possible
export ZDOTDIR="${XDG_CONFIG_HOME}/zsh"
# Use BSD-compatible date format instead of GNU format
export ZSH_LOG_DIR="$ZDOTDIR/logs/$(date +%Y-%m-%d 2>/dev/null || zsh_debug_echo 'unknown')"
export ZSH_CACHE_DIR="${XDG_CACHE_HOME}/zsh"
mkdir -p "$ZDOTDIR" "$ZSH_CACHE_DIR" "$ZSH_LOG_DIR" 2>/dev/null || true

# Platform-specific XDG runtime directory
if [[ "$(uname -s)" == "Darwin" ]]; then
    export XDG_RUNTIME_DIR="${XDG_RUNTIME_DIR:-${HOME}/Library/Caches/TemporaryItems}"
else
    export XDG_RUNTIME_DIR="${XDG_RUNTIME_DIR:-/run/user/$(id -u)}"
fi

# Save PATH_BASE for debug reporting (DISABLED - no longer needed)
# DEBUG_PATH_BASE="$PATH_BASE"
# Store original PATH_BASE for debug checks (DISABLED - debug_check_path_base is disabled)
# export ZSH_PATH_BASE_ORIG="$PATH_BASE"
unset PATH_BASE

# ==============================================================================

export ZSH_SESSION_ID="${ZSH_SESSION_ID:-$$-$(date +%s 2>/dev/null || zsh_debug_echo $RANDOM)-$RANDOM}"
export ZSH_DEBUG_LOG="$ZSH_LOG_DIR/$ZSH_SESSION_ID-zsh-debug.log"

# Function to check for missing PATH_BASE entries in PATH (DISABLED)
debug_check_path_base() {
    # DISABLED - This function was causing performance issues and excessive output
    # The PATH_BASE validation is no longer needed since PATH is now properly configured
    return 0

    # Original code commented out:
    # [[ -z "$ZSH_PATH_BASE_ORIG" ]] && return 0
    # local IFS=:
    # local -a base_parts path_parts
    # base_parts=(${=ZSH_PATH_BASE_ORIG})
    # path_parts=(${=PATH})
    # for dir in "${base_parts[@]}"; do
    #     local found=0
    #     for p in "${path_parts[@]}"; do
    #         [[ "$dir" == "$p" ]] && found=1 && break
    #     done
    #     if (( ! found )); then
    #             zsh_debug_echo "⚠️  PATH_BASE entry not found in PATH: $dir"
    #     fi
    # done
}
# REMOVED: autoload -Uz debug_check_path_base - function is defined inline above

zsh_debug_echo "[DEBUG] .zshenv sourced, ZSH_LOG_DIR=$ZSH_LOG_DIR" >> "$ZSH_DEBUG_LOG"

# Debug: Report missing PATH_BASE entries in PATH (DISABLED)
# This debug check has been disabled as it was causing performance issues
# and is no longer needed since PATH is now properly configured
# if [[ "$ZSH_DEBUG" == "1" ]]; then
#     local IFS=:
#     local -a base_parts path_parts
#     base_parts=(${=DEBUG_PATH_BASE})
#     path_parts=(${=PATH})
#     for dir in "${base_parts[@]}"; do
#         local found=0
#         for p in "${path_parts[@]}"; do
#             [[ "$dir" == "$p" ]] && found=1 && break
#         done
#         if (( ! found )); then
#             zsh_debug_echo "⚠️  PATH_BASE entry not found in PATH: $dir"
#         fi
#     done
#     unset DEBUG_PATH_BASE
# fi

# Reusable PATH de-duplication function (preserve first occurrence ordering)
path_dedupe() {
    # Option flags: --verbose to print summary, --dry-run to show changes only
    local verbose=0 dry=0
    for arg in "$@"; do
        case $arg in
            --verbose) verbose=1 ;;
            --dry-run) dry=1 ;;
        esac
    done
    local original=$PATH
    local -a parts new
    local -A seen
    parts=(${=PATH})
    for p in "${parts[@]}"; do
        [[ -z $p ]] && continue
        if [[ -z ${seen[$p]:-} ]]; then
            new+="$p"
            seen[$p]=1
        fi
    done
    local deduped="${(j.:.)new}"
    if (( dry )); then
        if [[ $deduped != $original ]]; then
            print -r -- "$deduped"
        else
            print -r -- "$original"
        fi
        return 0
    fi
    if [[ $deduped != $original ]]; then
        PATH=$deduped
        export PATH
    fi
    export PATH_DEDUP_DONE=1
    (( verbose )) && print -u2 "[path_dedupe] entries(before)=${#parts} entries(after)=${#new}" \
        && (( verbose )) && [[ $deduped != $original ]] && print -u2 "[path_dedupe] modified" || (( verbose )) && print -u2 "[path_dedupe] no change"
}

# Initial de-duplication (idempotent; safe to re-run later during startup if PATH mutated)
path_dedupe

# Robust SHORT_HOST calculation with fallbacks
if [[ -z "$SHORT_HOST" ]]; then
    if [[ "$OSTYPE" = darwin* ]]; then
        SHORT_HOST=$(scutil --get LocalHostName 2>/dev/null) || SHORT_HOST="${HOST/.*/}" || SHORT_HOST=$(hostname -s 2>/dev/null) || SHORT_HOST="localhost"
    else
        SHORT_HOST="${HOST/.*/}" || SHORT_HOST=$(hostname -s 2>/dev/null) || SHORT_HOST="localhost"
    fi
fi

# Set robust ZSH_COMPDUMP path
export ZSH_COMPDUMP="${ZSH_CACHE_DIR}/.zcompdump-${SHORT_HOST}-${ZSH_VERSION}"
# Ensure ZSH_COMPDUMP is writable
touch "$ZSH_COMPDUMP" 2>/dev/null || {
    export ZSH_COMPDUMP="${HOME}/.zcompdump-${SHORT_HOST}-${ZSH_VERSION}"
    touch "$ZSH_COMPDUMP" 2>/dev/null || {
        export ZSH_COMPDUMP="/tmp/.zcompdump-${USER:-$(whoami)}-${ZSH_VERSION}"
        touch "$ZSH_COMPDUMP" 2>/dev/null
    }
}

# Essential shell variables for startup
export ZSH_DEBUG="${ZSH_DEBUG:-0}"
export HISTFILE="${ZDOTDIR}/.zsh_history"
export SHELL_SESSIONS_DISABLE=1  # Disable macOS session restore

# Conditionally disable verbose output during normal startup
# Allow debugging when ZSH_DEBUG_VERBOSE is set
if [[ -z "$ZSH_DEBUG_VERBOSE" ]]; then
    setopt NO_VERBOSE
    # setopt NO_XTRACE  # Commented out to allow 040-testing and debugging
    # Also disable function tracing globally
    setopt NO_FUNCTION_ARGZERO
fi

# Essential ZSH options that should apply to ALL shells (interactive and non-interactive)
# These options are fundamental to shell behavior and should be set early

# === Globbing Options (Universal) ===
setopt EXTENDED_GLOB            # Enable extended globbing patterns
setopt NULLGLOB                 # Return empty list for unmatched globs
unsetopt NOMATCH                # Don't error on unmatched patterns

# === Basic Shell Behavior (Universal) ===
setopt CORRECT                  # Correct spelling for commands
unsetopt CORRECTALL             # Don't correct arguments/filenames

# === History Options (Universal) ===
# Basic history behavior that should work in all shell types
setopt APPEND_HISTORY           # Append to history file rather than replace
setopt EXTENDED_HISTORY         # Save timestamp and duration
setopt HIST_IGNORE_SPACE        # Ignore commands starting with space
setopt HIST_REDUCE_BLANKS       # Remove superfluous blanks from history
setopt HIST_VERIFY              # Show command with history expansion before running

# === COMPREHENSIVE ZSH OPTIONS REFERENCE ===
# Additional options suitable for all shells - commented with defaults
# Uncomment and modify as needed for your specific requirements

# === Globbing and Pattern Matching ===
# setopt BAD_PATTERN            # (default: on) Print an error for malformed glob patterns
# setopt BARE_GLOB_QUAL         # (default: off) Allow qualifiers after patterns without parentheses
# setopt BRACE_CCL              # (default: off) Expand {a-z} in brace expansion
# setopt CASE_GLOB              # (default: on) Case sensitive globbing
# setopt CASE_MATCH             # (default: on) Case sensitive pattern matching in case statements
# setopt CSH_NULL_GLOB          # (default: off) Don't error if no matches, just remove pattern
# setopt EQUALS                 # (default: on) Perform = filename expansion
# setopt GLOB                   # (default: on) Perform filename generation (globbing)
# setopt GLOB_ASSIGN            # (default: off) Glob the right side of assignments
# setopt GLOB_COMPLETE          # (default: off) Use glob patterns for completion
# setopt GLOB_DOTS              # (default: off) Don't require leading . to be matched explicitly
# setopt GLOB_STAR_SHORT        # (default: off) ** is equivalent to **/*
# setopt GLOB_SUBST             # (default: off) Treat output of parameter expansion as glob pattern
# setopt MARK_DIRS              # (default: on) Add trailing / to directory names from glob
# setopt MULTIBYTE              # (default: on) Enable multibyte character support
# setopt NUMERIC_GLOB_SORT      # (default: off) Sort numeric filenames numerically
# setopt RC_EXPAND_PARAM        # (default: off) Array expansion with foo${arr}bar
# setopt REMATCH_PCRE           # (default: off) Use PCRE library for regex matching
# setopt SH_GLOB                # (default: off) Disable special meaning of (, |, ) in patterns
# setopt UNSET                  # (default: on) Treat unset parameters as having empty values
# setopt WARN_CREATE_GLOBAL     # (default: off) Warn when creating global parameter in function

# === Command Execution and Job Control ===
# setopt AUTO_CONTINUE          # (default: off) Send CONT to disowned jobs when shell exits
# setopt AUTO_RESUME            # (default: off) Resume job on fg/bg without %
# setopt BG_NICE                # (default: on) Run background jobs at lower priority
# setopt CHECK_JOBS             # (default: on) Warn about running jobs on shell exit
# setopt CHECK_RUNNING_JOBS     # (default: on) Check for running jobs, not just suspended
# setopt EXEC                   # (default: on) Do execute commands (off for syntax check only)
# setopt HUP                    # (default: on) Send HUP to running jobs when shell exits
# setopt LONG_LIST_JOBS         # (default: off) List jobs in long format by default
# setopt MONITOR                # (default: on) Enable job control
# setopt NO_CLOBBER             # (default: off) Don't overwrite files with > redirection
# setopt NOTIFY                 # (default: on) Report job status immediately
# setopt POSIX_JOBS             # (default: off) Use POSIX job control
# setopt RM_STAR_SILENT         # (default: off) Don't query user before rm * is executed
# setopt RM_STAR_WAIT           # (default: off) Wait 10 seconds before rm * can proceed

# === Input/Output and Redirection ===
# setopt APPEND_CREATE          # (default: off) Allow >> to create files
# setopt CLOBBER                # (default: on) Allow > redirection to overwrite files
# setopt CLOBBER_EMPTY          # (default: off) Allow > to overwrite empty files
# setopt CORRECT_ALL            # (default: off) Try to correct all arguments in line
# setopt FLOW_CONTROL           # (default: on) Enable output flow control via start/stop chars
# setopt IGNORE_EOF             # (default: off) Don't exit on EOF (Ctrl-D)
# setopt MAIL_WARNING           # (default: off) Warn if mail file has been accessed
# setopt PATH_DIRS              # (default: on) Search PATH for command with / in name
# setopt PRINT_EXIT_VALUE       # (default: off) Print exit value if non-zero
# setopt RC_QUOTES              # (default: off) Allow '' to represent single quote in strings
# setopt SHORT_LOOPS            # (default: on) Allow short forms of for, repeat, select, if, function

# === Variable Expansion and Parameter Handling ===
# setopt BASH_REMATCH           # (default: off) Use BASH_REMATCH for regex captures
# setopt BSD_ECHO               # (default: off) Make     zsh_debug_echo not interpret escape sequences
# setopt C_BASES                # (default: off) Output hex/octal in C format (0xFF, 077)
# setopt C_PRECEDENCES          # (default: off) Use C operator precedences
# setopt KSH_ARRAYS             # (default: off) Use ksh array indexing (0-based)
# setopt KSH_TYPESET            # (default: off) Use ksh-style typeset behavior
# setopt MAGIC_EQUAL_SUBST      # (default: on) Expand foo=~/bar to foo=/home/<USER>/bar
# setopt POSIX_ALIASES          # (default: off) Use POSIX alias expansion rules
# setopt POSIX_BUILTINS         # (default: off) Use POSIX builtin behavior
# setopt POSIX_IDENTIFIERS      # (default: off) Use POSIX identifier rules
# setopt POSIX_STRINGS          # (default: off) Use POSIX string quoting
# setopt POSIX_TRAPS            # (default: off) Use POSIX trap behavior
# setopt SH_FILE_EXPANSION      # (default: off) Use sh-style filename expansion
# setopt SH_NULLCMD             # (default: off) Use sh-style behavior for null commands
# setopt SH_OPTION_LETTERS      # (default: off) Allow single letter option names
# setopt SH_WORD_SPLIT          # (default: off) Split unquoted parameter expansions
# setopt TYPE_SET_TO_PATH       # (default: off) Use absolute path in type builtin
# setopt WARN_NESTED_VAR        # (default: off) Warn about nested variable assignments

# === Error Handling and Debugging ===
# setopt ERR_EXIT               # (default: off) Exit immediately if command returns non-zero
# setopt ERR_RETURN             # (default: off) Return from function if command returns non-zero
# setopt FUNCTION_ARGZERO       # (default: on) Set $0 to function name when executing function
# setopt LOCAL_LOOPS            # (default: off) Make break/continue work in functions
# setopt LOCAL_OPTIONS          # (default: off) Options set in functions are local
# setopt LOCAL_PATTERNS         # (default: off) Pattern character settings are local
# setopt LOCAL_TRAPS            # (default: off) Trap settings in functions are local
# setopt MULTI_FUNC_DEF         # (default: on) Allow multiple function definitions per line
# setopt SOURCE_TRACE           # (default: off) Show trace output for sourced files
# setopt TYPESET_SILENT         # (default: off) Make typeset not print parameter values
# setopt VERBOSE                # (default: off) Print shell input lines as they are read
# setopt XTRACE                 # (default: off) Print commands as they are executed

# === Command Line Editing (Non-Interactive) ===
# These affect command line parsing but not interactive editing
# setopt COMBINING_CHARS        # (default: on) Combine zero-width characters with base chars
# setopt PROMPT_BANG            # (default: on) Perform ! expansion in prompts
# setopt PROMPT_PERCENT         # (default: on) Perform % expansion in prompts
# setopt PROMPT_SUBST           # (default: off) Perform parameter expansion in prompts
# setopt TRANSIENT_RPROMPT      # (default: off) Remove RPROMPT from previous lines

# === Miscellaneous Universal Options ===
# setopt ALIASES                # (default: on) Enable alias expansion
# setopt ALL_EXPORT             # (default: off) Export all parameters
# setopt ALWAYS_LAST_PROMPT     # (default: off) Always go to last line of prompt
# setopt ALWAYS_TO_END          # (default: off) Move cursor to end after completion
# setopt APPEND_HISTORY         # (default: off) Append to history file rather than replace
# setopt AUTO_NAME_DIRS         # (default: off) Parameters become directory names
# setopt AUTO_PARAM_KEYS        # (default: on) Remove trailing chars that don't match
# setopt AUTO_PARAM_SLASH       # (default: on) Add slash after directory parameter
# setopt AUTO_REMOVE_SLASH      # (default: on) Remove trailing slash when not needed
# setopt BEEP                   # (default: on) Beep on error in ZLE
# setopt CHASE_DOTS             # (default: off) Resolve .. in cd paths
# setopt CHASE_LINKS            # (default: off) Resolve symbolic links in cd
# setopt CLOBBER                # (default: on) Allow > redirection to overwrite files
# setopt CONTINUE_ON_ERROR      # (default: off) Continue after syntax errors
# setopt DEBUG_BEFORE_CMD       # (default: off) Run DEBUG trap before each command
# setopt DVORAK                 # (default: off) Use Dvorak keyboard layout for line editor
# setopt EMACS                  # (default: off unless -o vi) Use emacs-style line editing
# setopt SINGLE_COMMAND         # (default: off) Exit after reading and executing one command
# setopt SUN_KEYBOARD_HACK      # (default: off) Ignore invalid characters from Sun keyboards
# setopt VI                     # (default: off unless -o emacs) Use vi-style line editing

# Note: Interactive-specific options (completion, key bindings, prompts)
# remain in 00_60-options.zsh and only apply to interactive shells

# ==============================================================================
# END CRITICAL STARTUP STANZA
# ==============================================================================

# ==============================================================================
# DISABLE WARP TERMINAL HOOKS (fixes tee issue) - AGGRESSIVE VERSION
# ==============================================================================

# Function to completely remove Warp hooks
_remove_warp_hooks() {
    # Clear the hook arrays completely
    unset precmd_functions 2>/dev/null
    unset preexec_functions 2>/dev/null

    # Re-initialize as empty arrays
    typeset -ga precmd_functions
    typeset -ga preexec_functions
    precmd_functions=()
    preexec_functions=()

    # Remove specific Warp functions if they exist
    unfunction warp_set_title_idle_on_precmd 2>/dev/null
    unfunction warp_set_title_active_on_preexec 2>/dev/null

    # Set a flag to indicate we've cleaned hooks
    typeset -g _WARP_HOOKS_DISABLED=1
}
autoload -Uz remove_warp_hooks

# Call the removal function immediately
_remove_warp_hooks

# Create a persistent hook remover that runs before every command
# This will catch and remove Warp hooks if they get reinstalled
_persistent_warp_hook_remover() {
    # Only run if we detect Warp hooks have been reinstalled
    if [[ ${#precmd_functions[@]} -gt 0 || ${#preexec_functions[@]} -gt 0 ]]; then
        local has_warp=0

        # Check for Warp-specific functions
        for func in "${precmd_functions[@]}" "${preexec_functions[@]}"; do
            if [[ "$func" == *warp* ]]; then
                has_warp=1
                break
            fi
        done

        if [[ $has_warp -eq 1 ]]; then
            # Warp has reinstalled hooks, remove them again
            _remove_warp_hooks
        fi
    fi
}
autoload -Uz _persistent_warp_hook_remover

# Install our persistent remover as a precmd hook
# This ensures it runs before every prompt
if [[ -z "$_WARP_REMOVER_INSTALLED" ]]; then
    precmd_functions=(_persistent_warp_hook_remover)
    typeset -g _WARP_REMOVER_INSTALLED=1
fi

# ==============================================================================

# ==============================================================================
# WARP TERMINAL TEE ISSUE RESOLVED
# The tee error was caused by Warp Terminal's precmd/preexec hooks.
# The aggressive hook removal system above prevents this issue.
# Diagnostic logging removed since issue is resolved.
# ==============================================================================

export ZSH_DEBUG=${ZSH_DEBUG:-0}

[[ $ZSH_DEBUG == 1 ]] && {
    zsh_debug_echo "# ++++++ $0 ++++++++++++++++++++++++++++++++++++"
    # Add this check to detect errant file creation:
    if [[ -f "${ZDOTDIR:-$HOME}/2" ]] || [[ -f "${ZDOTDIR:-$HOME}/3" ]]; then
            zsh_debug_echo "Warning: Numbered files detected - check for redirection typos"
    fi
}

# Developer tools PATH - these append to the basic PATH set above
[[ -d "/Applications/Xcode.app/Contents/Developer/usr/bin" ]] && PATH="$PATH:/Applications/Xcode.app/Contents/Developer/usr/bin"
[[ -d "/Library/Developer/CommandLineTools/usr/bin" ]] && PATH="$PATH:/Library/Developer/CommandLineTools/usr/bin"

# Safe PATH de-duplication (preserve first occurrence ordering)
# (Replaced by path_dedupe function; re-run here only if developer tools added anything duplicate)
path_dedupe

# Export build environment variables IMMEDIATELY
export CC="/usr/bin/cc"
export CXX="/usr/bin/c++"
export CPP="/usr/bin/cpp"
export DEVELOPER_DIR="${DEVELOPER_DIR:-/Applications/Xcode.app/Contents/Developer}"

# Enhanced command existence checking with caching
# Moved from 00_02-standard-helpers.zsh for broader availability
declare -gA _zsh_command_cache

has_command() {
    local cmd="$1"
    local cache_key="cmd_$cmd"

    [[ -z "$cmd" ]] && return 1

    # Check cache first
    if [[ -n "${_zsh_command_cache[$cache_key]}" ]]; then
        [[ "${_zsh_command_cache[$cache_key]}" == "1" ]] && return 0 || return 1
    fi

    # Check command existence
    if command -v "$cmd" >/dev/null 2>&1; then
        _zsh_command_cache[$cache_key]="1"
        return 0
    else
        _zsh_command_cache[$cache_key]="0"
        return 1
    fi
}

# Legacy alias for backward compatibility
command_exists() { has_command "$@"; }

# Clear command cache utility
clear_command_cache() {
    _zsh_command_cache=()
    [[ "$ZSH_DEBUG" == "1" ]] && zsh_debug_echo "# [.zshenv] Command cache cleared"
}

# DISABLED - POTENTIALLY CAUSING HANGS: Prevent duplicate entries in PATH and FPATH arrays
# typeset -x PATH FPATH
# typeset -aUx path fpath

# PATH deduplication disabled for now due to hangs
# (Legacy comment retained; active dedupe now handled by path_dedupe())

# Create safe command wrappers (using direct paths to avoid alias conflicts)
safe_uname() { [[ -x "/usr/bin/uname" ]] && /usr/bin/uname "$@" || zsh_debug_echo "unknown"; }
safe_sed() { [[ -x "/usr/bin/sed" ]] && /usr/bin/sed "$@" || zsh_debug_echo "sed not available"; }

# Safe git wrapper - prioritizes Homebrew git over system git
safe_git() {
    local git_cmd

    # Prefer Homebrew git if available, then fall back to system git
    if [[ -x "/opt/homebrew/bin/git" ]]; then
        git_cmd="/opt/homebrew/bin/git"
    elif [[ -x "/usr/local/bin/git" ]]; then
        git_cmd="/usr/local/bin/git"
    elif [[ -x "/usr/bin/git" ]]; then
        git_cmd="/usr/bin/git"
    else
        # Final fallback to PATH-based git
        git_cmd="git"
    fi

    $git_cmd "$@"
}

# Alias for legacy compatibility
alias _lazy_git_wrapper='safe_git'

# Safe mkdir wrapper - creates directories with error handling
safe_mkdir() {
    local mkdir_cmd="/bin/mkdir"
    [[ ! -x "$mkdir_cmd" ]] && mkdir_cmd="mkdir"

    # Create directories safely with error suppression for .zshenv context
    $mkdir_cmd "$@" 2>/dev/null || true
}

# Safe dirname wrapper - gets directory path with fallback
safe_dirname() {
    local dirname_cmd="/usr/bin/dirname"
    [[ ! -x "$dirname_cmd" ]] && dirname_cmd="dirname"

    if [[ -n "$1" ]]; then
        $dirname_cmd "$1" 2>/dev/null || zsh_debug_echo "."
    else
            zsh_debug_echo "."
    fi
}

# Cross-platform safe date command to fix "illegal time format" errors on macOS
safe_date() {
    local format="$1"
    shift

    # Use direct path to avoid alias conflicts
    local date_cmd="/bin/date"
    [[ ! -x "$date_cmd" ]] && date_cmd="date"

    # Handle common GNU date format patterns for macOS BSD date compatibility
    case "$format" in
        "+%s"|"-u +%s")
            # Unix timestamp - works on both
            if [[ "$OSTYPE" == darwin* ]]; then
                $date_cmd "$@"
            else
                $date_cmd "$format" "$@"
            fi
            ;;
        "+%Y%m%d_%H%M%S"|"-u +%Y%m%d_%H%M%S")
            # Timestamp format - works on both
            if [[ "$OSTYPE" == darwin* ]]; then
                $date_cmd "+%Y%m%d_%H%M%S" "$@"
            else
                $date_cmd "$format" "$@"
            fi
            ;;
        *)
            # Default to system date command
            if [[ -n "$format" ]]; then
                $date_cmd "$format" "$@"
            else
                $date_cmd "$@"
            fi
            ;;
    esac
}


# OS-specific compatibility aliases
case "$(uname -s)" in
    Darwin|*BSD*)
        # macOS/BSD: readlink doesn't have -f flag, use realpath instead
        if command_exists realpath; then
            alias readlink-f='realpath'
        else
            # Fallback for systems without realpath
            function readlink-f() {
                local target="$1"
                if [[ -L "$target" ]]; then
                    cd "$(dirname "$target")"
                    target="$(readlink "$(basename "$target")")"
                    # Handle relative symlinks
                    if [[ "$target" != /* ]]; then
                        target="$(pwd)/$target"
                    fi
                fi
                    zsh_debug_echo "$target"
            }
        fi
        ;;
    Linux*)
        # Linux: readlink has -f flag
        alias readlink-f='readlink -f'
        ;;
    *)
        # Unknown OS: try realpath first, fallback to basic readlink
        if command_exists realpath; then
            alias readlink-f='realpath'
        else
            alias readlink-f='readlink'
        fi
        ;;
esac

## [_path]
{
    _path_remove() {
        for ARG in "$@"; do
            while [[ ":${PATH}:" == *":${ARG}:"* ]]; do
                # Delete path by parts to avoid accidentally removing sub paths
                [[ "${PATH}" == "${ARG}" ]] && PATH=""
                PATH=${PATH//":${ARG}:"/":"} # delete any instances in the middle
                PATH=${PATH/#"${ARG}:"/}     # delete any instance at the beginning
                PATH=${PATH/%":${ARG}"/}     # delete any instance at the end
            done
        done
        export PATH
    }

    _path_append() {
        for ARG in "$@"; do
            _path_remove "${ARG}"
            [[ -d "${ARG}" ]] && export PATH="${PATH:+"${PATH}:"}${ARG}"
        done
    }

    _path_prepend() {
        for ARG in "$@"; do
            _path_remove "${ARG}"
            [[ -d "${ARG}" ]] && export PATH="${ARG}${PATH:+":${PATH}"}"
        done
    }
}

# Debug output when ZSH_DEBUG is enabled
if [[ "$ZSH_DEBUG" == "1" ]]; then
        zsh_debug_echo "[DEBUG .zshenv] SHORT_HOST: $SHORT_HOST"
        zsh_debug_echo "[DEBUG .zshenv] ZSH_COMPDUMP: $ZSH_COMPDUMP"
        zsh_debug_echo "[DEBUG .zshenv] ZSH_CACHE_DIR: $ZSH_CACHE_DIR"
        zsh_debug_echo "[DEBUG .zshenv] ZSH_COMPDUMP exists: $([[ -f "$ZSH_COMPDUMP" ]] &&     zsh_debug_echo 'YES' || zsh_debug_echo 'NO')"
        zsh_debug_echo "[DEBUG .zshenv] ZSH_COMPDUMP writable: $([[ -w "$ZSH_COMPDUMP" ]] &&     zsh_debug_echo 'YES' || zsh_debug_echo 'NO')"
fi

typeset -ga ZSH_AUTOSUGGEST_STRATEGY
ZSH_AUTOSUGGEST_STRATEGY=(history completion)

typeset -ga GLOBALIAS_FILTER_VALUES
GLOBALIAS_FILTER_VALUES=("sudo" "man" "which" "bob")

# Plugin manager setup - zgenom configuration
# Resolve ZDOTDIR to actual path (not symlink) for consistency
ZDOTDIR="$(realpath "${ZDOTDIR}")"
ZGENOM_PARENT_DIR="${ZDOTDIR}"
ZGEN_SOURCE="${ZDOTDIR}/.zqs-zgenom"
ZGENOM_SOURCE_FILE="${ZDOTDIR}/.zqs-zgenom/zgenom.zsh"
ZGEN_DIR="${ZDOTDIR}/.zgenom"
ZGEN_INIT="${ZGEN_DIR}/init.zsh"
ZGENOM_BIN_DIR="${ZGEN_DIR}/_bin"

export ZDOTDIR ZGENOM_PARENT_DIR ZGEN_SOURCE ZGENOM_SOURCE_FILE ZGEN_DIR ZGEN_INIT ZGENOM_BIN_DIR

# Oh-My-ZSH configuration for zgenom
export ZGEN_OH_MY_ZSH_REPO="ohmyzsh/ohmyzsh"
export ZGEN_OH_MY_ZSH_BRANCH="master"

# Ensure zgenom functions are in fpath for autoloading
if [[ -d "${ZGEN_SOURCE}/functions" ]]; then
    fpath=("${ZGEN_SOURCE}/functions" $fpath)
fi

# Disable macOS session restore
export SHELL_SESSIONS_DISABLE=1

# History configuration
export HISTDUP=erase
export HISTFILE="${ZDOTDIR}/.zsh_history"
export HISTSIZE=2000000
export HISTTIMEFORMAT='%F %T %z %a %V '
export SAVEHIST=2000200

# Core application settings
export DISPLAY=:0.0
export LANG='en_GB.UTF-8'
export LC_ALL='en_GB.UTF-8'
export TIME_STYLE=long-iso

set_editor() {
    # Set editors if available (with fallback)
    local editors=(nvim hx vim nano)
    for e in $editors; do
        [[ -n "$e" ]] &&
            command -v "$e" >/dev/null 2>&1 &&
            { export EDITOR="$e"; break; }
    done
}

set_visual() {
    # Set editors if available (with fallback)
    set_editor
    local editors=(code-insiders code zed hx "$EDITOR")
    for e in $editors; do
        [[ -n "$e" ]] &&
            command -v "$e" >/dev/null 2>&1 &&
            { export VISUAL="$e"; break; }
    done
}

set_visual

# Java setup (macOS specific)
if [[ -x "/usr/libexec/java_home" ]]; then
    JAVA_HOME="$(/usr/libexec/java_home 2>/dev/null)" && export JAVA_HOME
fi

# Less configuration with color support
export LESS=' --HILITE-UNREAD --LONG-PROMPT --no-histdups --ignore-case --incsearch --no-init --line-numbers --mouse --quit-if-one-screen --squeeze-blank-lines --status-column --tabs=4 --use-color --window=-4 --RAW-CONTROL-CHARS '

# D-Bus session setup (Linux compatibility)
export MY_SESSION_BUS_SOCKET="/tmp/dbus/${USER}.session.usock"
export DBUS_SESSION_BUS_ADDRESS="unix:path=${MY_SESSION_BUS_SOCKET}"

# MCP server allowed directories for file operations
export ALLOWED_DIRECTORIES="/Users/<USER>/Desktop,/Users/<USER>/Downloads,/Users/<USER>/Herd,/Users/<USER>/Library,/Users/<USER>/Projects,/Users/<USER>/Work,/Users/<USER>/.config,/Users/<USER>/dotfiles,/Users/<USER>/.local,/Users/<USER>/.zshenv,/Users/<USER>/.zshrc,/Users/<USER>/.zprofile,/Users/<USER>/.zlogin"


## [_field]
{
    _field_append() {
        ## SYNOPSIS: field_append varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## Note: Forces fieldVal into the last position, if already present.
        ##             Duplicates are removed, too.
        ## EXAMPLE: field_append PATH /usr/local/bin
        local varName=$1 fieldVal=$2 sep=${3:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        auxArr=(${auxArr:#$fieldVal})            # ZSH: remove all instances of fieldVal
        auxArr+=($fieldVal)                      # ZSH: append to array
        # Use printf to ensure proper interpolation of separator
        local joined_result
        if [[ ${#auxArr[@]} -eq 0 ]]; then
            joined_result=""
        else
            printf -v joined_result '%s' "${auxArr[1]}"
            for element in "${auxArr[@]:1}"; do
                joined_result="${joined_result}${sep}${element}"
            done
        fi
        eval "$varName=\$joined_result"
    }

    _field_contains() {
        ## SYNOPSIS: field_contains varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_contains PATH /usr/local/bin
        local varName=$1 fieldVal=$2 sep=${3:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        [[ ${auxArr[(ie)$fieldVal]} -le ${#auxArr} ]]  # ZSH: exact match index check
    }

    _field_delete() {
        ## SYNOPSIS: field_delete varName fieldNum [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_delete PATH 2
        local varName=$1 fieldNum=$2 sep=${3:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        auxArr[$fieldNum]=()                     # ZSH: remove element at index
        # Use printf to ensure proper interpolation of separator
        local joined_result
        if [[ ${#auxArr[@]} -eq 0 ]]; then
            joined_result=""
        else
            printf -v joined_result '%s' "${auxArr[1]}"
            for element in "${auxArr[@]:1}"; do
                joined_result="${joined_result}${sep}${element}"
            done
        fi
        eval "$varName=\$joined_result"
    }

    _field_find() {
        ## SYNOPSIS: field_find varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_find PATH /usr/local/bin
        local varName=$1 fieldVal=$2 sep=${3:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        [[ ${auxArr[(ie)$fieldVal]} -le ${#auxArr} ]]  # ZSH: exact match index check
    }

    _field_get() {
        ## SYNOPSIS: field_get varName fieldNum [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_get PATH 2
        local varName=$1 fieldNum=$2 sep=${3:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
            zsh_debug_echo "${auxArr[$fieldNum]}"              # ZSH: array element access
    }

    _field_insert() {
        ## SYNOPSIS: field_insert varName fieldNum fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_insert PATH 2 /usr/local/bin
        local varName=$1 fieldNum=$2 fieldVal=$3 sep=${4:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        auxArr=("${auxArr[@]:0:fieldNum}" "$fieldVal" "${auxArr[@]:fieldNum}")  # Insert at position
        # Use printf to ensure proper interpolation of separator
        local joined_result
        if [[ ${#auxArr[@]} -eq 0 ]]; then
            joined_result=""
        else
            printf -v joined_result '%s' "${auxArr[1]}"
            for element in "${auxArr[@]:1}"; do
                joined_result="${joined_result}${sep}${element}"
            done
        fi
        eval "$varName=\$joined_result"
    }

    _field_prepend() {
        ## SYNOPSIS: field_prepend varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## Note: Forces fieldVal into the first position, if already present.
        ##             Duplicates are removed, too.
        ## EXAMPLE: field_prepend PATH /usr/local/bin
        local varName=$1 fieldVal=$2 sep=${3:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        auxArr=(${auxArr:#$fieldVal})            # ZSH: remove all instances of fieldVal
        auxArr=($fieldVal "${auxArr[@]}")        # ZSH: prepend to array
        # Use printf to ensure proper interpolation of separator
        local joined_result
        if [[ ${#auxArr[@]} -eq 0 ]]; then
            joined_result=""
        else
            printf -v joined_result '%s' "${auxArr[1]}"
            for element in "${auxArr[@]:1}"; do
                joined_result="${joined_result}${sep}${element}"
            done
        fi
        eval "$varName=\$joined_result"
    }

    _field_remove() {
        ## SYNOPSIS: field_remove varName fieldVal [sep]
        ##     SEP defaults to ':'
        ## Note: Duplicates are removed, too.
        ## EXAMPLE: field_remove PATH /usr/local/bin
        local varName=$1 fieldVal=$2 sep=${3:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        auxArr=(${auxArr:#$fieldVal})            # ZSH: remove all instances of fieldVal
        # Use printf to ensure proper interpolation of separator
        local joined_result
        if [[ ${#auxArr[@]} -eq 0 ]]; then
            joined_result=""
        else
            printf -v joined_result '%s' "${auxArr[1]}"
            for element in "${auxArr[@]:1}"; do
                joined_result="${joined_result}${sep}${element}"
            done
        fi
        eval "$varName=\$joined_result"
    }

    _field_replace() {
        ## SYNOPSIS: field_replace varName fieldVal newFieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_replace PATH /usr/local/bin /usr/local/bin2
        local varName=$1 fieldVal=$2 newFieldVal=$3 sep=${4:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        auxArr=(${auxArr/$fieldVal/$newFieldVal}) # ZSH: replace all instances
        # Use printf to ensure proper interpolation of separator
        local joined_result
        if [[ ${#auxArr[@]} -eq 0 ]]; then
            joined_result=""
        else
            printf -v joined_result '%s' "${auxArr[1]}"
            for element in "${auxArr[@]:1}"; do
                joined_result="${joined_result}${sep}${element}"
            done
        fi
        eval "$varName=\$joined_result"
    }

    _field_set() {
        ## SYNOPSIS: field_set varName fieldNum fieldVal [sep]
        ##     SEP defaults to ':'
        ## EXAMPLE: field_set PATH 2 /usr/local/bin
        local varName=$1 fieldNum=$2 fieldVal=$3 sep=${4:-':'}
        local -a auxArr
        auxArr=(${(ps:$sep:)${(P)varName}})      # ZSH: split on separator, indirect expansion
        auxArr[$fieldNum]="$fieldVal"            # ZSH: set array element
        # Use printf to ensure proper interpolation of separator
        local joined_result
        if [[ ${#auxArr[@]} -eq 0 ]]; then
            joined_result=""
        else
            printf -v joined_result '%s' "${auxArr[1]}"
            for element in "${auxArr[@]:1}"; do
                joined_result="${joined_result}${sep}${element}"
            done
        fi
        eval "$varName=\$joined_result"
    }

    _field_test() {
        ## SYNOPSIS: _field_test
        ## EXAMPLE: _field_test
        ## DESCRIPTION: Test function for the _field library.
        local varName=PATH fieldNum=2 fieldVal=/usr/local/bin fieldVal2=/usr/local/bin2
        eval "$varName='/usr/bin:/usr/local/bin:/bin:/usr/sbin'"
        _field_get "$varName" "$fieldNum"
        _field_set "$varName" "$fieldNum" "$fieldVal"
        _field_insert "$varName" "$fieldNum" "$fieldVal"
        _field_delete "$varName" "$fieldNum"
        _field_find "$varName" "$fieldVal"
        _field_replace "$varName" "$fieldVal" "$fieldVal2"
        _field_contains "$varName" "$fieldVal"
        _field_append "$varName" "$fieldVal"
        _field_prepend "$varName" "$fieldVal"
        _field_append "$varName" "$fieldVal"
        _field_remove "$varName" "$fieldVal"
    }
}


# Load all environment files from .env directory
# This ensures API keys and other environment variables are available for all shell sessions,
# including non-interactive ones used by MCP servers
if [[ -d "${ZDOTDIR}/.env" ]]; then
    for env_file in "${ZDOTDIR}/.env"/*.env; do
        [[ -r "$env_file" ]] && source "$env_file"
    done
    unset env_file
fi

# History management functions
# Disables zsh history recording by overriding the zshaddhistory hook
# shellcheck disable=SC1073
disablehistory() {
    zshaddhistory() { return 1 }
}

# Re-enables zsh history recording by removing the zshaddhistory override
enablehistory() {
    unset -f zshaddhistory
}
