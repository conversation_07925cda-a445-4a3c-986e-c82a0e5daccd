---
###########################
###########################
## Linter GitHub Actions ##
###########################
###########################
name: Lint Code Base

#
# Documentation:
# https://help.github.com/en/articles/workflow-syntax-for-github-actions
#

#############################
# Start the job on all push #
#############################
on:
  push:
    branches-ignore: [main]
    # Remove the line above to run when pushing to main
  pull_request:
    branches: [main]

###############
# Set the Job #
###############
jobs:
  build:
    # Name the Job
    name: <PERSON>lint Code Base
    # Set the agent to run on
    runs-on: ubuntu-latest

    ##################
    # Load all steps #
    ##################
    steps:
      ##########################
      # Checkout the code base #
      ##########################
      - name: Checkout Code
        uses: actions/checkout@v5
        with:
          # Full git history is needed to get a proper list of changed files
          # within `mega-linter`
          fetch-depth: 0

      ################################
      # Run Linter against code base #
      ################################
      - name: Lint Code Base
        uses: megalinter/megalinter@v8
        env:
          VALIDATE_ALL_CODEBASE: false
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          DISABLE_LINTERS: MARKDOWN_MARKDOWN_LINK_CHECK,REPOSITORY_SECRETLINT,SPELL_CSPELL,SPELL_LYCHEE,REPOSITORY_DEVSKIM
          ACTION_ACTIONLINT_DISABLE_ERRORS: true
          REPOSITORY_CHECKOV_DISABLE_ERRORS: true
          REPOSITORY_KICS_DISABLE_ERRORS: true
          REPOSITORY_TRIVY_DISABLE_ERRORS: true

      # Upload Mega-Linter artifacts.
      # They will be available on Github action page "Artifacts" section
      - name: Archive production artifacts
        if: ${{ success() }} || ${{ failure() }}
        uses: actions/upload-artifact@v4
        with:
          name: Mega-Linter reports
          path: |
            mega-linter-reports
            mega-linter.log
