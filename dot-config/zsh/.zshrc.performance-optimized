#!/opt/homebrew/bin/zsh
# ==============================================================================
# High-Performance ZSH Configuration
# ==============================================================================
# Purpose: Optimized ZSH configuration targeting <300ms startup time
# Author: ZSH Configuration Management System
# Created: 2025-08-21
# Version: 1.0
# Target: <300ms startup time with full functionality
# ==============================================================================

# Performance optimization flags
export ZSH_DEBUG=0
export ZSH_DISABLE_COMPFIX=true
export ZSH_ENABLE_SANITIZATION=false  # Lazy load on demand
export ZSH_PERFORMANCE_MODE=true

# Set ZDOTDIR
export ZDOTDIR="${ZDOTDIR:-$HOME/.config/zsh}"

# ==============================================================================
# PHASE 1: ESSENTIAL CORE COMPONENTS (Target: <50ms)
# ==============================================================================

# 1. Source/Execute Detection (essential for all other components)
if [[ -f "$ZDOTDIR/.zshrc.d/00-core/01-source-execute-detection.zsh" ]]; then
    source "$ZDOTDIR/.zshrc.d/00-core/01-source-execute-detection.zsh"
fi

# 2. Standard Helpers (essential utilities)
if [[ -f "$ZDOTDIR/.zshrc.d/00-core/00-standard-helpers.zsh" ]]; then
    export ZSH_HELPERS_TESTING=1  # Skip auto-validation for speed
    source "$ZDOTDIR/.zshrc.d/00-core/00-standard-helpers.zsh"
fi

# 3. Basic Environment (essential paths and variables)
if [[ -f "$ZDOTDIR/.zshrc.d/00-core/01-environment.zsh" ]]; then
    source "$ZDOTDIR/.zshrc.d/00-core/01-environment.zsh"
fi

# 4. Path System (essential for command resolution)
if [[ -f "$ZDOTDIR/.zshrc.d/00-core/02-path-system.zsh" ]]; then
    source "$ZDOTDIR/.zshrc.d/00-core/02-path-system.zsh"
fi

# ==============================================================================
# PHASE 2: LAZY LOADING SYSTEM SETUP (Target: <100ms total)
# ==============================================================================

# 5. Setup lazy loading for heavy components
_setup_lazy_loading() {
    # SSH Agent - only load when SSH commands are used
    _lazy_ssh_setup() {
        if [[ -f "$ZDOTDIR/.zshrc.pre-plugins.d/03-secure-ssh-agent.zsh" ]]; then
            source "$ZDOTDIR/.zshrc.pre-plugins.d/03-secure-ssh-agent.zsh"
            unfunction _lazy_ssh_setup
        fi
    }
    
    # Override SSH commands to trigger lazy loading
    ssh() { _lazy_ssh_setup; command ssh "$@"; }
    ssh-add() { _lazy_ssh_setup; command ssh-add "$@"; }
    ssh-agent() { _lazy_ssh_setup; command ssh-agent "$@"; }
    
    # Git Config - only load when git commands are used
    _lazy_git_setup() {
        if [[ -f "$ZDOTDIR/.zshrc.pre-plugins.d/05-lazy-git-config.zsh.disabled" ]]; then
            mv "$ZDOTDIR/.zshrc.pre-plugins.d/05-lazy-git-config.zsh.disabled" \
               "$ZDOTDIR/.zshrc.pre-plugins.d/05-lazy-git-config.zsh"
            source "$ZDOTDIR/.zshrc.pre-plugins.d/05-lazy-git-config.zsh"
            unfunction _lazy_git_setup
        fi
    }
    
    # Override git command to trigger lazy loading
    git() { _lazy_git_setup; command git "$@"; }
    
    # Environment Sanitization - only load when explicitly requested
    sanitize_env() {
        export ZSH_ENABLE_SANITIZATION=true
        if [[ -f "$ZDOTDIR/.zshrc.d/00-core/08-environment-sanitization.zsh" ]]; then
            source "$ZDOTDIR/.zshrc.d/00-core/08-environment-sanitization.zsh"
            _sanitize_environment
        fi
    }
}

# Execute lazy loading setup
_setup_lazy_loading

# ==============================================================================
# PHASE 3: MINIMAL PLUGIN LOADING (Target: <200ms total)
# ==============================================================================

# 6. Load only essential plugins synchronously
_load_essential_plugins() {
    # Check if zgenom is available
    if [[ ! -f "$ZDOTDIR/.zgenom/zgenom.zsh" ]]; then
        return 0
    fi
    
    # Load zgenom
    source "$ZDOTDIR/.zgenom/zgenom.zsh"
    
    # Only load absolutely essential plugins synchronously
    if ! zgenom saved; then
        # Essential completion system
        zgenom oh-my-zsh
        
        # Essential plugins only
        zgenom oh-my-zsh plugins/git
        zgenom oh-my-zsh plugins/colored-man-pages
        
        # Generate the init script
        zgenom save
    fi
}

# Load essential plugins in background to avoid blocking
{
    _load_essential_plugins
} &

# ==============================================================================
# PHASE 4: DEFERRED LOADING SYSTEM (Target: <250ms total)
# ==============================================================================

# 7. Setup deferred loading for non-essential features
_setup_deferred_loading() {
    # Defer heavy plugins and features
    {
        sleep 0.1  # Small delay to ensure shell is ready
        
        # Load remaining plugins
        if [[ -f "$ZDOTDIR/.zgenom/zgenom.zsh" ]]; then
            source "$ZDOTDIR/.zgenom/zgenom.zsh"
            
            # Load additional plugins in background
            if ! zgenom saved; then
                # Additional useful plugins
                zgenom load zsh-users/zsh-autosuggestions
                zgenom load zsh-users/zsh-syntax-highlighting
                zgenom load zsh-users/zsh-history-substring-search
                
                # NVM with lazy loading
                zgenom oh-my-zsh plugins/nvm
                
                # Save configuration
                zgenom save
            fi
        fi
        
        # Load UI enhancements
        if [[ -f "$ZDOTDIR/.zshrc.d/30-ui/30-prompt-ui-config.zsh" ]]; then
            source "$ZDOTDIR/.zshrc.d/30-ui/30-prompt-ui-config.zsh"
        fi
        
        # Load aliases and functions
        if [[ -f "$ZDOTDIR/.zshrc.d/30-ui/32-aliases.zsh" ]]; then
            source "$ZDOTDIR/.zshrc.d/30-ui/32-aliases.zsh"
        fi
        
        # Load development tools
        if [[ -f "$ZDOTDIR/.zshrc.d/10-tools/10-development-tools.zsh" ]]; then
            source "$ZDOTDIR/.zshrc.d/10-tools/10-development-tools.zsh"
        fi
        
    } &
    disown
}

# Execute deferred loading
_setup_deferred_loading

# ==============================================================================
# PHASE 5: FINALIZATION (Target: <300ms total)
# ==============================================================================

# 8. Minimal prompt setup (defer fancy prompts)
if [[ -z "$PROMPT" ]]; then
    # Simple, fast prompt
    PROMPT='%F{blue}%~%f %# '
fi

# 9. Essential key bindings
bindkey '^R' history-incremental-search-backward
bindkey '^[[A' history-substring-search-up
bindkey '^[[B' history-substring-search-down

# 10. Completion system (minimal)
autoload -Uz compinit
if [[ -n ${ZDOTDIR}/.zcompdump(#qN.mh+24) ]]; then
    compinit
else
    compinit -C
fi

# 11. History configuration
HISTFILE="$ZDOTDIR/.zsh_history"
HISTSIZE=10000
SAVEHIST=10000
setopt HIST_IGNORE_DUPS
setopt HIST_IGNORE_SPACE
setopt SHARE_HISTORY

# 12. Essential options
setopt AUTO_CD
setopt CORRECT
setopt NO_BEEP

# ==============================================================================
# PHASE 6: BACKGROUND FINALIZATION
# ==============================================================================

# 13. Load remaining features in background
{
    sleep 0.2  # Ensure shell is fully ready
    
    # Load splash screen if available (non-blocking)
    if [[ -f "$ZDOTDIR/.zshrc.d/90-finalize/99-splash.zsh" ]]; then
        # Re-enable fastfetch for background loading
        sed -i.bak 's/# \[\[/[[/g; s/# fastfetch/fastfetch/g' \
            "$ZDOTDIR/.zshrc.d/90-finalize/99-splash.zsh" 2>/dev/null
        source "$ZDOTDIR/.zshrc.d/90-finalize/99-splash.zsh" 2>/dev/null
    fi
    
    # Load macOS defaults in background
    if [[ -f "$ZDOTDIR/.zshrc.pre-plugins.d/03-macos-defaults-deferred.zsh" ]]; then
        # Re-enable deferred macOS defaults
        sed -i.bak 's/# if \[\[/if [[/g; s/# fi/fi/g' \
            "$ZDOTDIR/.zshrc.pre-plugins.d/03-macos-defaults-deferred.zsh" 2>/dev/null
        source "$ZDOTDIR/.zshrc.pre-plugins.d/03-macos-defaults-deferred.zsh" 2>/dev/null
    fi
    
} &
disown

# ==============================================================================
# PERFORMANCE MONITORING
# ==============================================================================

# 14. Optional performance monitoring
if [[ "$ZSH_PERFORMANCE_MONITOR" == "true" ]]; then
    echo "High-performance ZSH configuration loaded"
    echo "Lazy loading enabled for: SSH, Git, Environment Sanitization"
    echo "Deferred loading enabled for: Plugins, UI, Development Tools"
fi

# ==============================================================================
# END: High-Performance ZSH Configuration
# ==============================================================================
