; X-JMnemonic theme for Fast-Syntax-Highlighting:
; https://github.com/zdharma/fast-syntax-highlighting
; Version: 1.1
;
; Copyright (c) 2018 <PERSON>
;
; Based on: https://www.syntaxenvy.com/0753499
;
; When I first saw the above theme upon syntaxenvy.com generated it randomly,
; I've had a quick, bold association with the Johnny Mnemonic movie. I don't
; know why, but I've decided to name the theme like this and who knows, maybe
; someone will decipher the connection one day. The theme looks suprisingly
; well IMO and maybe it's the effect of having this movie property/connection.
;
; This theme is a descent of the 40 work hours (or more) non-public theme X-Paragon,
; which was created to say `thanks' to the patrons at Patreon.com/psprint. It should
; carry the same quality.
;
; The palette - naive and CIE L*a*b conversion:
;
; main:
;   #5e6466 -> 59                       -> CIELab: 241
; comment:
;   #434749 -> 16                      -> CIELab: 238
; keyword:
;   #b77c4b -> 137                      -> CIELab: 173
; number, string:
;   #5794a2 -> 67                      -> CIELab: 66
; title, section, name, selector-id:
;   #778ce0 -> 104                      -> CIELab: 104
; attribute, variable, type:
;   #d55383 -> 168                      -> CIELab: 168
; symbol, link:
;   #e66493 -> 168                      -> CIELab: 168
; builtin, deletion:
;   #bd5ac0 -> 133                      -> CIELab: 170
; formula-bg:
;   #363a3b -> 16                      -> CIELab: 237
;
; Token.Literal: "#dc5be0"  -> CIELab: 170 (Orchid; naive: 170)
; Token.Operator: "#677dcf" -> CIELab: 68 (SteelBlue3; naive: 68)
;

[base]
default           = none
unknown-token     = 196
secondary         = sv-orple
recursive-base    = 183

[background]
correct-subtle   = bg:18
incorrect-subtle = bg:238
subtle-bg        = bg:17
global-alias     = bg:20

;;
;; COLOR-GROUPS
;;

[gray]
comment = 243



[pastel]
here-string-tri        = 217



[no-color]
assign               = none
back-quoted-argument = none
redirection          = none
variable             = none




[magenta-3]
dollar-quoted-argument = 173
double-quoted-argument = 173
history-expansion      = 173
globbing-ext           = 173
precommand             = 173

[light-salmon-3]
builtin                = 137
subcommand             = 137
single-quoted-argument = 137

[steel-blue-3]
command           = 68
double-sq-bracket = 68
double-paren      = 68
single-sq-bracket = 68

[steel-blue]
reserved-word          = 67



[medium-purple]
; backslash in $'...'
back-dollar-quoted-argument  = 104
commandseparator             = 104
single-hyphen-option         = 104

[dark-khaki]
double-hyphen-option   = 143



[hot-pink-3]
alias              = 168
exec-descriptor    = 168
function           = 168
hashed-command     = 168
here-string-var    = 168
suffix-alias       = 168

[pale-green-3]
assign-array-bracket                  = 114
; variable $... or backslash in "..." (i.e. variable in string)
back-or-dollar-double-quoted-argument = 114
globbing                              = 114
here-string-text                      = 114



[orchid]
path                   = 170
path-to-dir            = 170,underline
pathseparator          = 



;;
;; FUNCTIONALITY-GROUPS
;;

[brackets]
paired-bracket = black,bg:216
bracket-level-1 = 117
bracket-level-2 = 217
bracket-level-3 = 220

[math]
mathvar = 68
mathnum = 173
matherr = 124

[for-loop]
forvar = 68
fornum = 173
; operator
foroper = 133
; separator
forsep = 104

[case]
case-input       = 168
case-parentheses = 217
case-condition   = bg:25
