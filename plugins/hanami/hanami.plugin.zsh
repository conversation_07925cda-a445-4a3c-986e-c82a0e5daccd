alias HED='HANAMI_ENV=development '
alias HEP='HANAMI_ENV=production '
alias HET='HANAMI_ENV=test '

alias hc='hanami console'
alias hd='hanami destroy'
alias hg='hanami generate'
alias hgm='hanami generate migration'
alias hs='hanami server'
alias hsp='hanami server -p'
alias hr='hanami routes'
alias hdc='hanami db create'
alias hdd='hanami db drop'
alias hdp='hanami db prepare'
alias hda='hanami db apply'
alias hdv='hanami db version'
alias hdrs='hdd && hdp'
alias hdtp='HET hdp'
alias hrg='hr | grep'
