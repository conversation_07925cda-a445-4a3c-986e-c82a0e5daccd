#!/opt/homebrew/bin/zsh
# ==============================================================================
# ZSH Configuration: Weekly Security Maintenance Script
# ==============================================================================
# Purpose: Automated weekly security maintenance including environment
#          sanitization, security audits, configuration validation, and
#          performance monitoring with comprehensive logging and notifications.
# 
# Author: ZSH Configuration Management System
# Created: 2025-08-21
# Version: 1.0
# Usage: ./weekly-security-maintenance (cron job or manual execution)
# Schedule: Weekly (recommended: Sunday 2 AM)
# Dependencies: All ZSH security and validation components
# ==============================================================================

# ------------------------------------------------------------------------------
# 0. CONFIGURATION AND SETUP
# ------------------------------------------------------------------------------

# Script configuration
SCRIPT_VERSION="1.0.0"
ZDOTDIR="${ZDOTDIR:-$HOME/.config/zsh}"
USER_EMAIL="${USER_EMAIL:-$(git config user.email 2>/dev/null || echo "$USER@$(hostname)")}"

# Logging configuration
MAINTENANCE_LOG_DIR="$ZDOTDIR/logs/maintenance"
WEEKLY_LOG_FILE="$MAINTENANCE_LOG_DIR/weekly-maintenance-$(date -u '+%Y-%m-%d_%H-%M-%S').log"
SUMMARY_LOG_FILE="$MAINTENANCE_LOG_DIR/weekly-summary.log"
NOTIFICATION_LOG_FILE="$MAINTENANCE_LOG_DIR/notifications.log"

# Create maintenance log directory
mkdir -p "$MAINTENANCE_LOG_DIR" 2>/dev/null || true

# Notification configuration
NOTIFICATION_METHODS="${NOTIFICATION_METHODS:-console,log,email}"
NOTIFICATION_LEVEL="${NOTIFICATION_LEVEL:-summary}"  # summary, detailed, issues-only

# Performance thresholds
STARTUP_TIME_THRESHOLD_MS=5000  # Alert if startup > 5 seconds
SECURITY_ISSUES_THRESHOLD=5     # Alert if > 5 security issues found

# ------------------------------------------------------------------------------
# 1. UTILITY FUNCTIONS
# ------------------------------------------------------------------------------

# 1.1. Logging functions
log_maintenance() {
    local message="$1"
    local level="${2:-INFO}"
    local timestamp=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
    
    echo "[$timestamp] [$level] $message" | tee -a "$WEEKLY_LOG_FILE"
    
    # Also log to summary if important
    if [[ "$level" == "WARN" || "$level" == "ERROR" || "$level" == "SUMMARY" ]]; then
        echo "[$timestamp] [$level] $message" >> "$SUMMARY_LOG_FILE"
    fi
}

log_notification() {
    local message="$1"
    local timestamp=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
    echo "[$timestamp] [NOTIFICATION] $message" >> "$NOTIFICATION_LOG_FILE"
}

# 1.2. Notification functions
send_notification() {
    local subject="$1"
    local message="$2"
    local level="${3:-INFO}"
    
    log_notification "Sending notification: $subject"
    
    # Console notification (always enabled)
    if [[ "$NOTIFICATION_METHODS" == *"console"* ]]; then
        echo "🔔 NOTIFICATION: $subject"
        echo "$message"
    fi
    
    # Log notification
    if [[ "$NOTIFICATION_METHODS" == *"log"* ]]; then
        log_maintenance "NOTIFICATION: $subject - $message" "$level"
    fi
    
    # Email notification (if configured)
    if [[ "$NOTIFICATION_METHODS" == *"email"* ]] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "ZSH Security Maintenance: $subject" "$USER_EMAIL" 2>/dev/null || {
            log_maintenance "Failed to send email notification to $USER_EMAIL" "WARN"
        }
    fi
    
    # macOS notification (if available)
    if [[ "$NOTIFICATION_METHODS" == *"macos"* ]] && command -v osascript >/dev/null 2>&1; then
        osascript -e "display notification \"$message\" with title \"ZSH Security Maintenance\" subtitle \"$subject\"" 2>/dev/null || {
            log_maintenance "Failed to send macOS notification" "WARN"
        }
    fi
}

# ------------------------------------------------------------------------------
# 2. MAINTENANCE TASKS
# ------------------------------------------------------------------------------

# 2.1. Environment sanitization
run_environment_sanitization() {
    log_maintenance "Starting environment sanitization..."
    
    local sanitization_output
    local sanitization_exit_code
    
    # Run environment sanitization
    sanitization_output=$(ZSH_ENABLE_SANITIZATION=true \
                         ZSH_SANITIZATION_LOG_VIOLATIONS=true \
                         source "$ZDOTDIR/.zshrc.d/00-core/08-environment-sanitization.zsh" 2>&1)
    sanitization_exit_code=$?
    
    # Log detailed output
    echo "$sanitization_output" >> "$WEEKLY_LOG_FILE"
    
    # Extract summary information
    local sanitized_vars=$(echo "$sanitization_output" | grep -c "Sanitizing sensitive variable" || echo "0")
    local security_issues=$(echo "$sanitization_output" | grep -c "security issues" || echo "0")
    
    if [[ $sanitization_exit_code -eq 0 ]]; then
        log_maintenance "Environment sanitization completed successfully" "SUMMARY"
        log_maintenance "  - Sanitized variables: $sanitized_vars" "SUMMARY"
        return 0
    else
        log_maintenance "Environment sanitization completed with $security_issues issues" "WARN"
        log_maintenance "  - Sanitized variables: $sanitized_vars" "WARN"
        return $sanitization_exit_code
    fi
}

# 2.2. Security audit
run_security_audit() {
    log_maintenance "Starting comprehensive security audit..."
    
    local audit_output
    local audit_exit_code
    
    # Run security audit
    audit_output=$(ZSH_ENABLE_SECURITY_CHECK=true \
                  ZSH_SECURITY_LOG_ISSUES=true \
                  source "$ZDOTDIR/.zshrc.d/00-core/99-security-check.zsh" 2>&1)
    audit_exit_code=$?
    
    # Log detailed output
    echo "$audit_output" >> "$WEEKLY_LOG_FILE"
    
    # Extract summary information
    local security_issues=$(echo "$audit_output" | grep -o "[0-9]\+ issues found" | head -1 | grep -o "[0-9]\+" || echo "0")
    
    if [[ $audit_exit_code -eq 0 ]]; then
        log_maintenance "Security audit completed successfully" "SUMMARY"
        log_maintenance "  - Security issues found: $security_issues" "SUMMARY"
        return 0
    else
        log_maintenance "Security audit found $security_issues issues" "WARN"
        
        # Alert if too many issues
        if [[ $security_issues -gt $SECURITY_ISSUES_THRESHOLD ]]; then
            log_maintenance "HIGH SECURITY ALERT: $security_issues issues exceed threshold of $SECURITY_ISSUES_THRESHOLD" "ERROR"
        fi
        
        return $audit_exit_code
    fi
}

# 2.3. Configuration validation
run_configuration_validation() {
    log_maintenance "Starting configuration validation..."
    
    local validation_output
    local validation_exit_code
    
    # Run configuration validation
    validation_output=$(ZSH_ENABLE_VALIDATION=true \
                       ZSH_VALIDATION_LOG_ISSUES=true \
                       source "$ZDOTDIR/.zshrc.d/00-core/99-validation.zsh" 2>&1)
    validation_exit_code=$?
    
    # Log detailed output
    echo "$validation_output" >> "$WEEKLY_LOG_FILE"
    
    # Extract summary information
    local validation_issues=$(echo "$validation_output" | grep -o "[0-9]\+ issues found" | head -1 | grep -o "[0-9]\+" || echo "0")
    
    if [[ $validation_exit_code -eq 0 ]]; then
        log_maintenance "Configuration validation completed successfully" "SUMMARY"
        log_maintenance "  - Validation issues found: $validation_issues" "SUMMARY"
        return 0
    else
        log_maintenance "Configuration validation found $validation_issues issues" "WARN"
        return $validation_exit_code
    fi
}

# 2.4. Performance monitoring
run_performance_check() {
    log_maintenance "Starting performance monitoring..."
    
    if [[ -f "$ZDOTDIR/zsh-profile-startup" ]]; then
        local perf_output
        local perf_exit_code
        
        # Run performance check
        perf_output=$("$ZDOTDIR/zsh-profile-startup" -i 5 -w 2 2>&1)
        perf_exit_code=$?
        
        # Log detailed output
        echo "$perf_output" >> "$WEEKLY_LOG_FILE"
        
        # Extract performance metrics
        local avg_time=$(echo "$perf_output" | grep "Average:" | awk '{print $2}' | sed 's/ms//' | sed 's/[^0-9.]//g')
        
        if [[ -n "$avg_time" && "$avg_time" != "0" ]]; then
            log_maintenance "Performance check completed" "SUMMARY"
            log_maintenance "  - Average startup time: ${avg_time}ms" "SUMMARY"
            
            # Check if performance is degraded
            if (( $(awk "BEGIN {print ($avg_time > $STARTUP_TIME_THRESHOLD_MS) ? 1 : 0}") )); then
                log_maintenance "PERFORMANCE ALERT: Startup time ${avg_time}ms exceeds threshold of ${STARTUP_TIME_THRESHOLD_MS}ms" "WARN"
                return 1
            fi
            
            return 0
        else
            log_maintenance "Performance check failed to get valid measurements" "WARN"
            return 1
        fi
    else
        log_maintenance "Performance profiler not found, skipping performance check" "WARN"
        return 1
    fi
}

# 2.5. Log cleanup
cleanup_old_logs() {
    log_maintenance "Cleaning up old log files..."
    
    # Remove log files older than 30 days
    local cleaned_files=0
    
    if [[ -d "$MAINTENANCE_LOG_DIR" ]]; then
        # Find and remove old files
        while IFS= read -r -d '' file; do
            rm -f "$file"
            cleaned_files=$((cleaned_files + 1))
        done < <(find "$MAINTENANCE_LOG_DIR" -name "*.log" -type f -mtime +30 -print0 2>/dev/null)
        
        log_maintenance "Log cleanup completed: $cleaned_files old files removed" "SUMMARY"
    fi
}

# ------------------------------------------------------------------------------
# 3. MAIN MAINTENANCE EXECUTION
# ------------------------------------------------------------------------------

# 3.1. Run all maintenance tasks
run_weekly_maintenance() {
    local maintenance_start_time=$(date +%s)
    local total_issues=0
    local failed_tasks=0
    
    log_maintenance "========================================================"
    log_maintenance "ZSH Weekly Security Maintenance"
    log_maintenance "========================================================"
    log_maintenance "Version: $SCRIPT_VERSION"
    log_maintenance "Started: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
    log_maintenance "User: $USER"
    log_maintenance "ZDOTDIR: $ZDOTDIR"
    log_maintenance "Log File: $WEEKLY_LOG_FILE"
    log_maintenance ""
    
    # Task 1: Environment Sanitization
    log_maintenance "=== Task 1: Environment Sanitization ==="
    if ! run_environment_sanitization; then
        failed_tasks=$((failed_tasks + 1))
        total_issues=$((total_issues + 1))
    fi
    
    # Task 2: Security Audit
    log_maintenance ""
    log_maintenance "=== Task 2: Security Audit ==="
    if ! run_security_audit; then
        failed_tasks=$((failed_tasks + 1))
        local audit_issues=$?
        total_issues=$((total_issues + audit_issues))
    fi
    
    # Task 3: Configuration Validation
    log_maintenance ""
    log_maintenance "=== Task 3: Configuration Validation ==="
    if ! run_configuration_validation; then
        failed_tasks=$((failed_tasks + 1))
        local validation_issues=$?
        total_issues=$((total_issues + validation_issues))
    fi
    
    # Task 4: Performance Monitoring
    log_maintenance ""
    log_maintenance "=== Task 4: Performance Monitoring ==="
    if ! run_performance_check; then
        failed_tasks=$((failed_tasks + 1))
        total_issues=$((total_issues + 1))
    fi
    
    # Task 5: Log Cleanup
    log_maintenance ""
    log_maintenance "=== Task 5: Log Cleanup ==="
    cleanup_old_logs
    
    # Calculate maintenance duration
    local maintenance_end_time=$(date +%s)
    local maintenance_duration=$((maintenance_end_time - maintenance_start_time))
    
    # Generate summary
    log_maintenance ""
    log_maintenance "========================================================"
    log_maintenance "Weekly Maintenance Summary"
    log_maintenance "========================================================"
    log_maintenance "Completed: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
    log_maintenance "Duration: ${maintenance_duration}s"
    log_maintenance "Failed Tasks: $failed_tasks/5"
    log_maintenance "Total Issues Found: $total_issues"
    log_maintenance "Log Files: $WEEKLY_LOG_FILE"
    log_maintenance "           $SUMMARY_LOG_FILE"
    log_maintenance "========================================================"
    
    # Send notifications based on results
    if [[ $failed_tasks -eq 0 && $total_issues -eq 0 ]]; then
        send_notification "Weekly Maintenance Successful" \
            "All security maintenance tasks completed successfully.\nDuration: ${maintenance_duration}s\nNo issues found." \
            "INFO"
    elif [[ $total_issues -gt 0 ]]; then
        send_notification "Security Issues Detected" \
            "Weekly maintenance found $total_issues security issues.\nFailed tasks: $failed_tasks/5\nDuration: ${maintenance_duration}s\nCheck logs: $SUMMARY_LOG_FILE" \
            "WARN"
    else
        send_notification "Maintenance Completed with Warnings" \
            "Weekly maintenance completed but $failed_tasks tasks failed.\nDuration: ${maintenance_duration}s\nCheck logs: $SUMMARY_LOG_FILE" \
            "WARN"
    fi
    
    return $total_issues
}

# ------------------------------------------------------------------------------
# 4. MAIN EXECUTION
# ------------------------------------------------------------------------------

# 4.1. Main function
main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                echo "ZSH Weekly Security Maintenance v$SCRIPT_VERSION"
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --help, -h          Show this help"
                echo "  --dry-run          Show what would be done without executing"
                echo "  --notification METHOD  Set notification method (console,log,email,macos)"
                echo "  --level LEVEL      Set notification level (summary,detailed,issues-only)"
                exit 0
                ;;
            --dry-run)
                echo "DRY RUN: Would execute weekly security maintenance"
                echo "Log file: $WEEKLY_LOG_FILE"
                echo "Summary file: $SUMMARY_LOG_FILE"
                echo "Notification methods: $NOTIFICATION_METHODS"
                exit 0
                ;;
            --notification)
                NOTIFICATION_METHODS="$2"
                shift 2
                ;;
            --level)
                NOTIFICATION_LEVEL="$2"
                shift 2
                ;;
            *)
                echo "Unknown option: $1" >&2
                exit 1
                ;;
        esac
    done
    
    # Run weekly maintenance
    run_weekly_maintenance
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]] || [[ "${(%):-%N}" == *"weekly-security-maintenance"* ]]; then
    main "$@"
fi

# ==============================================================================
# END: ZSH Weekly Security Maintenance Script
# ==============================================================================
