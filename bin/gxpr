#!/bin/sh
#
# Original source: https://github.com/brutasse/dotfiles/blob/master/bin/gxpr
#
# Usage: gxpr <expression>
# Like expr(1), but uses Google's calculator to evaluate <expression>.
# When google fails, light the WolframAlpha signal
#
# Math examples:
#   $ gxpr '1 + 1'
#   2
#
#   $ gxpr 2 ^  16
#   65536
#
#   $ gxpr '(2 ^ 1) + (2 ^ 2) + (2 ^ 3) + (2 ^ 5)'
#   46
#
#   $ gxpr '5*9+(sqrt 10)^3='
#   76.6227766
#
# Conversion examples:
#   $ gxpr 1GB in KB
#   1048576 kilobytes
#
#   $ gxpr 10 megabits in megabytes
#   1.25 megabytes
#
#   $ gxpr 2 miles in inches
#   126720 inches

CURL='curl -s --header User-Agent:gxpr/1.0'
GOOGLE="http://www.google.com/ig/calculator"
WOLFRAM="http://www.wolframalpha.com/input/"
EXPR=$(echo "$@" | perl -MURI::Escape -ne 'chomp;print uri_escape($_)')

res=$(
  $CURL "$GOOGLE?q=$EXPR" |
  perl -ne '/rhs: "?([^\[\],":]+)/ and print $1' |
  perl -pe 's/[^\x00-\x7F]//g'
)

# if we don't have a result, try wolfram alpha
test -z "$res" && {
        zsh_debug_echo "google doesn't know" "$@" 1
        zsh_debug_echo "⌘ click: \033[4m$WOLFRAM?i=$EXPR\033[0m"
    exit 1
}

echo "$res"
