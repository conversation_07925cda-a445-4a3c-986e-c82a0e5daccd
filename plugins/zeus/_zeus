#compdef zeus
#autoload

# in order to make this work, you will need to have the gem zeus installed
# zeus zsh completion

local -a _1st_arguments
if [[ -e .zeus.sock ]]; then
  _1st_arguments=(
  'console:Lets you interact with your Rails application from the command line. (alias = c)'
  'cucumber:Runs cucumber.'
  'dbconsole:Figures out which database you are using and drops you into whichever command line interface.'
  'destroy:Figures out what generate did, and undoes it. (alias = d)'
  'generate:Uses templates to create a whole lot of things. (alias = g)'
  'rake:Execute rake tasks.'
  'runner:Runs Ruby code in the context of Rails non-interactively. (alias = r)'
  'server:Launches a small web server named WEBrick which comes bundled with Ruby. (alias = s)'
  'test:Runs RSpec tests. (alias = rspec, testrb)'
  'version:Shows the version number.'
  )
else
  _1st_arguments=(
  'start:Preloads the zeus environment'
  'init:Generate a zeus.json file'
  )
fi

_rails_generate_arguments() {
    generate_arguments=(
      controller
      generator
      helper
      integration_test
      mailer
      migration
      model
      observer
      performance_test
      plugin
      resource
      scaffold
      scaffold_controller
      session_migration
      stylesheets
    )
}

_rake_does_task_list_need_generating () {
  if [ ! -f .rake_tasks ]; then return 0;
  else
    accurate=$(stat -f%m .rake_tasks)
    changed=$(stat -f%m Rakefile)
    return $(expr $accurate '>=' $changed)
  fi
}

_zrake ()
{
    local expl
    declare -a tasks

    if [ -f Rakefile ]; then
      if _rake_does_task_list_need_generating; then
        echo "\nGenerating .rake_tasks..." > /dev/stderr
        rake --silent --tasks | cut -d " " -f 2 > .rake_tasks
      fi
      tasks=(`cat .rake_tasks`)
      _wanted tasks expl 'rake' compadd $tasks
    fi
}

local expl
local curcontext="$curcontext" state line
typeset -A opt_args

_arguments -C \
    ':command:->command' \
    '*::options:->options'


case $state in
  (command)
    _describe -t commands "zeus subcommand" _1st_arguments
    return
  ;;

  (options)
    case $line[1] in
      (rake)
        _zrake
      ;;
      (generate|g|destroy|d)
        _rails_generate_arguments
        _wanted generate_arguments expl 'all generate' compadd -a generate_arguments
      ;;
    esac
  ;;
esac
