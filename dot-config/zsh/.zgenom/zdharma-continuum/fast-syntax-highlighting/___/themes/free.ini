[base]
default          = none
unknown-token    = red,bold
commandseparator = none
redirection      = none
here-string-tri  = yellow
here-string-text = bg:19
here-string-var  = 110,bg:19
exec-descriptor  = yellow,bold
comment          = black,bold
correct-subtle   = bg:55
incorrect-subtle = bg:52
subtle-bg        = bg:18
secondary        = zdharma
recursive-base   = 183

[command-point]
reserved-word  = 150
subcommand     = 150
alias          = 180
suffix-alias   = 180
global-alias   = bg:19
builtin        = 180
function       = 180
command        = 180
precommand     = 180
hashed-command = 180
single-sq-bracket = 180
double-sq-bracket = 180
double-paren   = 150

[paths]
path          = 166
pathseparator = 
path-to-dir   = 166,underline
globbing      = 112
globbing-ext  = 118

[brackets]
paired-bracket = bg:blue
bracket-level-1 = 130
bracket-level-2 = 70
bracket-level-3 = 69

[arguments]
single-hyphen-option   = 110
double-hyphen-option   = 110
back-quoted-argument   = none
single-quoted-argument = 150
double-quoted-argument = 150
dollar-quoted-argument = 150

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 110
; backslash or $... in "..." (i.e. variable inside a string)
back-or-dollar-double-quoted-argument = 110

[other]
variable             = none
assign               = none
assign-array-bracket = 180
history-expansion    = blue,bold

[math]
mathvar = blue,bold
mathnum = 166
matherr = red

[for-loop]
forvar = none
fornum = 150
; operator
foroper = 150
; separator
forsep = 109

[case]
case-input       = 180
case-parentheses = 116
case-condition   = bg:19
