#!/usr/bin/env zsh
# System PATH Management - PATH is now set in .zshenv
# This file provides helper functions and validates PATH health
# Load time target: <10ms

[[ $ZSH_DEBUG == 1 ]] && {
    printf "# ++++++ %s ++++++++++++++++++++++++++++++++++++\n" "$0"
}

# NOTE: Core PATH is now set in ~/.zshenv for reliability
# This file only provides supplementary functionality

# Set up build environment immediately
export CC="/usr/bin/cc"
export CXX="/usr/bin/c++"
export CPP="/usr/bin/cpp"
export DEVELOPER_DIR="${DEVELOPER_DIR:-/Applications/Xcode.app/Contents/Developer}"

# Ensure DEVELOPER_DIR exists, fallback to CommandLineTools
[[ ! -d "$DEVELOPER_DIR" && -d "/Library/Developer/CommandLineTools" ]] && export DEVELOPER_DIR="/Library/Developer/CommandLineTools"

# PATH is now managed in ~/.zshenv - no function wrappers needed

# Basic commands are now ensured by .zshenv - no additional PATH setup needed

# Prevent duplicate PATH entries
typeset -aUx path

zsh_debug_echo "# [00-core] System PATH configured: $PATH"
