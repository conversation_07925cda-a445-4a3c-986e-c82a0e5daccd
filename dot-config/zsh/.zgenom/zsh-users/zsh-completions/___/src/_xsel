#compdef xsel
# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for xsel v1.2.1 (https://www.vergenet.net/~conrad/software/xsel/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Tomo Kazahaya (https://github.com/tomonacci)
#
# ------------------------------------------------------------------------------

local input='(-a --append -f --follow -i --input)'
local operation='(-c --clear -d --delete -k --keep -x --exchange)'
local selection='(-p --primary -s --secondary -b --clipboard)'

_arguments -s \
  $input{-a,--append}'[append standard input to the selection]' \
  $input{-f,--follow}'[append to selection as standard input grows]' \
  $input{-z,--zeroflush}'[overwrites selection when zero(\\0) is received]' \
  $input{-i,--input}'[read standard input into the selection]' \
  {-o,--output}'[write the selection to standard output]' \
  $operation{-c,--clear}'[clear the selection]' \
  $operation{-d,--delete}'[request that the current selection be deleted]' \
  $selection{-p,--primary}'[operate on the PRIMARY selection (default)]' \
  $selection{-s,--secondary}'[operate on the SECONDARY selection]' \
  $selection{-b,--clipboard}'[operate on the CLIPBOARD selection]' \
  $operation{-k,--keep}'[do not modify the selections, but make the PRIMARY and SECONDARY selections persist even after the programs they were selected in exit]' \
  $operation{-x,--exchange}'[exchange the PRIMARY and SECONDARY selections]' \
  '--display[specify the server to use; see X(1)]:X display:_x_display' \
  '--windowName[specify the X WM_ NAME window property]:window_name' \
  {-t,--selectionTimeout}'[specify the timeout in milliseconds within which the selection must be retrieved]:number' \
  {-l,--logfile}'[specify the file to log errors to when detached (default $HOME/.xsel.log)]:log file:_files' \
  {-n,--nodetach}'[do not detach from the controlling terminal]' \
  '(- *)'{-h,--help}'[display usage information and exit]' \
  {-v,--verbose}'[print informative messages; additional instances of -v raise the debugging level]' \
  '(- *)--version[output version information and exit]'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
