#compdef rsvm
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for rsvm (https://github.com/sdepold/rsvm).
#  Adapted from Docker Machine completion by hhatto (https://github.com/ilkka)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * michaelmior (https://github.com/michaelmior)
#
# ------------------------------------------------------------------------------

# helper function for completing available rusts
__rusts() {
  declare -a rusts_cmd
  rusts_cmd=($(ls "$HOME/.rsvm/versions"))
  _describe 'rusts' rusts_cmd
}

# subcommands
local -a _rsvm_cmds

_rsvm_cmds=(
   'help:Show a help message' \
   'install:Download and install a version' \
   'uninstall:Uninstall a version' \
   'use:Activate a version for now and the future' \
   'ls:List all installed versions of rust' \
   'ls-remote:List remote versions available for install' \
   'ls-channel:Print a channel version available for install' \
)

# subcommand completion functions
__uninstall() {
  __rusts
}

__use() {
  __rusts
}

# common args
_arguments \
  '--help[show help]' \
  '--version[print the version]' \
  '*:: :->command'

# start rusts!
if (( CURRENT == 1 )); then
  _describe -t commands 'rsvm command' _rsvm_cmds
fi

local -a _command_args
case "$words[1]" in
  uninstall)
    __uninstall ;;
  use)
    __use ;;
esac
