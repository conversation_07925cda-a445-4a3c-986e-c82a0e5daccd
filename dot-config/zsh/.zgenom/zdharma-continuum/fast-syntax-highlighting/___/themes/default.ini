; default theme, also embedded in the source of fast-syntax-highlighting

[base]
default          = none
unknown-token    = red,bold
commandseparator = none
redirection      = none
here-string-tri  = yellow
here-string-text = 18
here-string-var  = cyan,bg:18
exec-descriptor  = yellow,bold
comment          = black,bold
correct-subtle   = 12
incorrect-subtle = red
subtle-separator = green
subtle-bg        = bg:18
secondary        = free
; recursive-base   =

[command-point]
reserved-word  = yellow
subcommand     = yellow
alias          = green
suffix-alias   = green
global-alias   = bg:blue
builtin        = green
function       = green
command        = green
precommand     = green
hashed-command = green
single-sq-bracket = green
double-sq-bracket = green
double-paren   = yellow

[paths]
path          = magenta
pathseparator = 
path-to-dir   = magenta,underline
globbing      = blue,bold
globbing-ext  = 13

[brackets]
paired-bracket = bg:blue
bracket-level-1 = green,bold
bracket-level-2 = yellow,bold
bracket-level-3 = cyan,bold

[arguments]
single-hyphen-option   = cyan
double-hyphen-option   = cyan
back-quoted-argument   = none
single-quoted-argument = yellow
double-quoted-argument = yellow
dollar-quoted-argument = yellow

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = cyan
; backslash or $... in "..."
back-or-dollar-double-quoted-argument = cyan

[other]
variable             = 113
assign               = none
assign-array-bracket = green
history-expansion    = blue,bold

[math]
mathvar = blue,bold
mathnum = magenta
matherr = red

[for-loop]
forvar = none
fornum = magenta
; operator
foroper = yellow
; separator
forsep = yellow,bold

[case]
case-input       = green
case-parentheses = yellow
case-condition   = bg:blue
