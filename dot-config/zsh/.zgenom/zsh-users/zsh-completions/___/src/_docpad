#compdef docpad
# ------------------------------------------------------------------------------
# Copyright (c) 2011 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for docpad v6.38.2 (https://github.com/bevry/docpad).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Changwoo Park (https://github.com/pismute)
#  * Shohei Yoshida (https://github.com/syohex)
#
# ------------------------------------------------------------------------------

_docpad_subcommands() {
  local -a commands=(
    "run:run docpad on your project"
    "init:initialize your project"
    "generate:(re)generates your project"
    "render:render the file at <path> and output its results to stdout"
    "watch:watches your project for changes, and (re)generates whenever a change is made"
    "clean:ensure everything is cleaned correctly (will remove your out directory)"
    "update:update your local DocPad and plugin installations to their latest compatible version"
    "upgrade:update your local DocPad and plugin installations to their latest compatible version"
    "install:install plugins"
    "uninstall:uninstall a plugin"
    "info:display the information about your docpad instance"
  )

  _describe -t commands 'command' commands "$@"
}

_docpad() {
  local ret=1

  _arguments \
    '--outpath[a custom directory to place the rendered project]: :_files -/' \
    '--config[a custom configuration file to load in]: :_files' \
    '--env[the environment name to use for this instance, multiple names can be separated with a comma]' \
    '--log[the rfc log level to display]:level' \
    '(-v --verbose)'{-v,--verbose}'[set log level to 7]' \
    '(-d --debug)'{-d,--debug}'[output a log file]' \
    '--global[whether or not we should just fire global installation of docpad]' \
    '(--color --colour)'{--color,--colour}'[use color terminal output(default: true)]' \
    '--silent[do not write anything that is not essential]' \
    '--progress[output the progress as it occurs(default: true)]' \
    '--version[show version]' \
    '(- *)'{-h,--help}'[output usage information]'\
    '1: :_docpad_subcommands'\
    '*:: :_files' && ret=0

  return ret
}

_docpad "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
