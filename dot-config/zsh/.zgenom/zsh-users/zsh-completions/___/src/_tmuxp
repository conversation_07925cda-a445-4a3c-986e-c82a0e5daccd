#compdef tmuxp

# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for tmuxp (https://github.com/tmux-python/tmuxp)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

_tmuxp() {

  local curcontext="$curcontext" state line
  typeset -A opt_args

  _arguments -C \
    ":command:->command" \
    "*::options:->options" \
    "--log-level[log level(default info)]: :(debug info warning error critical)" \
    '(- *)'{-h,--help}'[display usage information]' \
    '(- *)'{-V,--version}'[show version number and exit]'

    case $state in
      (command)
        local -a subcommands
        subcommands=(
          'load:Load tmuxp workspaces'
          'shell:launch python shell for tmux server, session, window and pane'
          'import:Import a teamocil/tmuxinator config'
          'convert:Convert a tmuxp config between JSON and YAML'
          'debug-info:print out all diagnostic info'
          'ls:list sessions in config directory'
          'edit: run $EDITOR on config'
          'freeze:Snapshot a session into a config'
        )
        _describe -t commands 'commands' subcommands
        ;;
      (options)
        case $line[1] in
          (load)
            __tmuxp_load
            ;;
          (import)
            __tmuxp_import
            ;;
          (freeze)
            local sessions="$(__tmux_sessions)"
            _arguments -C \
             '(- *)'{-h,--help}'[show  help message and exit]' \
             '-S[pass-through for tmux -S]: :_files' \
             '-L[pass-through for tmux -L]: :' \
             '(-f --config-format)'{-f,--config-format}'[format to save in]: :(yaml json)' \
             '(-o --save-to)'{-o,--save-to}'[file to save to]: :_files' \
             '(-y --yes)'{-y,--yes}'[always answer yes]' \
             '(-q --quiet)'{-q,--quiet}"[don't prompt for confirmation]" \
             '--force[overwrite the config file]'\
              "1::session name:compadd $sessions"
            ;;
          (convert)
            _arguments -C \
              '1:: :_files -g "*.(json|yaml|yml)"'
            ;;
          (shell)
            local sessions="$(__tmux_sessions)"
            local windows="$(__tmux_windows)"
            _arguments -C \
             '(- *)'{-h,--help}'[show  help message and exit]' \
             '-S[pass-through for tmux -S]: :_files' \
             '-L[pass-through for tmux -L]: :' \
             '--best[use best shell available in site packages]' \
             '--pdb[use plain pdb]' \
             "--code[use stdlib's code.interact()]" \
             '--ptipython[use ptpython + ipython]' \
             '--ptpython[use ptpython]' \
             '--ipython[use ipython]' \
             '--bpython[use bpython]' \
             (--no-startup)'--use-pythonrc[load PYTHONSTARTUP env var and ~/.pythonrc.py script in --code]' \
             (--use-pythonrc)'--no-startup[do not load PYTHONSTARTUP env var and ~/.pythonrc.py script in --code]' \
             (--no-vi-mode)'--use-vi-mode[use vi-mode in ptpython/ptipython]' \
             (--vi-mode)'--no-vi-mode[do not use vi-mode in ptpython/ptipython]' \
              "1::session name:compadd $sessions" \
              "2::window name:compadd $windows"
            ;;
          (ls|debug-info)
            _arguments -C \
              '(- *)'{-h,--help}'[show help message and exit]'
            ;;
          (edit)
            _arguments -C \
              '(- *)'{-h,--help}'[show help message and exit]' \
              '1:: :_files -g "*.(json|yaml|yml)"'
            ;;
        esac
    esac

}

__tmuxp_load() {
  local state line
  typeset -A opt_args
  _arguments -C \
    '*:sessions:->sessions' \
    '--yes:yes' \
    '-d[Load the session without attaching it]' \
    '-2[Force tmux to assume the terminal supports 256 colors]' \
    '-8[Like -2, but indicates that the terminal supports 88 colors]'

  # Can't get the options to be recognized when there are sessions that has
  # a dash.

  local tmuxp_dir="${HOME}/.tmuxp"
  local tmuxp_xdg_dir="${XDG_CONFIG_HOME:-${HOME}/.config}/tmuxp"
  if [[ -d "${TMUXP_CONFIGDIR-}" ]]; then
    tmuxp_dir="${TMUXP_CONFIGDIR}"
  elif [[ -d "${tmuxp_xdg_dir}" ]]; then
    tmuxp_dir="${tmuxp_xdg_dir}"
  fi
  case $state in
    (sessions)
      local s
      _alternative \
        'sessions-user:user session:compadd -F line - ${tmuxp_dir}/*.(json|yml|yaml)(:r:t)' \
        'sessions-other:session in current directory:_path_files -/ -g "**/.tmuxp.(yml|yaml|json)"' \
        'sessions-other:session in current directory:_path_files -g "*.(yml|yaml|json)"'
      ;;
  esac
}

__tmuxp_import() {
  local state line
  typeset -A opt_args
  _arguments -C \
    '1::program:(tmuxinator teamocil)' \
    '2::project:->project'

  case $state in
    (project)
      if [[ $line[1] == 'tmuxinator' ]]
      then
        _wanted tmuxinator-projects exp 'tmuxinator projects' compadd $(tmuxinator completions start)
      fi
      ;;
  esac
}

__tmux_sessions () {
  local tmux_sessions
  tmux_sessions=($(_call_program tmux_sessions 'tmux ls -F "#{session_name}"'))
  echo $tmux_sessions
}

__tmux_windows () {
  local tmux_windows
  tmux_windows=($(_call_program tmux_sessions 'tmux ls -F "#{window_name}"'))
  echo $tmux_windows
}

_tmuxp "$@"


# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
