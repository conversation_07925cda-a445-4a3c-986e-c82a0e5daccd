#compdef supervisorctl
# ------------------------------------------------------------------------------
# Copyright (c) 2015 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for supervisorctl 4.2.5 from Supervisord (https://github.com/Supervisor/supervisor)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Matt Black (https://github.com/mafrosis)
#  * dongweiming (https://github.com/dongweiming)
#  * Shohei Yoshida (https://github.com/syohex)
#
# ------------------------------------------------------------------------------

_supervisorctl() {
  local -a procs
  typeset -A opt_args
  local context state line
  local curcontext="$curcontext"
  local ret=1

  _arguments -C \
    {--configuration,-c}='[configuration file path (default /etc/supervisor.conf)]:filename:_files' \
    '(- *)'{--help,-h}'[print usage message and exit]:' \
    {--interactive,-i}'[start an interactive shell after executing commands]' \
    {--serverurl,-s}='[URL on which supervisord server is listening (default "http://localhost:9001")]:url:_urls' \
    {--username,-u}='[username to use for authentication with server]:username:_users' \
    {--password,-p}='[password to use for authentication with server]:password' \
    {--history-file,-r}'[keep a readline history (if readline is available)]:filename:_files' \
    '1: :_supervisorctl_subcommands' \
    '*:: :->subcmds' && ret=0

  case $state in
    (subcmds)
      case "$words[1]" in
        (help)
          _arguments \
           '1: :_supervisorctl_subcommands' \
            && ret=0
          ;;
        (add|remove)
          _arguments \
            '1: :_supervisorctl_procs_groups' \
            && ret=0
          ;;
        (fg)
          _arguments \
            '1: :_supervisorctl_processes' \
            && ret=0
          ;;
        (pid|clear)
          _arguments \
            '*: :_supervisorctl_processes_all' \
            && ret=0
          ;;
        (restart|status)
          _arguments \
            '*:process_or_group:_supervisorctl_procs_and_group_prefixes' \
            && ret=0
          ;;
        (update)
          _arguments \
            '*: :_supervisorctl_groups' \
            && ret=0
          ;;
        (stop)
          _arguments \
            '*:running process or group:_supervisorctl_running_procs' \
            && ret=0
          ;;
        (start)
          _arguments \
            '*:stopped process or group:_supervisorctl_stopped_procs' \
            && ret=0
          ;;
        (signal)
          _arguments \
            '1:signal:_signals -s' \
            '*:running process or group:_supervisorctl_running_procs' \
            && ret=0
          ;;
        (tail)
          _arguments \
            '-f[Continuous tail of named process stdout]' \
            '-[last N *bytes* of process stdout]:number' \
            '1: :_supervisorctl_processes' \
            '2:output:(stdout stderr)' \
            && ret=0
          ;;
        (maintail)
          _arguments \
            '-f[Continuous tail of named process stdout]' \
            '-[last N *bytes* of process stdout]:number' \
            && ret=0
          ;;
      esac
  esac

  return 0
}

(( $+functions[_supervisorctl_subcommands] )) ||
_supervisorctl_subcommands() {
  local -a commands=(
    'add:Activates any updates in config for process/group'
    'avail:Display all configured processes'
    'clear:Clear single/multiple/all process log files'
    'exit:Exit the supervisor shell'
    'fg:Connect to a process in foreground mode'
    'maintail:tail of supervisor main log file'
    'open:Connect to a remote supervisord process. (for UNIX domain socket, use unix:///socket/path)'
    'pid:Get the PID of process/supervisord'
    'quit:Exit the supervisor shell'
    'reload:Restart the remote supervisord'
    'remove:Removes process/group from active config'
    "reread:Reload the daemon's configuration files"
    'restart:Restart process, group or all'
    'signal:Send signal to a process'
    'shutdown:Shut the remote supervisord down'
    'start:Start process, group or all'
    'status:Get process/group status info'
    'stop:Stop process, group or all'
    'tail:tail of process stdout'
    'update:Reload config and add/remove as necessary'
    'version:Show the version of the remote supervisord process'
    'help:Show help'
  )

  _describe -t commands 'command' commands "$@"
}

(( $+functions[_supervisorctl_processes] )) ||
_supervisorctl_processes() {
  local -a procs
  procs=(${(f)"$(_call_program processes supervisorctl avail | awk '{gsub(":","\\:", $1); print $1 }')"})
  if [[ "$1" = 'all' ]]; then
    procs+=(all)
  fi
  _describe 'processes' procs
}

(( $+functions[_supervisorctl_processes_all] )) ||
_supervisorctl_processes_all() {
  _supervisorctl_processes all
}

(( $+functions[_supervisorctl_procs_groups] )) ||
_supervisorctl_procs_groups() {
  local -a procs
  procs=(${(f)"$(_call_program processes supervisorctl status \
  | awk '{n=$1;gsub(":","\\:",n); printf "%s\n%s\n",n,substr($1,1,index($1,":")-1)}' \
  | uniq)"})
  _describe 'process and groups' procs
}

(( $+functions[_supervisorctl_procs_and_group_prefixes] )) ||
_supervisorctl_procs_and_group_prefixes() {
  _supervisorctl_collect_procs '.'
}

(( $+functions[_supervisorctl_running_procs] )) ||
_supervisorctl_running_procs() {
  _supervisorctl_collect_procs 'RUNNING'
}

(( $+functions[_supervisorctl_stopped_procs] )) ||
_supervisorctl_stopped_procs() {
  _supervisorctl_collect_procs 'STOPPED'
}

(( $+functions[_supervisorctl_collect_procs] )) ||
_supervisorctl_collect_procs() {
  if (( $words[(I)all] )); then
    return
  fi

  local pattern=$1

  local -a procs
  procs=(${(f)"$(_call_program processes supervisorctl status \
  | awk "/$pattern/"'{n=$1;gsub(":","\\:",n); printf "%s\n%s\\:\n",n,substr($1,1,index($1,":")-1)}' \
  | uniq)"})
  procs+=(all)
  _describe 'stooped processes or groups' procs
}

(( $+functions[_supervisorctl_groups] )) ||
_supervisorctl_groups() {
  if (( $words[(I)all] )); then
    return
  fi

  local -a groups
  groups=(${(f)"$(_call_program processes supervisorctl status \
  | awk '{printf "%s\n",substr($1,1,index($1,":")-1)}' \
  | uniq)"})
  groups+=(all)
  _describe 'groups' groups
}

_supervisorctl "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
