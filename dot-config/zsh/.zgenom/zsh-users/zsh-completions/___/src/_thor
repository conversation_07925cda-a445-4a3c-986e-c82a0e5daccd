#compdef thor
# ------------------------------------------------------------------------------
# Copyright (c) 2009-2015 Rob<PERSON> and contributors (see
# https://github.com/ohmyzsh/ohmyzsh/graphs/contributors
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for thor 1.2.1 (https://github.com/rails/thor).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Andrew Hodges (https://github.com/betawaffle)
#  * Shohei Yoshida (https://github.com/syohex)
#
# ------------------------------------------------------------------------------

_thor_subcommands() {
  local -a commands=(
    'help:Describe available commands or one specific command'
  )

  if [[ -e Thorfile ]]; then
    commands=(${(@f)"$(thor list 2>/dev/null | awk '/^thor/{sub(/^thor /,"\\"); sub(/[ ]*#[ ]*/,":");print}')"} $commands)
  else
    commands=(
      $commands
      'install:Install an optionally named Thor file into your system commands'
      'installed:List the installed Thor modules and commands'
      'list:List the available thor commands'
      'uninstall:Uninstall a named Thor module'
      'update:Update a Thor file from its original location'
      'version:Show Thor version'
    )
  fi

  _describe -t commands 'command' commands "$@"
}

_thor_help() {
  local -a commands

  if [[ -e Thorfile ]]; then
    commands=(${(@f)"$(thor list 2>/dev/null  | awk "/^thor/{ printf \"\\\\%s\\n\", \$2 }")"})
  else
    commands=(help install installed list uninstall update version)
  fi

  _values 'help' $commands
}

_thor() {
  typeset -A opt_args
  local context state line
  local curcontext="$curcontext"
  local ret=1

  _arguments -C -A "-*" \
    '1: :_thor_subcommands' \
    '*::arg:->args' \
    && ret=0

  case "$state" in
    (args)
      case $words[1] in
        (help)
           _arguments \
            '1: :_thor_help' \
            && ret=0
          ;;
        (install)
          _arguments \
            '--as=[specify the name]:name' \
            '--relative[use relative path]' \
            '--force[force install]' \
            '*: :_files' \
            && ret=0
          ;;
        (installed)
          _arguments \
            '--internal[show internal]' \
            && ret=0
          ;;
        (list)
          _arguments \
            '--substring[use search keyword as substring]' \
            '--group=[specify the group name]' \
            '--all[search from all groups]' \
            '--debug[show all back trace if error raises]' \
            '*::' \
            && ret=0
          ;;
        (*)
          _arguments \
            '*::_files' \
            && ret=0
          ;;
      esac
      ;;
  esac

  return ret
}

_thor "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
