#compdef rslsync
# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for resilio sync 2.7.3 (https://www.resilio.com/individuals/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Fabian Klötzl (https://github.com/kloetzl)
#
# ------------------------------------------------------------------------------

_rslsync(){
  integer ret=1
  local -a args
  args+=(
    '(- *)--help[Print help]'
    '--config[Use a configuration file]:file:_files'
    '--storage[Storage path for identity and license]:path:_files -/'
    '--identity[Creates user identity]:name:'
    '--license[Apply owner license]:file:_files'
    '--decrypt[Decrypt encrypted folder]:'
    '--upgradedb[Upgrade databases in specified storage or upgrade a single db]:db:_files'
    '--nodaemon[Do not daemonize]'
    '--dump-sample-config[Print a sample configuration file]'
    '--log[Set log file]:file:_files'
    '(--help)--webui.listen[Set the webui listening interface]:ip\:port:'
    '--generate-secret[Generate a read/write key]::version:(2)'
    '--get-ro-secret[Get the read-only key associated to a read/write key]:key:'
    '--server[Set Management Console address]:ip\:port:'
  )
  _arguments $args[@] && ret=0
  return ret
}

_rslsync

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
