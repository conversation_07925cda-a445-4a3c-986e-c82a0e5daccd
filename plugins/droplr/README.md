# droplr

Use [Droplr](https://droplr.com/) from the command line to upload files and shorten
links. It needs to have [Droplr.app](https://droplr.com/apps) installed and logged
in. MacOS only.

To use it, add `droplr` to the `$plugins` variable in your zshrc file:

```zsh
plugins=(... droplr)
```

Author: [<PERSON><PERSON><PERSON>](https://github.com/fabiofl)

## Examples

- Upload a file: `droplr ./path/to/file/`

- Shorten a link: `droplr https://example.com`
