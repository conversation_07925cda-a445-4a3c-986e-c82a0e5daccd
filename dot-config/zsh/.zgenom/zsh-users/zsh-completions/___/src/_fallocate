#compdef fallocate
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
# Completion for fallocate, util-linux 2.40.2 (https://github.com/util-linux/util-linux/)
# - preallocate or deallocate space to a file
# https://man7.org/linux/man-pages/man1/fallocate.1.html
# ------------------------------------------------------------------------------
# Authors
# -------
#  * Aditi Sharma (https://github.com/Aditi76117)
# ------------------------------------------------------------------------------

_arguments \
  '(-c --collapse-range)'{-c,--collapse-range}'[Removes a byte range from a file without leaving a hole]' \
  '(-d --dig-holes)'{-d,--dig-holes}'[Detect and dig holes]' \
  '(-i --insert-range)'{-i,--insert-range}'[Insert a hole of length bytes from offset]' \
  '(-l --length)'{-l+,--length}'[Specifies the length of the range, in bytes]:bytes' \
  '(-n --keep-size)'{-n,--keep-size}'[Do not modify the apparent length of the file]' \
  '(-o --offset)'{-o+,--offset}'[Specifies the beginning offset of the range, in bytes]:offset' \
  '(-p --punch-hole)'{-p,--punch-hole}'[Deallocates space in the byte range starting at offset and continuing for length bytes]' \
  '(-v --verbose)'{-v,--verbose}'[Enable verbose mode]' \
  '(-x --posix)'{-p,--posix}'[Enable POSIX operation mode]' \
  '(-z --zero-range)'{-z,--zero-range}'[Zeroes space in the byte range starting at offset and continuing for length bytes]' \
  '(- *)'{-h,--help}'[Display help text and exit]' \
  '(- *)'{-V,--version}'[Print version and exit]' \
  '*:filename:_files'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
