#!/usr/bin/env bash

#
# blackbox_initialize -- Enable blackbox for a GIT or HG repo.
#
#
# Example:
#    blackbox_initialize
#

set -e
source "${0%/*}/_blackbox_common.sh"

if [[ $1 != 'yes' ]]; then
  read -r -p "Enable blackbox for this $VCS_TYPE repo? (yes/no) " ans
  if [[ $ans = 'no' || $ans = 'n' || $ans = '' ]]; then
    echo 'Exiting...'
    exit 1
  fi
fi

if [[ $VCS_TYPE = "unknown" ]]; then
	echo 'Not in a known VCS directory'
	exit 1
fi

change_to_vcs_root

echo VCS_TYPE: $VCS_TYPE
vcs_ignore "${BLACKBOXDATA}/pubring.gpg~" "${BLACKBOXDATA}/pubring.kbx~" "${BLACKBOXDATA}/secring.gpg"

# Make directories
mkdir -p "${KEYRINGDIR}"
vcs_add "${KEYRINGDIR}"
touch "$BLACKBOXDATA/$BB_ADMINS_FILE" "$BLACKBOXDATA/$BB_FILES_FILE"
vcs_add "$BLACKBOXDATA/$BB_ADMINS_FILE" "$BLACKBOXDATA/$BB_FILES_FILE"

if [[ $VCS_TYPE = "git" ]]; then

  # Set .gitattributes so that Windows users don't break the admin files.
  FILE="$BLACKBOXDATA/.gitattributes"
  touch "$FILE"
  LINE='blackbox-admins.txt text eol=lf'
  grep -qF "$LINE" "$FILE" || echo "$LINE" >> "$FILE"
  LINE='blackbox-files.txt text eol=lf'
  grep -qF "$LINE" "$FILE" || echo "$LINE" >> "$FILE"
  vcs_add "$FILE"
fi

if [[ $VCS_TYPE = "svn" ]]; then
	echo
	echo
	echo '`subversion` automatically tracks the ignored files; you just need to commit.'
else
	IGNOREFILE="$(vcs_ignore_file_path)"
	test -f "$IGNOREFILE" && vcs_add "$IGNOREFILE"

	# Make a suggestion:
	echo
	echo
	echo 'NEXT STEP: You need to manually check these in:'
	echo '     ' $VCS_TYPE commit -m\'INITIALIZE BLACKBOX\' "$BLACKBOXDATA" "$IGNOREFILE"
fi
