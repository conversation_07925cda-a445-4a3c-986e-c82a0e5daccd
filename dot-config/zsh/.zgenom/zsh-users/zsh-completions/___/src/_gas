#compdef gas
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for gas (https://github.com/walle/gas).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

local curcontext="$curcontext" state line cmds ret=1

_arguments -C \
  '(- 1 *)'{-v,--version}'[display version information]' \
  '(-h|--help)'{-h,--help}'[show help information]' \
  '1: :->cmds' \
  '*: :->args' && ret=0

case "$state" in
  (cmds)
    cmds=(
      "version:Prints Gas's version"
      "use:Uses author"
      "show:Shows your current user"
      "list:Lists your authors"
      "import:Imports current user to gasconfig"
      "help:Describe available tasks or one specific task"
      "delete:Deletes author"
      "add:Adds author to gasconfig"
    )
    _describe -t commands 'gas command' cmds && ret=0
  ;;
  (args)
    case "$line[1]" in
      (use|delete)
        _values -S , 'authors' $(cat ~/.gas | sed -n -e 's/^\[\(.*\)\]/\1/p') && ret=0
      ;;
    esac
  ;;
esac

return ret

