#compdef node
# ------------------------------------------------------------------------------
# Copyright (c) 2018 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for Node.js v24.4.0 (https://nodejs.org)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Mario Fernandez (https://github.com/sirech)
#  * Nicholas Penree (https://github.com/drudge)
#  * Masafumi Koba (https://github.com/ybiquitous)
#  * Shohei YOSHIDA (https://github.com/syohex)
#
# ------------------------------------------------------------------------------

_node_files() {
  for (( i = 2; i < CURRENT; i++)); do
    if [[ ${words[i]} == "--prof-process" ]]; then
      _files -g "*.log"
      return
    fi
  done

  _files -g "*.(js|mjs|ts|mts)"
}

_node_args() {
  if (( CURRENT == 2 )); then
    _alternative "_node_files" "_values 'command' 'inspect[enable inspector for debugging]'"
    return
  fi

  _node_files
}

_node_scripts() {
  local -a scripts=()
  if (( $+commands[jq] )) && [[ -e package.json ]]; then
    scripts=(${(@f)"$(jq -r '.scripts | keys[]' package.json | sed 's/:/\\:/g')"})
  fi
  _describe 'scripts' scripts
}

local curcontext="$curcontext" state line ret=1
typeset -A opt_args

_arguments -C \
  '-[script read from stdin (default; interactive mode if a tty)]' \
  '--[indicate the end of node options]' \
  '--abort-on-uncaught-exception[aborting instead of exiting causes a core file to be generated for analysis]' \
  '--allow-addons[allow use of addons when any permissions are set]' \
  '--allow-child-process[allow use of child process when any permissions are set]' \
  '--allow-fs-read=[allow permissions to read the filesystem]: :_files' \
  '--allow-fs-write=[allow permissions to write in the filesystem]:_files' \
  '--allow-wasi[allow wasi when any permissions are set]' \
  '--allow-worker[allow worker threads when any permissions are set]' \
  '--build-snapshot[generate a snapshot blob when the process exits]' \
  '--build-snapshot-config=[generate a snapshot blob when the process exits using a JSON configuration in the specified path]:path:_files' \
  {-c,--check}'[syntax check script without executing]' \
  '--completion-bash[print source-able bash completion script]' \
  '*'{-C,--conditions=}'[additional user conditions for conditional exports and imports]:condition' \
  '--cpu-prof[Start the V8 CPU profiler on start up]' \
  '--cpu-prof-dir=[directory where the V8 profiles generated by --cpu-prof]:dir:_files -/' \
  '--cpu-prof-interval=[sampling interval in microseconds for the V8 CPU profiler]:number' \
  '--cpu-prof-name=[file name of the V8 profile generated with --cpu-prof]: :_files' \
  '--diagnostic-dir=[set dir for all output files(default: current working directory)]:dir:_files -/' \
  '--disable-proto=[disable Object.prototype.__proto__]:mode:(delete throw)' \
  '--disable-sigusr1[disable inspector thread to be listening for SIGUSR1 signal]' \
  '*--disable-warning=[silence specific process warnings]:warn type' \
  '--disable-wasm-trap-handler[disable trap-handler-based WebAssembly bound checks]' \
  '--disallow-code-generation-from-strings[disallow eval and friends]' \
  '--dns-result-order=[set default value of verbatim in dns.lookup]: :(ipv4first verbatim)' \
  '--enable-etw-stack-walking[provides heap data to ETW Windows native tracing]' \
  '--enable-fips[enable FIPS crypto at startup]' \
  '--enable-source-maps[source map support for stack traces]' \
  '--entry-url[treat the entrypoint as a URL]' \
  '*--env-file=[set environment variables from supplied file]:envfile:_files' \
  '*--env-file-if-exists=[set environment variables from supplied file if exists]:envfile:_files' \
  '(- 1 *)'{-e,--eval}'[evaluate script]:inline JavaScript' \
  '--experimental-addon-modules[enable experimental import support for addons]' \
  '--experimental-config-file=[set config file from supplied file]:file:_files' \
  '--experimental-default-config-file[set config file from default config file]' \
  '--experimental-eventsource[enable experimental EventSource API]' \
  '--experimental-import-meta-resolve[experimental ES Module import.meta.resolve() support]' \
  '(--loader --experimental-loader)'{--loader,--experimental-loader}'=[Specify the module of a custom ECMAScript Module loader]: :_files' \
  '--experimental-network-inspection[enable experimental network inspection support]' \
  '--experimental-print-required-tla[print pending top-level await]' \
  '--experimental-sea-config=[generate a blob that can be embedded into the single executable application]: :_files' \
  '--experimental-test-coverage[enable code coverage in the test runner]' \
  '--experimental-test-module-mocks[enable module mocking in the test runner]' \
  '--experimental-transform-types[enable transformation of TypeScript-onlysyntax into JavaScript code]' \
  '--experimental-vm-modules[experimental ES Module support in vm module]' \
  '--experimental-wasm-modules[experimental ES module support for webassembly modules]' \
  '--experimental-webstorage[experimental Web Storage API]' \
  '--expose-gc[expose gc extension]' \
  '--force-context-aware[disable loading non-context-aware addons]' \
  '--force-fips[force FIPS crypto]' \
  '--force-node-api-uncaught-exceptions-policy[enforces "uncaughtException" event on Node API asynchronous callbacks]' \
  '--frozen-intrinsics[experimental frozen intrinsics support]' \
  '--heap-prof[Start the V8 heap profiler on start up]' \
  '--heap-prof-dir=[Directory where the V8 profiles generated by --heap-prof]: :_files -/' \
  '--heap-prof-interval=[sampling interval in bytes for the V8 heap profile]: :number' \
  '--heap-prof-name=[file name of the V8 heap profile generated]: :_files' \
  '--heapsnapshot-near-heap-limit=[Generate heapsnapshots whenever V8 is approaching the heap limit]:limit' \
  '--heapsnapshot-signal=[Generate heap snapshot on specified signal]:signals:_signals -s' \
  '--icu-data-dir=[set ICU data load path to dir (overrides NODE_ICU_DATA) note: linked-in ICU data is present]: :_directories' \
  '--import=[ES module to preload]:module:_node_files' \
  '--input-type=[set module type for string input]:module type :(commonjs module)' \
  '--insecure-http-parser[Use an insecure HTTP parser that accepts invalid HTTP headers]' \
  '--inspect=-[activate inspector on host:port (default: 127.0.0.1:9229)]:host_port' \
  '--inspect-brk=-[activate inspector on host:port and break at start of user script]:host_port' \
  '(--debug-port --inspect-port)'{--debug-port,--inspect-port}'=[set host:port for inspector]:host_port' \
  '--inspect-publish-uid=[comma separated list of destinations for inspector uid]' \
  '--inspect-wait=-[activate inspector on host:port and wait for debugger to be attached]::host_port' \
  {-i,--interactive}'[always enter the REPL even if stdin does not appear to be a terminal]' \
  '--interpreted-frames-native-stack[help system profilers to translate JavaScript interpreted frames]' \
  '--jitless[Disable runtime allocation of executable memory]' \
  '--localstorage-file=[file used to persist localStorage data]:file:_files' \
  '--max-http-header-size=[set the maximum size of HTTP headers]: :number' \
  '--network-family-autoselection-attempt-timeout=[sets the default value for the network family autoselection attempt timeout]:timeout' \
  '--no-addons[disable loading native addons]' \
  '--no-deprecation[silence deprecation warnings]' \
  '--no-experimental-detect-module[when ambiguous modules fail to evaluate, try again to evaluate them as ES modules]' \
  '--no-experimental-global-navigator[expose experimental Navigator API on the global scope]' \
  '--no-experimental-repl-await[disable experimental await keyword support in REPL]' \
  '--no-experimental-require-module[allow loading synchronous ES Modules in require()]' \
  '--no-experimental-sqlite[disable experimental node sqlite module]' \
  '--no-experimental-strip-types[disable experimental type-stripping for TypeScript files]' \
  '--no-experimental-websocket[experimental WebSocket API (currently set)]' \
  '--no-extra-info-on-fatal-exception[hide extra information on fatal exception that causes exit]' \
  '--no-force-async-hooks-checks[disable checks for async_hooks]' \
  '--no-global-search-paths[disable global module search paths]' \
  '--no-warnings[silence all process warnings]' \
  '--node-memory-debug[run with extra debug checks for memory leaks in Node.js itself]' \
  '--openssl-config=[load OpenSSL configuration from the specified file (overrides OPENSSL_CONF)]:file:_files' \
  '--openssl-legacy-provider[enable OpenSSL 3.0 legacy provider]' \
  '--openssl-shared-config[enable OpenSSL shared configuration]' \
  '--pending-deprecation[emit pending deprecation warnings]' \
  '--preserve-symlinks[preserve symbolic links when resolving]' \
  '--preserve-symlinks-main[preserve symbolic links when resolving the main module]' \
  '(- 1 *)'{-p,--print}'[evaluate script and print result]:inline JavaScript' \
  '--prof[generate V8 profiler output]' \
  '--prof-process[process V8 profiler output generated using --prof]' \
  '--redirect-warnings=[write warnings to file instead of stderr]: :_files' \
  '--report-compact[output compact single-line JSON]' \
  '--report-directory=[custom report path]: :_files -/' \
  '--report-exclude-network[exclude network interface diagnostics(default: false)]' \
  '--report-filename=[custom report file name]: :_files' \
  '--report-on-fatalerror[generate diagnostic report on fatal (internal) errors]' \
  '--report-on-signal=[generate diagnostic report upon receiving signals]' \
  '--report-signal=[causes diagnostic report to be produced on provided signal]:signals:_signals -s' \
  '--report-uncaught-exception[generate diagnostic report on uncaught exceptions]' \
  '*'{-r,--require}'[module to preload (option can be repeated)]: :_node_files' \
  '--run=[run a script specified in package.json]:script:_node_scripts' \
  '--secure-heap=[total size of the OpenSSL secure heap]: :number' \
  '--secure-heap-min=[minimum allocation size from the OpenSSL secure heap]' \
  '--snapshot-blob=[path to the snapshot blob that is used to restore the application state]:snapshot:_files' \
  '--test[launch test runner on startup]' \
  '--test-concurrency=[specify test runner concurrency]:concurrency' \
  '--test-coverage-branches=[the branch coverage minimum threshold]:threshold' \
  '--test-coverage-exclude=[exclude files from coverage report that match this glob pattern]:pattern' \
  '--test-coverage-functions=[the function coverage minimum threshold]:threshold' \
  '--test-coverage-include=[include files from coverage report that match this glob pattern]:pattern' \
  '--test-coverage-lines=[the line coverage minimum threshold]:threshold' \
  '--test-force-exit[force test runner to exit upon completion]' \
  '--test-isolation=[configurations the type of test isolation used in the test runner]:isolation' \
  '--test-name-pattern=[run tests whose name matches this regular expression]:pattern' \
  '--test-only[run tests with "only" option set]' \
  '*--test-reporter=[report test output using the given reporter]:reporter:(tap spec dot)' \
  '*--test-reporter-destination=[report given reporter to the given destination]:destination:_files' \
  '--test-shard=[run test at specific shard]:shard' \
  '--test-skip-pattern=[run tests whose name do not match this regular expression]' \
  '--test-timeout=[specify test runner timeout]' \
  '--test-update-snapshots[regenerate test snapshots]' \
  '--throw-deprecation[throw an exception on deprecations]' \
  '--title=[the process title to use on startup]:process title' \
  '--tls-cipher-list=[use an alternative default TLS cipher list]:cipher list string' \
  '--tls-keylog=[log TLS decryption keys to named file for traffic analysis]: :_files' \
  '(--tls-max-v1.3)--tls-max-v1.2[set default TLS maximum to TLSv1.2]' \
  '(--tls-max-v1.2)--tls-max-v1.3[set default TLS maximum to TLSv1.3]' \
  '(--tls-min-v1.1 --tls-min-v1.2 --tls-min-v1.3)--tls-min-v1.0[set default TLS minimum to TLSv1.0]' \
  '(--tls-min-v1.0 --tls-min-v1.2 --tls-min-v1.3)--tls-min-v1.1[set default TLS minimum to TLSv1.1]' \
  '(--tls-min-v1.0 --tls-min-v1.1 --tls-min-v1.3)--tls-min-v1.2[set default TLS minimum to TLSv1.2]' \
  '(--tls-max-v1.2 --tls-min-v1.0 --tls-min-v1.1 --tls-min-v1.2)--tls-min-v1.3[set default TLS minimum to TLSv1.3]' \
  '--trace-deprecation[show stack traces on deprecations]' \
  '--trace-env[print accesses to the environment variables]' \
  '--trace-env-js-stack[print accesses to the environment variables and the JavaScript stack trace]' \
  '--trace-env-native-stack[print accesses to the environment variables and the native stack trace ]' \
  '--trace-event-categories[comma separated list of trace event categories to record]: :{_values -s , categories node node.async_hooks node.bootstrap node.perf node.perf.usertiming node.perf.timerify node.fs.sync node.vm.script v8}' \
  '--trace-event-file-pattern[Template string specifying the filepath for the trace-events data, it supports ${rotation} and ${pid} log-rotation id. %2$u is the pid.]:template string' \
  '--trace-exit[show stack trace when an environment exits]' \
  '--trace-promises[show stack traces on promise initialization and resolution]' \
  '--trace-sigint[prints a stack trace on SIGINT]' \
  '--trace-sync-io[show stack trace when use of sync IO is detected after the first tick]' \
  '--trace-tls[prints TLS packet trace information to stderr]' \
  '--trace-uncaught[show stack traces for the throw behind uncaught exceptions]' \
  '--trace-warnings[show stack traces on process warnings]' \
  '--track-heap-objects[track heap object allocations for heap snapshots]' \
  '--unhandled-rejections=[define unhandled rejections behavior]:rejection behavior:(strict warn none)' \
  '--use-bundled-ca[use bundled CA store (default)]' \
  '--use-largepages=[re-map the Node.js static code to large memory pages at startup]:mode:(off on silent)' \
  "--use-openssl-ca[use OpenSSL's default CA store]" \
  "--use-system-ca[Use system's CA store]" \
  '(- 1 *)--v8-options[print v8 command line options]' \
  "--v8-pool-size=[set v8's thread pool size]:number" \
  "--watch[run in watch mode]" \
  '--watch-kill-signal=[kill signal to send to the process on watch mode restarts]:signal:_signals -s' \
  "--watch-path=[path to watch]: :_node_files" \
  '--watch-preserve-output[preserve outputs on watch mode restart]' \
  '--zero-fill-buffers[automatically zero-fill all newly allocated Buffer and SlowBuffer instances]' \
  '(- 1 *)'{-h,--help}'[print node command line options]' \
  '(- 1 *)'{-v,--version}'[print Node.js version]' \
  '*: :_node_args' && ret=0

return ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
