#compdef openvpn3
# ------------------------------------------------------------------------------
# Copyright (c) 2011 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
# * Redistributions of source code must retain the above copyright
# notice, this list of conditions and the following disclaimer.
# * Redistributions in binary form must reproduce the above copyright
# notice, this list of conditions and the following disclaimer in the
# documentation and/or other materials provided with the distribution.
# * Neither the name of the zsh-users nor the
# names of its contributors may be used to endorse or promote products
# derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for openvpn 3 (https://github.com/openvpn/openvpn3) (https://openvpn.net/openvpn-client-for-linux/).
#  version: 13_beta-1 (Jul 1 2021)
#           openvpn 3/Linux git:makepkg:2031975261858750 (openvpn3)
#           openvpn core 3.git:HEAD:ce0c9963 linux x86_64 64-bit
#
# ------------------------------------------------------------------------------
# Author
# -------
#
#  * undg (https://github.com/undg)
#
# ------------------------------------------------------------------------------

local sessions_configs_names=$(openvpn3 sessions-list | grep 'Config name:' | awk '{print $3}' | xargs)
local sessions_paths=$(openvpn3 sessions-list | grep 'Path:' | awk '{print $2}' | xargs)
local sessions_interfaces=$(openvpn3 sessions-list | grep 'Device:' | awk '{print $4}' | xargs)
local sessions_users=$(openvpn3 sessions-list | grep 'Owner:' | awk '{print $2}' | xargs)
local sessions_names=$(openvpn3 sessions-list | grep 'Session name:' | awk '{print $2}' | xargs)
local configs_names=$(openvpn3 configs-list | awk 'count&&!--count; /\/net\/openvpn\//{count=2}' | awk '{print $1}' | xargs)
local configs_paths=$(openvpn3 configs-list | grep '/net/openvpn/v3/configuration/' | xargs)

_openvpn3_config-acl(){
    _arguments \
        {-s,--show}"[Show the current access control lists]" \
        {-o,--path}"[OBJ-PATH Path to the configuration in the configuration manager]: :($configs_paths)" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --path, where configuration profile name is used instead]: :($configs_names)" \
        {-S,--seal}"[Make the configuration profile permanently read-only]" \
        {-R,--revoke}"[<UID | username> Revoke this user access from this configuration profile]" \
        {-G,--grant}"[<UID | username> Grant this user access to this configuration profile]" \
        "--public-access[<true|false> Set/unset the public access flag]" \
        "--lock-down[<true|false> Set/unset the lock-down flag.Will disable config retrieval for users]: :(true false)" \
        "--config-path[OBJ-PATH Alias for --path]: :($configs_paths)" \
}

_openvpn3_config-import(){
    _arguments \
        {-p,--persistent}"[Make the configuration profile persistent through service restarts]" \
        {-n,--name}"[NAME Provide a different name for the configuration (default: CFG-FILE)]" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CFG-FILE Configuration file to import]: :_files" \
}

_openvpn3_config-manage(){
    _arguments \
        {-s,--show}"[Show current configuration options]" \
        {-r,--rename}"[NEW-CONFIG-NAME Renames the configuration]" \
        {-o,--path}"[CONFIG-PATH Path to the configuration in the configuration manager]: :($configs_paths)" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --path, where configuration profile name is used instead]: :($configs_names)" \
        "--unset-override[<name> Removes the <name> override]" \
        "--tls-version-min[<tls_1_0|tls_1_1|tls_1_2|tls_1_3> Sets the minimal TLS version for the control channel]: :(tls_1_0 tls_1_1 tls_1_2 tls_1_3)" \
        "--tls-cert-profile[<legacy preferred suiteb> Sets the control channel tls profile]: :(legacy preferred suiteb)" \
        "--server-override[<value> Replace the remote, connecting to this server instead the server specified in the configuration]" \
        "--proxy-username[<value> HTTP Proxy username to authenticate as]" \
        "--proxy-port[<value> HTTP Proxy port to connect on]" \
        "--proxy-password[<value> HTTP Proxy password to use for authentication]" \
        "--proxy-host[<value> HTTP Proxy to connect via, overrides configuration file http-proxy]" \
        "--proxy-auth-cleartext[<true|false> Adds the boolean override proxy-auth-cleartext]: :(true false)" \
        "--proto-override[<tcp|udp> Overrides the protocol being used]: :(tcp upd)" \
        "--port-override[<value> Replace the remote port, connecting to this port instead of the configuration value]" \
        "--persist-tun[<true|false> Adds the boolean override persist-tun]: :(true false)" \
        "--ipv6[<yes|no|default> Sets the IPv6 policy of the client]: :(yes no default)" \
        "--force-cipher-aes-cbc[<true|false> Adds the boolean override force-cipher-aes-cbc]: :(true false)" \
        "--dns-sync-lookup[<true|false> Adds the boolean override dns-sync-lookup]: :(true false)" \
        "--dns-setup-disabled[<true|false> Adds the boolean override dns-setup-disabled]: :(true false)" \
        "--dns-fallback-google[<true|false> Adds the boolean override dns-fallback-google]: :(true false)" \
        "--config-path[CONFIG-PATH Alias for --path]: :($configs_paths)" \
        "--auth-fail-retry[<true|false> Adds the boolean override auth-fail-retry]: :(true false)" \
        "--allow-compression[<no asym yes> Set compression mode]: :(no asym yes)" \
}

_openvpn3_config-remove(){
    _arguments \
        {-o,--path}"[OBJ-PATH Path to the configuration in the configuration manager]: :($configs_paths)" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --path, where configuration profile name is used instead]: :($configs_names)" \
        "--force[Force the deletion process without asking for confirmation]" \
        "--config-path[OBJ-PATH Alias for --path]: :($configs_paths)" \
}

_openvpn3_config-show(){
    _arguments \
        {-o,--path}"[OBJ-PATH Path to the configuration in the configuration manager]: :($configs_paths)" \
        {-j,--json}"[Dump the configuration in JSON format]" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --path, where configuration profile name is used instead]: :($configs_names)" \
        "--config-path[OBJ-PATH Alias for --path]: :($configs_paths)" \
}

_openvpn3_configs-list(){
    _arguments \
        {-h,--help}"[This help screen]" \
}

_openvpn3_help(){
    _arguments \
        {-h,--help}"[This help screen]" \
}

_openvpn3_log(){
    _arguments \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --session-path, where configuration profile name is used instead]: :($sessions_configs_names $configs_names)" \
        {-I,--interface}"[INTERFACE Alternative to --session-path, where tun interface name is used instead]: :($sessions_interfaces)" \
        "--session-path[SESSION-PATH Receive log events for a specific session]: :($sessions_paths)" \
        "--log-level[LOG-LEVEL Set the log verbosity level of messages to be shown (default: 4)]" \
        "--config-events[Receive log events issued by the configuration manager]" \
}

_openvpn3__session-acl(){
    _arguments \
        {-s,--show}"[Show the current access control lists]" \
        {-o,--path}"[SESSION-PATH Path to the session in the session manager]: :($sessions_paths)" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --path, where configuration profile name is used instead]: :($sessions_configs_names)" \
        {-R,--revoke}"[<UID | username> Revoke this user access from this session]" \
        {-I,--interface}"[INTERFACE   Alternative to --path, where tun interface name is used instead]: :($sessions_interfaces)" \
        {-G,--grant}"[<UID | username> Grant this user access to this session]" \
        "--session-path[SESSION-PATH Alias for --path]: :($sessions_paths)" \
        "--public-access[<true|false> Set/unset the public access flag]: :(true false)" \
        "--allow-log-access[<true|false> Can users granted access also access the session log?]: :(true false)" \
}

_openvpn3_session-manage(){
    _arguments \
        {-o,--path}"[SESSION-PATH Path to the session in the session manager]: :($sessions_paths)" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --path, where configuration profile name is used instead]: :($sessions_configs_names)" \
        {-R,--resume}"[Resumes a paused VPN session]" \
        {-P,--pause}"[Pauses the VPN session]" \
        {-I,--interface}"[INTERFACE Alternative to --path, where tun interface name is used instead]: :($sessions_interfaces)" \
        {-D,--disconnect}"[Disconnects a VPN session]" \
        "--session-path[SESSION-PATH Alias for --path]: :($sessions_paths)" \
        "--restart[Disconnect and reconnect a running VPN session]" \
        "--cleanup[Clean up stale sessions]" \
}

_openvpn3_session-start(){
    _arguments \
        {-p,--config-path}"[CONFIG-PATH Configuration path to an already imported configuration]: :($configs_paths)" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-FILE Configuration file to start directly]: :_files" \
        "--persist-tun[Enforces persistent tun/seamless tunnel (requires --config)]" \
}

_openvpn3_session-stats(){
    _arguments \
        {-o,--path}"[SESSION-PATH Path to the configuration in the configuration manager]: :($sessions_paths)" \
        {-j,--json}"[Dump the configuration in JSON format]" \
        {-h,--help}"[This help screen]" \
        {-c,--config}"[CONFIG-NAME Alternative to --path, where configuration profile name is used instead]: :($sessions_configs_names)" \
        {-I,--interface}"[INTERFACE Alternative to --path, where tun interface name is used instead]: :($sessions_interfaces)" \
        "--session-path[SESSION-PATH Alias for --path]: :($sessions_paths)" \
}

_openvpn3_sessions-list(){
    _arguments \
        {-h,--help}"[This help screen]" \
}

_openvpn3_shell-completion(){
    _arguments \
        {-h,--help}"[This help screen]" \
        "--list-commands[List all available commands]" \
        "--list-options[COMMAND List all available options for a specific command]: :($(openvpn3 shell-completion --list-commands))" \
        "--arg-helper[OPTION Used together with --list-options, lists value hint to an option]"
}

_openvpn3_version(){
    _arguments \
        {-h,--help}"[This help screen]" \
}


_openvpn3_command(){
    local -a _openvpn3_cmds
    _openvpn3_cmds=(
        "config-acl: Manage access control lists for configurations" \
        "config-import: Import configuration profiles" \
        "config-manage: Manage configuration properties" \
        "config-remove: Remove an available configuration profile" \
        "config-show: Show/dump a configuration profile" \
        "configs-list: List all available configuration profiles" \
        "help: This help screen" \
        "log: Receive log events as they occur" \
        "session-acl: Manage access control lists for sessions" \
        "session-manage: Manage VPN sessions" \
        "session-start: Start a new VPN session" \
        "session-stats: Show session statistics" \
        "sessions-list: List available VPN sessions" \
        "shell-completion: Helper function to provide shell completion data" \
        "version: Show program version information" \
    )

    if ((CURRENT == 1)); then
        _describe -t commands 'openvpn3 commands' _openvpn3_cmds
    else
        local curcontext="$curcontext"
        cmd="${${_openvpn3_cmds[(r)$words[1]:*]%%:*}}"
        if (($#cmd)); then
            if (( $+functions[_openvpn3_$cmd] )); then
                _openvpn3_$cmd
            else
                _message "no options for $cmd"
            fi
        else
            _message "no more options"
        fi
    fi
}

_arguments \
    {-h,--help}"[that This help screen]" \
    "*::openvpn3 commands:_openvpn3_command" \

