{"zgenom.zsh": {"type": "init"}, "options.zsh": {"type": "option"}, "functions/zgenom-*": {"type": "api", "template": ["#!/usr/bin/env zsh", "", "function zgenom-{}() {", "    ", "}", "", "zgenom-{} $@"]}, "functions/__zgenom": {"type": "private"}, "functions/__zgenom_*": {"type": "private", "template": ["#!/usr/bin/env zsh", "", "function __zgenom_{}() {", "    ", "}", "", "__zgenom_{} $@"]}, "functions/_zgenom": {"type": "completion"}, "README.md": {"type": "readme"}}