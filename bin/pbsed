#!/bin/bash
#
#/ Original source: https://github.com/rtomayko/dotfiles
#
#/ Usage: pbsed [<options>] <pattern>
#/ Run sed(1) on the contents of the pboard and put the result back
#/ on the pboard. All sed options and arguments are supported.
# shellcheck disable=SC2002
set -e
set -o pipefail

# shellcheck disable=SC2317
function echo-stderr() {
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}


function only-run-on() {
  # shellcheck disable=SC2086
  if [[ "$(uname -s | tr '[:upper:]' '[:lower:]')" != "$(echo $1 | tr '[:upper:]' '[:lower:]')" ]]; then
    fail "This script only runs on $1, this machine is running $(uname -s)"
  else
    debug "OS ($(uname -s)) is valid..."
  fi
}

# print help/usage with no pattern/args
test $# -eq 0 -o "$1" = "--help" && {
  grep '^#/' < "$0" | cut -c4-
  exit 0
}

# pipe pboard through sed and then back again
only-run-on darwin
pbpaste | sed "$@" | pbcopy
exit $?
