[base]
default          = none
unknown-token    = red,bold
commandseparator = none
redirection      = none
here-string-tri  = 141
here-string-text = bg:19
here-string-var  = 177,bg:19
exec-descriptor  = yellow,bold
comment          = black,bold
correct-subtle   = bg:55
incorrect-subtle = bg:52
subtle-bg        = bg:17
secondary        = safari
recursive-base   = 186

[command-point]
reserved-word  = 146
reserved-word  = 146
alias          = 63
suffix-alias   = 63
global-alias   = bg:19
builtin        = 63
function       = 63
command        = 63
precommand     = 63
hashed-command = 63
single-sq-bracket = 63
double-sq-bracket = 63
double-paren   = 146

[paths]
path          = 154
pathseparator = 
path-to-dir   = 154,underline
globbing      = 114
globbing-ext  = 153

[brackets]
paired-bracket = bg:blue
bracket-level-1 = 117
bracket-level-2 = 141
bracket-level-3 = 90

[arguments]
single-hyphen-option   = 177
double-hyphen-option   = 177
back-quoted-argument   = none
single-quoted-argument = 146
double-quoted-argument = 146
dollar-quoted-argument = 146

[in-string]
; backslash in $'...'
back-dollar-quoted-argument           = 177
; backslash or $... in "..."
back-or-dollar-double-quoted-argument = 177

[other]
variable             = none
assign               = none
assign-array-bracket = 63
history-expansion    = blue,bold

[math]
mathvar = blue,bold
mathnum = 154
matherr = red

[for-loop]
forvar = none
fornum = 154
; operator
foroper = 146
; separator
forsep = 109

[case]
case-input       = 63
case-parentheses = 141
case-condition   = bg:19
