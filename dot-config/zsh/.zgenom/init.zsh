# {{{
# Generated by zgenom.
# This file will be overwritten the next time you run zgenom save!

# Don't source twice or when zgenom was invoked otherwise.
[[ -z $ZGENOM_PLUGINS ]] || return 0

export PMSPEC=0fiPs
export ZPFX=/Users/<USER>/.config/zsh/.zqs-zgenom/polaris

ZGENOM_PLUGINS=(https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master https://github.com/caiogondim/bullet-train.zsh/___ supercrabtree/k/___ zsh-users/zsh-autosuggestions/___ RobSis/zsh-completion-generator/___ unixorn/1password-op.plugin.zsh/___ srijanshetty/docker-zsh/___ zsh-users/zsh-completions/___ chrissicool/zsh-256color/___ sharat87/pip-app/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master romkatv/zsh-defer/___ mafredri/zsh-async/___ mroth/evalcache/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master hlissner/zsh-autopair/___ olets/zsh-abbr/___ StackExchange/blackbox/___ peterhurford/git-it-on.zsh/___ skx/sysadmin-util/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ unixorn/fzf-zsh-plugin/___ so-fancy/diff-so-fancy/___ unixorn/git-extra-commands/___ djui/alias-tips/___ eventi/noreallyjustfuckingstopalready/___ unixorn/tumult.plugin.zsh/___ unixorn/warhol.plugin.zsh/___ unixorn/jpb.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/rake-completion.zshplugin/___ zsh-users/zsh-history-substring-search/___ zdharma-continuum/fast-syntax-highlighting/___ ohmyzsh/ohmyzsh/master colored-man-pages/___ command-not-found/___ zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ zsh-users/zsh-autosuggestions/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ ohmyzsh/ohmyzsh\ plugins/python/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___ ohmyzsh/ohmyzsh/master zdharma-continuum/fast-syntax-highlighting/___ zsh-users/zsh-history-substring-search/___ unixorn/rake-completion.zshplugin/___ unixorn/autoupdate-zgenom/___ unixorn/jpb.zshplugin/___ unixorn/warhol.plugin.zsh/___ unixorn/tumult.plugin.zsh/___ eventi/noreallyjustfuckingstopalready/___ djui/alias-tips/___ unixorn/git-extra-commands/___ so-fancy/diff-so-fancy/___ unixorn/fzf-zsh-plugin/___ unixorn/bitbucket-git-helpers.plugin.zsh/___ skx/sysadmin-util/___ peterhurford/git-it-on.zsh/___ StackExchange/blackbox/___ olets/zsh-abbr/___ hlissner/zsh-autopair/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master mroth/evalcache/___ mafredri/zsh-async/___ romkatv/zsh-defer/___ ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master ohmyzsh/ohmyzsh/master sharat87/pip-app/___ chrissicool/zsh-256color/___ zsh-users/zsh-completions/___ srijanshetty/docker-zsh/___ unixorn/1password-op.plugin.zsh/___ RobSis/zsh-completion-generator/___ zsh-users/zsh-autosuggestions/___ supercrabtree/k/___ https://github.com/caiogondim/bullet-train.zsh/___)

ZSH=/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master

# ### Plugins & Completions
fpath=(/Users/<USER>/.config/zsh/.zgenom/https-COLON--SLASH--SLASH-github.com-SLASH-caiogondim/bullet-train.zsh/___ /Users/<USER>/.config/zsh/.zgenom/supercrabtree/k/___ /Users/<USER>/.config/zsh/.zgenom/zsh-users/zsh-autosuggestions/___ /Users/<USER>/.config/zsh/.zgenom/RobSis/zsh-completion-generator/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/1password-op.plugin.zsh/___ /Users/<USER>/.config/zsh/.zgenom/srijanshetty/docker-zsh/___ /Users/<USER>/.config/zsh/.zgenom/zsh-users/zsh-completions/___/src /Users/<USER>/.config/zsh/.zgenom/chrissicool/zsh-256color/___ /Users/<USER>/.config/zsh/.zgenom/sharat87/pip-app/___ /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/macos /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/brew /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/vagrant /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/screen /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/rsync /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/python /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/github /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/git /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/colored-man-pages /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/chruby /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/aws /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/sudo /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/pip /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/fzf /Users/<USER>/.config/zsh/.zgenom/romkatv/zsh-defer/___ /Users/<USER>/.config/zsh/.zgenom/mafredri/zsh-async/___ /Users/<USER>/.config/zsh/.zgenom/mroth/evalcache/___ /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/zoxide /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/iterm2 /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/gh /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/eza /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/aliases /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/laravel /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/composer /Users/<USER>/.config/zsh/.zgenom/hlissner/zsh-autopair/___ /Users/<USER>/.config/zsh/.zgenom/olets/zsh-abbr/___ /Users/<USER>/.config/zsh/.zgenom/StackExchange/blackbox/___ /Users/<USER>/.config/zsh/.zgenom/peterhurford/git-it-on.zsh/___ /Users/<USER>/.config/zsh/.zgenom/skx/sysadmin-util/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/bitbucket-git-helpers.plugin.zsh/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/fzf-zsh-plugin/___ /Users/<USER>/.config/zsh/.zgenom/so-fancy/diff-so-fancy/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/git-extra-commands/___ /Users/<USER>/.config/zsh/.zgenom/djui/alias-tips/___ /Users/<USER>/.config/zsh/.zgenom/eventi/noreallyjustfuckingstopalready/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/tumult.plugin.zsh/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/warhol.plugin.zsh/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/jpb.zshplugin/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/autoupdate-zgenom/___ /Users/<USER>/.config/zsh/.zgenom/unixorn/rake-completion.zshplugin/___ /Users/<USER>/.config/zsh/.zgenom/zsh-users/zsh-history-substring-search/___ /Users/<USER>/.config/zsh/.zgenom/zdharma-continuum/fast-syntax-highlighting/___ /Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master ${fpath})

# ### General modules
typeset -ga zsh_loaded_plugins
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/oh-my-zsh.sh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/oh-my-zsh.sh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/oh-my-zsh.sh"
zsh_loaded_plugins+=( "zdharma-continuum/fast-syntax-highlighting" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/zdharma-continuum/fast-syntax-highlighting/___/fast-syntax-highlighting.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/zdharma-continuum/fast-syntax-highlighting/___/fast-syntax-highlighting.plugin.zsh"
zsh_loaded_plugins+=( "zsh-users/zsh-history-substring-search" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/zsh-users/zsh-history-substring-search/___/zsh-history-substring-search.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/zsh-users/zsh-history-substring-search/___/zsh-history-substring-search.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/rake-completion.zshplugin" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/rake-completion.zshplugin/___/rake_completion.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/rake-completion.zshplugin/___/rake_completion.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/autoupdate-zgenom" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/autoupdate-zgenom/___/autoupdate-zgen.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/autoupdate-zgenom/___/autoupdate-zgen.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/jpb.zshplugin" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/jpb.zshplugin/___/jpb.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/jpb.zshplugin/___/jpb.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/warhol.plugin.zsh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/warhol.plugin.zsh/___/warhol.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/warhol.plugin.zsh/___/warhol.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/tumult.plugin.zsh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/tumult.plugin.zsh/___/tumult.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/tumult.plugin.zsh/___/tumult.plugin.zsh"
zsh_loaded_plugins+=( "eventi/noreallyjustfuckingstopalready" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/eventi/noreallyjustfuckingstopalready/___/noreallyjustfuckingstopalready.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/eventi/noreallyjustfuckingstopalready/___/noreallyjustfuckingstopalready.plugin.zsh"
zsh_loaded_plugins+=( "djui/alias-tips" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/djui/alias-tips/___/alias-tips.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/djui/alias-tips/___/alias-tips.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/git-extra-commands" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/git-extra-commands/___/git-extra-commands.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/git-extra-commands/___/git-extra-commands.plugin.zsh"
zsh_loaded_plugins+=( "so-fancy/diff-so-fancy" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/so-fancy/diff-so-fancy/___/diff-so-fancy.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/so-fancy/diff-so-fancy/___/diff-so-fancy.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/fzf-zsh-plugin" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/fzf-zsh-plugin/___/fzf-zsh-plugin.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/fzf-zsh-plugin/___/fzf-zsh-plugin.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/bitbucket-git-helpers.plugin.zsh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/bitbucket-git-helpers.plugin.zsh/___/bitbucket-git-helpers.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/bitbucket-git-helpers.plugin.zsh/___/bitbucket-git-helpers.plugin.zsh"
zsh_loaded_plugins+=( "skx/sysadmin-util" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/skx/sysadmin-util/___/sysadmin-util.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/skx/sysadmin-util/___/sysadmin-util.plugin.zsh"
zsh_loaded_plugins+=( "peterhurford/git-it-on.zsh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/peterhurford/git-it-on.zsh/___/git-it-on.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/peterhurford/git-it-on.zsh/___/git-it-on.plugin.zsh"
zsh_loaded_plugins+=( "StackExchange/blackbox" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/StackExchange/blackbox/___/blackbox.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/StackExchange/blackbox/___/blackbox.plugin.zsh"
zsh_loaded_plugins+=( "olets/zsh-abbr" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/olets/zsh-abbr/___/zsh-abbr.zsh" source "/Users/<USER>/.config/zsh/.zgenom/olets/zsh-abbr/___/zsh-abbr.zsh"
zsh_loaded_plugins+=( "hlissner/zsh-autopair" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/hlissner/zsh-autopair/___/zsh-autopair.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/hlissner/zsh-autopair/___/zsh-autopair.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/composer" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/composer/composer.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/composer/composer.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/laravel" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/laravel/laravel.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/laravel/laravel.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/aliases" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/aliases/aliases.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/aliases/aliases.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/eza" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/eza/eza.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/eza/eza.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/gh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/gh/gh.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/gh/gh.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/iterm2" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/iterm2/iterm2.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/iterm2/iterm2.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/zoxide" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/zoxide/zoxide.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/zoxide/zoxide.plugin.zsh"
zsh_loaded_plugins+=( "mroth/evalcache" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/mroth/evalcache/___/evalcache.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/mroth/evalcache/___/evalcache.plugin.zsh"
zsh_loaded_plugins+=( "mafredri/zsh-async" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/mafredri/zsh-async/___/async.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/mafredri/zsh-async/___/async.plugin.zsh"
zsh_loaded_plugins+=( "romkatv/zsh-defer" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/romkatv/zsh-defer/___/zsh-defer.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/romkatv/zsh-defer/___/zsh-defer.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/fzf" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/fzf/fzf.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/fzf/fzf.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/pip" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/pip/pip.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/pip/pip.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/sudo" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/sudo/sudo.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/sudo/sudo.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/aws" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/aws/aws.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/aws/aws.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/chruby" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/chruby/chruby.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/chruby/chruby.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/colored-man-pages" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/colored-man-pages/colored-man-pages.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/colored-man-pages/colored-man-pages.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/git" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/git/git.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/git/git.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/github" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/github/github.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/github/github.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/python" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/python/python.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/python/python.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/rsync" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/rsync/rsync.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/rsync/rsync.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/screen" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/screen/screen.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/screen/screen.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/vagrant" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/vagrant/vagrant.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/vagrant/vagrant.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/brew" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/brew/brew.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/brew/brew.plugin.zsh"
zsh_loaded_plugins+=( "ohmyzsh/ohmyzsh/plugins/macos" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/macos/macos.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/ohmyzsh/ohmyzsh/master/plugins/macos/macos.plugin.zsh"
zsh_loaded_plugins+=( "sharat87/pip-app" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/sharat87/pip-app/___/pip-app.sh" source "/Users/<USER>/.config/zsh/.zgenom/sharat87/pip-app/___/pip-app.sh"
zsh_loaded_plugins+=( "chrissicool/zsh-256color" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/chrissicool/zsh-256color/___/zsh-256color.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/chrissicool/zsh-256color/___/zsh-256color.plugin.zsh"
zsh_loaded_plugins+=( "srijanshetty/docker-zsh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/srijanshetty/docker-zsh/___/docker.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/srijanshetty/docker-zsh/___/docker.plugin.zsh"
zsh_loaded_plugins+=( "unixorn/1password-op.plugin.zsh" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/unixorn/1password-op.plugin.zsh/___/1password-op.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/unixorn/1password-op.plugin.zsh/___/1password-op.plugin.zsh"
zsh_loaded_plugins+=( "RobSis/zsh-completion-generator" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/RobSis/zsh-completion-generator/___/zsh-completion-generator.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/RobSis/zsh-completion-generator/___/zsh-completion-generator.plugin.zsh"
zsh_loaded_plugins+=( "zsh-users/zsh-autosuggestions" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/zsh-users/zsh-autosuggestions/___/zsh-autosuggestions.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/zsh-users/zsh-autosuggestions/___/zsh-autosuggestions.plugin.zsh"
zsh_loaded_plugins+=( "supercrabtree/k" )
set -- && ZERO="/Users/<USER>/.config/zsh/.zgenom/supercrabtree/k/___/k.plugin.zsh" source "/Users/<USER>/.config/zsh/.zgenom/supercrabtree/k/___/k.plugin.zsh"

# }}}
