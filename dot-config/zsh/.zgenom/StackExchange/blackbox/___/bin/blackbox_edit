#!/usr/bin/env bash

#
# blackbox_edit -- Decrypt a file temporarily for edition, then re-encrypts it again
#
set -e
source "${0%/*}/_blackbox_common.sh"

for param in "$@" ; do

  unencrypted_file=$(get_unencrypted_filename "$param")
  encrypted_file=$(get_encrypted_filename "$param")

  echo >&2 ========== PLAINFILE '"'$unencrypted_file'"'
  echo >&2 ========== ENCRYPTED '"'$encrypted_file'"'

  if  ! is_on_cryptlist "$encrypted_file" && ! is_on_cryptlist "$unencrypted_file" ; then
    read -r -p "Encrypt file $param? (y/n) " ans
    case "$ans" in
      y* | Y*)
        "${BLACKBOX_HOME}/blackbox_register_new_file" "$unencrypted_file"
        ;;
      *)
        echo >&2 'Skipping...'
        continue
        ;;
    esac
  fi
  "${BLACKBOX_HOME}/blackbox_edit_start" "$unencrypted_file"
  $EDITOR "$(get_unencrypted_filename "$unencrypted_file")"
  "${BLACKBOX_HOME}/blackbox_edit_end" "$unencrypted_file"

done
