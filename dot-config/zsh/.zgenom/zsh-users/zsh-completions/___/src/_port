#compdef port
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for MacPorts (https://www.macports.org/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * <PERSON> <<EMAIL>>
#  * <PERSON><PERSON> <<EMAIL>>
#  * <PERSON><PERSON><PERSON> <<EMAIL>>
# -----------------------------------------------------------------------------
# License
# -------
#
# Copyright (c) 2016, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the <organization> nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# ------------------------------------------------------------------------------

_port() {
  local -a  revupgrade_options select_options \
              pseudo_common pseudo_advanced port_prefix

  local port_path=$(which port)
  port_prefix=${port_path%/bin/port}

  pseudo_common=(all current active inactive actinact installed uninstalled outdated
  obsolete requested unrequested leaves rleaves)

  pseudo_advanced=('variants:' 'variant:' 'description:' 'depends:'
  'depends_lib:' 'depends_run:' 'depends_build:' 'depends_fetch:' 'depends_extract:'
  'portdir:' 'homepage:' 'epoch:' 'platforms:' 'platform:' 'name:' 'long_description:'
  'maintainers:' 'maintainer:' 'categories:' 'category:' 'version:' 'revision:' 'license:')

  select_options=(
  '--summary:Display summary of selected options'
  '--list:List available versions for the group'
  '--set:Select the given version for the group'
  '--show:Show which version is currently selected for the group (default if none given)'
  )

  revupgrade_options=('--id-loadcmd-check:Run more checks against a special loadcommand in Mach-O binaries')

  _arguments -s -C \
    '-v[Verbose mode (generate verbose messages)]' \
    '-d[Debug mode (generate debugging messages, implies -v)]' \
    '-q[Quiet mode (suppress messages)]' \
    "-n[Don't upgrade dependencies (affects upgrade and install)]" \
    "-R[Also upgrade dependents (only affects upgrade) - note that this does not upgrade dependents' dependencies]" \
    '-u[Uninstall non-active ports when upgrading and uninstalling]' \
    '-f[Force mode (ignore state file)]' \
    '-o[Honor state files even if the Portfile has been modified since (called -o because it used to mean "older")]' \
    '-s[Source-only mode (build and install from source, do not attempt to fetch binary archives)]' \
    '-b[Binary-only mode (build and install from binary archives, ignore source, abort if no archive available)]' \
    '-c[Autoclean mode (execute clean after install)]' \
    "-k[Keep mode (don't autoclean after install)]" \
    '-D[Specify portdir]' \
    '-F[Read and process the file of commands specified by the argument.]' \
    '-p[Despite any errors encountered, proceed to process multiple ports and commands.]' \
    '-y[Perform a dry run.]' \
    '-t[Enable trace mode debug facilities on platforms that support it (macOS).]' \
    "1:Port actions:_port_actions" \
    '::Per-action arguments:_port_dispatch' \
    && return 0
}

(( $+functions[_port_actions] )) ||
_port_actions() {
  local -a actions
  actions=(
    'activate:Activate the given ports'
    'archive:Archive the given ports, i.e. install the port image but do not activate'
    'archivefetch:Fetch archive for the given ports'
    'build:Build the given ports'
    'bump:Update the outdated checksums of a Portfile'
    'cat:Writes the Portfiles of the given ports to stdout'
    'checksum:Compares the checksums for the downloaded files of the given ports'
    'clean:Removes files associated with the given ports'
    'configure:Configure the given ports'
    'contents:Returns a list of files installed by given ports'
    'deactivate:Deactivates the given ports'
    'dependents:Returns a list of installed dependents for each of the given ports'
    'deps:Display a dependency listing for the given ports'
    'destroot:Destroot the given ports'
    'diagnose:Detects common issues'
    'dir:Returns the directories of the given ports'
    'distcheck:Checks if the given ports can be fetched from all of its master_sites'
    'distfiles:Returns a list of distfiles for the given port'
    'dmg:Creates a dmg for each of the given ports'
    'dpkg:Creates a dpkg for each of the given ports'
    'echo:Returns the list of ports the argument expands to'
    'edit:Edit given ports'
    'extract:Extract the downloaded files of the given ports'
    'fetch:Downloaded distfiles for the given ports'
    'file:Returns the path to the Portfile for each of the given ports'
    'gohome:Opens the homepages of the given ports in your browser'
    'help:Displays short help texts for the given actions'
    'info:Returns information about the given ports '
    'install:Installs the given ports'
    'installed:List installed versions of the given port, or all installed ports if no port is given'
    'lint:Checks if the Portfile is lint-free for each of the given ports'
    'list:List the available version for each of the given ports'
    'livecheck:Checks if a new version of the software is available'
    'load:Interface to launchctl(1) for ports providing startup items'
    'location:Returns the install location for each of the given ports'
    'log:Shows main log for given ports'
    'logfile:Returns the log file path for each of the given ports'
    'mdmg:Creates a dmg containing an mpkg for each of the given ports and their dependencies'
    'mirror:Fetches distfiles for the given ports'
    'mpkg:Creates an mpkg for each of the given ports and their dependencies'
    'notes:Displays informational notes for each of the given ports'
    'outdated:Returns a list of outdated ports'
    'patch:Applies patches to each of the given ports'
    'pkg:Creates a pkg for each of the given ports'
    'platform:Returns the current platform that port is running on'
    'provides:Return which port provides each of the files given'
    'rdependents:Recursive version of dependents'
    'rdeps:Display a recursive dependency listing for the given ports'
    'reclaim:Reclaims disk space'
    'rev-upgrade:Scan for broken binaries in the installed ports and rebuild them as needed'
    'rpm:Creates a rpm for each of the given ports'
    'search:Search for a port'
    'select:Select between multiple versions of a versioned port'
    'selfupdate:Upgrade MacPorts itself and run the sync target'
    'setrequested:Marks each of the given ports as requested'
    'space:Show the disk space used by the given ports'
    'srpm:Creates a srpm for each of the given ports'
    'sync:Synchronize the set of Portfiles'
    'test:Run tests on each of the given ports'
    'unarchive:Unarchive the destroot of the given ports from installed images'
    'uninstall:Uninstall the given ports'
    'unload:Interface to launchctl(1) for ports providing startup items'
    'unsetrequested:Marks each of the given ports as unrequested'
    'upgrade:Upgrades the given ports to the latest version'
    'url:Returns the URL for each of the given ports'
    'usage:Returns basic usage of the port command'
    'variants:Returns a list of variants provided by the given ports, with descriptions if present'
    'version:Returns the version of MacPorts'
    'work:Returns the path to the work directory for each of the given ports'
  )

  _describe -t actions 'actions' actions
}

_port_dispatch() {
  local cache_policy
  zstyle -s ":completion:${curcontext}:" cache-policy cache_policy
  zstyle ":completion:${curcontext}:" cache-policy ${cache_policy:-_port_caching_policy}

  case "$words[2]" in
    provides)
      _files
      ;;
    search)
      _message 'pattern'
      ;;
    help)
      _port_actions
      ;;
    select)
      _call_function - _port_select
      ;;
    contents|deactivate|setrequested|space|uninstall|unsetrequested)
      # Cache the list of installed ports.
      if ( [[ ${+_port_installed_packages} -eq 0 ]] || _cache_invalid PORT_INSTALLED_PACKAGES ) &&
        ! _retrieve_cache PORT_INSTALLED_PACKAGES;
      then
        _port_installed_packages=( $(_call_program path-all "port -q echo installed") )
        _store_cache PORT_INSTALLED_PACKAGES _port_installed_packages
      fi
      _alternative \
        "ports:Installed ports:($_port_installed_packages)" \
        "pseudo-common:Common Pseudo-portnames:($pseudo_common)" \
        "pseudo-advanced:Advanced Pseudo-portnames:($pseudo_advanced)"
      ;;
    upgrade)
      # No good reason to actually cache outdated ports list
      local outdated_packages
      outdated_packages=( $(_call_program path-outdated "port -q echo outdated") )
      _alternative -- \
        "upgrade-options:Upgrade options:_port_upgrade_options" \
        "ports:Outdated ports:($outdated_packages)" \
        "pseudo-common:Common Pseudo-portnames:($pseudo_common)" \
        "pseudo-advanced:Advanced Pseudo-portnames:($pseudo_advanced)"
      ;;
    rev-upgrade)
      if (( CURRENT == 3 )); then
        _describe 'Rev-upgrade options' revupgrade_options
      fi
      ;;
    outdated|sync)
      # No need to complete anything more here.
      return 0;
      ;;
    selfupdate)
      _all_labels 'Selfupdate options' '--nosync'
      ;;
    *)
      # Cache the list of all ports.
      if ( [[ ${+_port_available_packages} -eq 0 ]] || _cache_invalid PORT_AVAILABLE_PACKAGES ) &&
        ! _retrieve_cache PORT_AVAILABLE_PACKAGES;
      then
        _port_available_packages=( $(_call_program path-all "port -q echo all") )
        _store_cache PORT_AVAILABLE_PACKAGES _port_available_packages
      fi
      _alternative \
        "ports:Available ports:($_port_available_packages)" \
        "pseudo-common:Common Pseudo-portnames:($pseudo_common)" \
        "pseudo-advanced:Advanced Pseudo-portnames:($pseudo_advanced)"
      ;;
  esac
}

(( $+functions[_port_upgrade_options] )) ||
_port_upgrade_options() {
  local -a options
  options=(
    '--enforce-variants:Upgrade all given ports and their dependencies'
    '--force:Ignore circumstances that would normally cause ports to be skipped'
    '--no-replace:Do not automatically install ports that replace a new-obsolete port you have installed'
    '--no-rev-upgrade:Do not run rev-upgrade after upgrading'
  )

  _describe -t options 'options' options
}

_port_select() {
  if (( CURRENT == 3 )); then
    _describe 'Port select options' select_options
  elif (( CURRENT == 4 )); then
    local select_group=($port_prefix/etc/select/*(N:t))
    _describe "Port select groups" select_group
  elif [[ $CURRENT -eq 5 && $words[3] == '--set' ]]; then
    local select_variants
    select_variants=("${(f)$(port select --list $words[4] | sed -e '1 d' -e 's/^[ \t]*//' -e 's/ (active)$//')}")
    _describe "Port select group $words[4] variants" select_variants
  fi
}

_port_caching_policy() {
  local reg_time comp_time check_file

  zmodload -F zsh/stat b:zstat 2> /dev/null
  case "${1##*/}" in
    PORT_INSTALLED_PACKAGES)
      check_file=$port_prefix/var/macports/registry/registry.db
      ;;
    PORT_AVAILABLE_PACKAGES)
      check_file=${$(port dir MacPorts)%/*/*}/PortIndex
      ;;
  esac
  reg_time=$(zstat +mtime $check_file)
  comp_time=$(zstat +mtime $1)
  return $(( reg_time < comp_time ))
}

_port "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
