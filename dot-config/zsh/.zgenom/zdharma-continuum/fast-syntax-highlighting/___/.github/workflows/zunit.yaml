name: zunit
on: [push, pull_request]
jobs:
  zunit:
    runs-on: ubuntu-latest

    steps:
      - name: Check out repository code
        uses: actions/checkout@v2

      - name: Install dependencies
        run: |
          sudo apt install -y zsh

          mkdir bin
          curl -fsSL https://raw.githubusercontent.com/molovo/revolver/v0.2.4/revolver > bin/revolver
          curl -fsSL https://raw.githubusercontent.com/molovo/color/d8f91ab5fcfceb623ae45d3333ad0e543775549c/color.zsh > bin/color

          git clone https://github.com/zunit-zsh/zunit.git zunit.git
          cd zunit.git
          git checkout b86c006f62db138a119e9be3a4b41e28876889b2
          ./build.zsh
          cd ..
          mv ./zunit.git/zunit bin

          chmod u+x bin/{color,revolver,zunit}

      - name: Unit tests
        run: |
          export PATH="$PWD/bin:$PATH"
          zunit
