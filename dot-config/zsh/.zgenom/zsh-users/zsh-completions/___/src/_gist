#compdef gist
# ------------------------------------------------------------------------------
# Copyright (c) 2017 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for gist (https://github.com/defunkt/gist)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Akira Maeda <https://github.com/glidenote>
#  * Patrick Ziegler <https://github.com/patrick96>
#  * Shivam Mehta <https://github.com/maniac-en>
#
# ------------------------------------------------------------------------------

local curcontext="$curcontext" state line ret=1
typeset -A opt_args

_arguments -C \
  '(--login)--login[Authenticate gist on this computer.]' \
  '(-f --filename)'{-f,--filename}'[Sets the filename and syntax type.]:NAME' \
  '(-t --type)'{-t,--type}'[Sets the file extension and syntax type.]:EXT' \
  '(-p --private --no-private)'{-p,--private}'[Makes your gist private.]' \
  '(--no-private -p --private)'--no-private'[Makes your gist no private.]' \
  '(-d --description)'{-d,--description}'[Adds a description to your gist.]:DESCRIPTION' \
  '(-s --shorten)'{-s,--shorten}'[Shorten the gist URL using git.io.]' \
  '(-u --update)'{-u,--update}'[Update an existing gist.]:URL ID:user_gists' \
  '(-c --copy)'{-c,--copy}'[Copy the resulting URL to the clipboard]' \
  '(-e --embed)'{-e,--embed}'[Copy the embed code for the gist to the clipboard]' \
  '(-o --open --no-open)'{-o,--open}'[Open the resulting URL in a browser]' \
  '(--no-open -o --open)'--no-open'[No open the resulting URL in a browser]' \
  '(-P --paste)'{-P,--paste}'[Paste from the clipboard to gist]' \
  '(-R --raw)'{-R,--raw}'[Display raw URL of the new gist]' \
  '(-l --list)'{-l,--list}'[List all gists for user ]::user' \
  '(-h --help)'{-h,--help}'[print options help]' \
  '(-v --version)'{-v,--version}'[print version]' \
  '(-r --read)'{-r,--read}'[Read a gist and print out the contents]:user gists:_gist_read_gists' \
  '*: :_files' && ret=0

_gist_cache_policy() {
  # rebuild if cache is more than a day old
  local -a oldp
  oldp=( "$1"(mh+1) )
  (( $#oldp ))
}

_gist_read_gists() {
  local update_policy ret=1
  zstyle -s ":completion:${curcontext}:" cache-policy update_policy
  if [[ -z "$update_policy" ]]; then
    zstyle ":completion:${curcontext}:" cache-policy _gist_cache_policy
  fi

  # stores the gists of the logged in user in the format ID[Description]
  _list=()
  _cached_gists="user_gists"

  # retrieve/Write gists from/to cache
  if _cache_invalid $_cached_gists  || ! _retrieve_cache $_cached_gists; then
    _gists=$(gist -l)

    if [ $? -eq 0 ]; then
      _store_cache $_cached_gists _gists
    else
      # some error occurred, the user is probably not logged in
      # set _gists to an empty string so that no completion is attempted
      _gists=""
    fi
  else
    _retrieve_cache $_cached_gists
  fi

  if [ -n "$_gists" ]; then
    echo "$_gists" | while read -r line; do
      # Splitting the gist -l output
      url="$(echo "$line" | cut -d " " -f 1 | cut -d "/" -f 4)"
      # gists w/o descriptions can have only one column in the output, those
      # have their description set to an empty string
      description="$(echo "$line" | awk '{if(NF > 1){$1=""; print $0}}')"

      _list+=( "${url}[${description}]" )
    done

    _values "gists" $_list
    ret=0
  fi

  return ret
}

return ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
