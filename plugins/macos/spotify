#!/usr/bin/env bash

function spotify() {
# Copyright (c) 2012--2023 <PERSON><PERSON> <<EMAIL>>
#
# Contains numerous helpful contributions from <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>.

# Permission is hereby granted, free of charge, to any person
# obtaining a copy of this software and associated documentation files
# (the "Software"), to deal in the Software without restriction,
# including without limitation the rights to use, copy, modify, merge,
# publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:

# The above copyright notice and this permission notice shall be
# included in all copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
# BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
# ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

USER_CONFIG_DEFAULTS="CLIENT_ID=\"\"\nCLIENT_SECRET=\"\"";
USER_CONFIG_FILE="${HOME}/.shpotify.cfg";
if ! [[ -f "${USER_CONFIG_FILE}" ]]; then
    touch "${USER_CONFIG_FILE}";
    echo -e "${USER_CONFIG_DEFAULTS}" > "${USER_CONFIG_FILE}";
fi
source "${USER_CONFIG_FILE}";

# Set the percent change in volume for vol up and vol down
VOL_INCREMENT=10

showAPIHelp() {
    echo;
    echo "Connecting to Spotify's API:";
    echo;
    echo "  This command line application needs to connect to Spotify's API in order to";
    echo "  find music by name. It is very likely you want this feature!";
    echo;
    echo "  To get this to work, you need to sign up (or in) and create an 'Application' at:";
    echo "  https://developer.spotify.com/dashboard/create";
    echo;
    echo "  Once you've created an application, find the 'Client ID' and 'Client Secret'";
    echo "  values, and enter them into your shpotify config file at '${USER_CONFIG_FILE}'";
    echo;
    echo "  Be sure to quote your values and don't add any extra spaces!";
    echo "  When done, it should look like this (but with your own values):";
    echo '  CLIENT_ID="abc01de2fghijk345lmnop"';
    echo '  CLIENT_SECRET="qr6stu789vwxyz"';
}

showHelp () {
    echo "Usage:";
    echo;
    echo "  `basename $0` <command>";
    echo;
    echo "Commands:";
    echo;
    echo "  play                         # Resumes playback where Spotify last left off.";
    echo "  play <song name>             # Finds a song by name and plays it.";
    echo "  play album <album name>      # Finds an album by name and plays it.";
    echo "  play artist <artist name>    # Finds an artist by name and plays it.";
    echo "  play list <playlist name>    # Finds a playlist by name and plays it.";
    echo "  play uri <uri>               # Play songs from specific uri.";
    echo;
    echo "  next                         # Skips to the next song in a playlist.";
    echo "  prev                         # Returns to the previous song in a playlist.";
    echo "  replay                       # Replays the current track from the beginning.";
    echo "  pos <time>                   # Jumps to a time (in secs) in the current song.";
    echo "  pause                        # Pauses (or resumes) Spotify playback.";
    echo "  stop                         # Stops playback.";
    echo "  quit                         # Stops playback and quits Spotify.";
    echo;
    echo "  vol up                       # Increases the volume by 10%.";
    echo "  vol down                     # Decreases the volume by 10%.";
    echo "  vol <amount>                 # Sets the volume to an amount between 0 and 100.";
    echo "  vol [show]                   # Shows the current Spotify volume.";
    echo;
    echo "  status                       # Shows the current player status.";
    echo "  status artist                # Shows the currently playing artist.";
    echo "  status album                 # Shows the currently playing album.";
    echo "  status track                 # Shows the currently playing track.";
    echo;
    echo "  share                        # Displays the current song's Spotify URL and URI."
    echo "  share url                    # Displays the current song's Spotify URL and copies it to the clipboard."
    echo "  share uri                    # Displays the current song's Spotify URI and copies it to the clipboard."
    echo;
    echo "  toggle shuffle               # Toggles shuffle playback mode.";
    echo "  toggle repeat                # Toggles repeat playback mode.";
    showAPIHelp
}

cecho(){
    bold=$(tput bold);
    green=$(tput setaf 2);
    reset=$(tput sgr0);
    echo $bold$green"$1"$reset;
}

showArtist() {
    echo `osascript -e 'tell application "Spotify" to artist of current track as string'`;
}

showAlbum() {
    echo `osascript -e 'tell application "Spotify" to album of current track as string'`;
}

showTrack() {
    echo `osascript -e 'tell application "Spotify" to name of current track as string'`;
}

showStatus () {
    state=`osascript -e 'tell application "Spotify" to player state as string'`;
    cecho "Spotify is currently $state.";
    duration=`osascript -e 'tell application "Spotify"
            set durSec to (duration of current track / 1000) as text
            set tM to (round (durSec / 60) rounding down) as text
            if length of ((durSec mod 60 div 1) as text) is greater than 1 then
                set tS to (durSec mod 60 div 1) as text
            else
                set tS to ("0" & (durSec mod 60 div 1)) as text
            end if
            set myTime to tM as text & ":" & tS as text
            end tell
            return myTime'`;
    position=`osascript -e 'tell application "Spotify"
            set pos to player position
            set nM to (round (pos / 60) rounding down) as text
            if length of ((round (pos mod 60) rounding down) as text) is greater than 1 then
                set nS to (round (pos mod 60) rounding down) as text
            else
                set nS to ("0" & (round (pos mod 60) rounding down)) as text
            end if
            set nowAt to nM as text & ":" & nS as text
            end tell
            return nowAt'`;

    echo -e $reset"Artist: $(showArtist)\nAlbum: $(showAlbum)\nTrack: $(showTrack) \nPosition: $position / $duration";
}

if [ $# = 0 ]; then
    showHelp;
else
	if [ ! -d /Applications/Spotify.app ] && [ ! -d $HOME/Applications/Spotify.app ]; then
		echo "The Spotify application must be installed."
		return 1
	fi

    if [ $(osascript -e 'application "Spotify" is running') = "false" ]; then
        osascript -e 'tell application "Spotify" to activate' || return 1
        sleep 2
    fi
fi
while [ $# -gt 0 ]; do
    arg=$1;

    case $arg in
        "play"    )
            if [ $# != 1 ]; then
                # There are additional arguments, so find out how many
                array=( $@ );
                len=${#array[@]};
                SPOTIFY_SEARCH_API="https://api.spotify.com/v1/search";
                SPOTIFY_TOKEN_URI="https://accounts.spotify.com/api/token";
                if [ -z "${CLIENT_ID}" ]; then
                    cecho "Invalid Client ID, please update ${USER_CONFIG_FILE}";
                    showAPIHelp;
                    return 1;
                fi
                if [ -z "${CLIENT_SECRET}" ]; then
                    cecho "Invalid Client Secret, please update ${USER_CONFIG_FILE}";
                    showAPIHelp;
                    return 1;
                fi
                SHPOTIFY_CREDENTIALS=$(printf "${CLIENT_ID}:${CLIENT_SECRET}" | base64 | tr -d "\n"|tr -d '\r');
                SPOTIFY_PLAY_URI="";

                getAccessToken() {
                    cecho "Connecting to Spotify's API";

                    SPOTIFY_TOKEN_RESPONSE_DATA=$( \
                        curl "${SPOTIFY_TOKEN_URI}" \
                            --silent \
                            -X "POST" \
                            -H "Authorization: Basic ${SHPOTIFY_CREDENTIALS}" \
                            -d "grant_type=client_credentials" \
                    )
                    if ! [[ "${SPOTIFY_TOKEN_RESPONSE_DATA}" =~ "access_token" ]]; then
                        cecho "Authorization failed, please check ${USER_CONFG_FILE}"
                        cecho "${SPOTIFY_TOKEN_RESPONSE_DATA}"
                        showAPIHelp
                        return 1
                    fi
                    SPOTIFY_ACCESS_TOKEN=$( \
                        printf "${SPOTIFY_TOKEN_RESPONSE_DATA}" \
                        | command grep -E -o '"access_token":".*",' \
                        | sed 's/"access_token"://g' \
                        | sed 's/"//g' \
                        | sed 's/,.*//g' \
                    )
                }

                searchAndPlay() {
                    type="$1"
                    Q="$2"

                    getAccessToken;

                    cecho "Searching ${type}s for: $Q";

                    SPOTIFY_PLAY_URI=$( \
                        curl -s -G $SPOTIFY_SEARCH_API \
                            -H "Authorization: Bearer ${SPOTIFY_ACCESS_TOKEN}" \
                            -H "Accept: application/json" \
                            --data-urlencode "q=$Q" \
                            -d "type=$type&limit=1&offset=0" \
                        | command grep -E -o "spotify:$type:[a-zA-Z0-9]+" -m 1
                    )
                }

                case $2 in
                    "list"  )
                        _args=${array[@]:2:$len};
                        Q=$_args;

                        getAccessToken;

                        cecho "Searching playlists for: $Q";

                        results=$( \
                            curl -s -G $SPOTIFY_SEARCH_API --data-urlencode "q=$Q" -d "type=playlist&limit=10&offset=0" -H "Accept: application/json" -H "Authorization: Bearer ${SPOTIFY_ACCESS_TOKEN}" \
                            | command grep -E -o "spotify:playlist:[a-zA-Z0-9]+" -m 10 \
                        )

                        count=$( \
                            echo "$results" | command grep -c "spotify:playlist" \
                        )

                        if [ "$count" -gt 0 ]; then
                            random=$(( $RANDOM % $count));

                            SPOTIFY_PLAY_URI=$( \
                                echo "$results" | awk -v random="$random" '/spotify:playlist:[a-zA-Z0-9]+/{i++}i==random{print; exit}' \
                            )
                        fi;;

                    "album" | "artist" | "track"    )
                        _args=${array[@]:2:$len};
                        searchAndPlay $2 "$_args";;

                    "uri"  )
                        SPOTIFY_PLAY_URI=${array[@]:2:$len};;

                    *   )
                        _args=${array[@]:1:$len};
                        searchAndPlay track "$_args";;
                esac

                if [ "$SPOTIFY_PLAY_URI" != "" ]; then
                    if [ "$2" = "uri" ]; then
                        cecho "Playing Spotify URI: $SPOTIFY_PLAY_URI";
                    else
                        cecho "Playing ($Q Search) -> Spotify URI: $SPOTIFY_PLAY_URI";
                    fi

                    osascript -e "tell application \"Spotify\" to play track \"$SPOTIFY_PLAY_URI\"";

                else
                    cecho "No results when searching for $Q";
                fi

            else

                # play is the only param
                cecho "Playing Spotify.";
                osascript -e 'tell application "Spotify" to play';
            fi
            break ;;

        "pause"    )
            state=`osascript -e 'tell application "Spotify" to player state as string'`;
            if [ $state = "playing" ]; then
              cecho "Pausing Spotify.";
            else
              cecho "Playing Spotify.";
            fi

            osascript -e 'tell application "Spotify" to playpause';
            break ;;

        "stop"    )
            state=`osascript -e 'tell application "Spotify" to player state as string'`;
            if [ $state = "playing" ]; then
              cecho "Pausing Spotify.";
              osascript -e 'tell application "Spotify" to playpause';
            else
              cecho "Spotify is already stopped."
            fi

            break ;;

        "quit"    ) cecho "Quitting Spotify.";
            osascript -e 'tell application "Spotify" to quit';
            break ;;

        "next"    ) cecho "Going to next track." ;
            osascript -e 'tell application "Spotify" to next track';
            showStatus;
            break ;;

        "prev"    ) cecho "Going to previous track.";
            osascript -e '
            tell application "Spotify"
                set player position to 0
                previous track
            end tell';
            showStatus;
            break ;;

        "replay"  ) cecho "Replaying current track.";
            osascript -e 'tell application "Spotify" to set player position to 0'
            break ;;

        "vol"    )
            vol=`osascript -e 'tell application "Spotify" to sound volume as integer'`;
            if [[ $2 = "" || $2 = "show" ]]; then
                cecho "Current Spotify volume level is $vol.";
                break ;
            elif [ "$2" = "up" ]; then
              if [ $vol -le $(( 100-$VOL_INCREMENT )) ]; then
                    newvol=$(( vol+$VOL_INCREMENT ));
                    cecho "Increasing Spotify volume to $newvol.";
                else
                    newvol=100;
                    cecho "Spotify volume level is at max.";
                fi
            elif [ "$2" = "down" ]; then
                if [ $vol -ge $(( $VOL_INCREMENT )) ]; then
                    newvol=$(( vol-$VOL_INCREMENT ));
                    cecho "Reducing Spotify volume to $newvol.";
                else
                    newvol=0;
                    cecho "Spotify volume level is at min.";
                fi
            elif [[ $2 =~ ^[0-9]+$ ]] && [[ $2 -ge 0 && $2 -le 100 ]]; then
                newvol=$2;
                cecho "Setting Spotify volume level to $newvol";
            else
                echo "Improper use of 'vol' command"
                echo "The 'vol' command should be used as follows:"
                echo "  vol up                       # Increases the volume by $VOL_INCREMENT%.";
                echo "  vol down                     # Decreases the volume by $VOL_INCREMENT%.";
                echo "  vol [amount]                 # Sets the volume to an amount between 0 and 100.";
                echo "  vol                          # Shows the current Spotify volume.";
                return 1;
            fi

            osascript -e "tell application \"Spotify\" to set sound volume to $newvol";
            break ;;

        "toggle"  )
            if [ "$2" = "shuffle" ]; then
                osascript -e 'tell application "Spotify" to set shuffling to not shuffling';
                curr=`osascript -e 'tell application "Spotify" to shuffling'`;
                cecho "Spotify shuffling set to $curr";
            elif [ "$2" = "repeat" ]; then
                osascript -e 'tell application "Spotify" to set repeating to not repeating';
                curr=`osascript -e 'tell application "Spotify" to repeating'`;
                cecho "Spotify repeating set to $curr";
            fi
            break ;;

        "status" )
            if [ $# != 1 ]; then
                # There are additional arguments, a status subcommand
                case $2 in
                    "artist" )
                        showArtist;
                        break ;;

                    "album" )
                        showAlbum;
                        break ;;

                    "track" )
                        showTrack;
                        break ;;
                esac
            else
                # status is the only param
                showStatus;
            fi
            break ;;

        "info" )
            info=`osascript -e 'tell application "Spotify"
                set durSec to (duration of current track / 1000)
                set tM to (round (durSec / 60) rounding down) as text
                if length of ((durSec mod 60 div 1) as text) is greater than 1 then
                    set tS to (durSec mod 60 div 1) as text
                else
                    set tS to ("0" & (durSec mod 60 div 1)) as text
                end if
                set myTime to tM as text & "min " & tS as text & "s"
                set pos to player position
                set nM to (round (pos / 60) rounding down) as text
                if length of ((round (pos mod 60) rounding down) as text) is greater than 1 then
                    set nS to (round (pos mod 60) rounding down) as text
                else
                    set nS to ("0" & (round (pos mod 60) rounding down)) as text
                end if
                set nowAt to nM as text & "min " & nS as text & "s"
                set info to "" & "\nArtist:         " & artist of current track
                set info to info & "\nTrack:          " & name of current track
                set info to info & "\nAlbum Artist:   " & album artist of current track
                set info to info & "\nAlbum:          " & album of current track
                set info to info & "\nSeconds:        " & durSec
                set info to info & "\nSeconds played: " & pos
                set info to info & "\nDuration:       " & mytime
                set info to info & "\nNow at:         " & nowAt
                set info to info & "\nPlayed Count:   " & played count of current track
                set info to info & "\nTrack Number:   " & track number of current track
                set info to info & "\nPopularity:     " & popularity of current track
                set info to info & "\nId:             " & id of current track
                set info to info & "\nSpotify URL:    " & spotify url of current track
                set info to info & "\nArtwork:        " & artwork url of current track
                set info to info & "\nPlayer:         " & player state
                set info to info & "\nVolume:         " & sound volume
                set info to info & "\nShuffle:        " & shuffling
                set info to info & "\nRepeating:      " & repeating
            end tell
            return info'`
            cecho "$info";
            break ;;

        "share"     )
            uri=`osascript -e 'tell application "Spotify" to spotify url of current track'`;
            remove='spotify:track:'
            url=${uri#$remove}
            url="https://open.spotify.com/track/$url"

            if [ "$2" = "" ]; then
                cecho "Spotify URL: $url"
                cecho "Spotify URI: $uri"
                echo "To copy the URL or URI to your clipboard, use:"
                echo "\`spotify share url\` or"
                echo "\`spotify share uri\` respectively."
            elif [ "$2" = "url" ]; then
                cecho "Spotify URL: $url";
                echo -n $url | pbcopy
            elif [ "$2" = "uri" ]; then
                cecho "Spotify URI: $uri";
                echo -n $uri | pbcopy
            fi
            break ;;

        "pos"   )
            cecho "Adjusting Spotify play position."
            osascript -e "tell application \"Spotify\" to set player position to $2";
            break ;;

        "help" )
            showHelp;
            break ;;
        * )
            showHelp;
            return 1;

    esac
done
}
