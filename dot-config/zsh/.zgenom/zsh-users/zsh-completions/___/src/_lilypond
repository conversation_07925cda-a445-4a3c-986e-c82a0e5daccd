#compdef lilypond
# ------------------------------------------------------------------------------
# Copyright (c) 2021 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for lilypond 2.22.2 (https://lilypond.org/)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Andre Kugland (https://github.com/kugland)
#
# ------------------------------------------------------------------------------

_arguments -C -S -A "-*" \
  '(- 1 *)-dhelp[show help for scheme options]' \
  '(- 1 *)'{-h,--help}'[show usage information]' \
  '(- 1 *)'{-v,--version}'[show version number]' \
  '(- 1 *)'{-w,--warranty}'[show warranty and copyright]' \
  '(-f --formats)'{-f,--formats=}'[select format(s) to generate]:format(s) to generate:_sequence compadd - pdf svg png ps eps' \
  '--pdf[generate .pdf files]' \
  '--svg[generate .svg files]' \
  '--png[generate .png files]' \
  '--ps[generate .ps files]' \
  '(-E --eps)'{-E,--eps}'[generate .eps files]' \
  '(-O --pspdfopt)'{-O,--pspdfopt=}'[set .ps/.pdf optimization]:.ps/.pdf optimization:(size TeX TeX-GS)' \
  '*'{-d,--define-default=}'[set scheme option]:scheme option:->scheme_opt' \
  '(-e --evaluate)'{-e,--evaluate=}'[evaluate scheme code]:scheme code' \
  '(-H --header)'{-H,--header=}'[dump header field to file]:field' \
  '*'{-I,--include=}'[append directory to include search path]:directory:_files -/' \
  '(-i --init)'{-i,--init=}'[use file as init file]:file:_files -g "*.(#i)ly(-.)"' \
  '(-j --jail)'{-j,--jail=}'[chroot to jail, become user:group and cd into dir]:jail arg (user,group,jail,dir)' \
  '(-o --output)'{-o,--output=}'[set output base filename or folder]:output base filename or folder:_files' \
  '(-l --loglevel -s --silent -V --verbose)'{-l,--loglevel=}'[set level of the log]:log level [INFO]:(NONE ERROR WARNING BASIC PROGRESS INFO DEBUG)' \
  '(-l --loglevel -s --silent -V --verbose)'{-s,--silent}'[no progress, only error messages (same as --loglevel=ERROR)]' \
  '(-l --loglevel -s --silent -V --verbose)'{-V,--verbose}'[be verbose (same as --loglevel=DEBUG)]' \
  '*:input file:_files -g "*.(#i)ly(-.)"'

case $state in
  scheme_opt)
    _values 'scheme option' \
      'anti-alias-factor[set anti-alias factor]:anti-alias-factor (1-8)' \
      'aux-files[create .tex, .texi, .count files in the EPS backend]:aux-files:(\#t \#f)' \
      'backend[select backend]:backend:(eps null ps scm svg)' \
      'check-internal-types[check every property assignment for types]:check-internal-types:(\#t \#f)' \
      'clip-systems[generate cut-out snippets of a score]:clip-systems:(\#t \#f)' \
      'crop[match the size of the normal output to the typeset image]:crop:(\#t \#f)' \
      'datadir[LilyPond prefix for data files]: :_files -/' \
      'debug-gc-assert-parsed-dead[ensure that all references to parsed objects are dead]:debug-gc-assert-parsed-dead:(\#t \#f)' \
      'debug-gc[dump memory debugging statistics]:debug-gc:(\#t \#f)' \
      'debug-lexer[debug the flex lexer]:debug-lexer:(\#t \#f)' \
      'debug-page-breaking-scoring[dump scores for many different page breaking configurations]:debug-page-breaking-scoring:(\#t \#f)' \
      'debug-parser[debug the bison parser]:debug-parser:(\#t \#f)' \
      'debug-property-callbacks[debug cyclic callback chains]:debug-property-callbacks:(\#t \#f)' \
      'debug-skylines[debug skylines]:debug-skylines:(\#t \#f)' \
      'delete-intermediate-files[delete unusable, intermediate PostScript files]:delete-intermediate-files:(\#t \#f)' \
      'dump-signatures[dump output signatures of each system]:dump-signatures:(\#t \#f)' \
      'embed-source-code[embed the source files inside the generated PDF document]:embed-source-code:(\#t \#f)' \
      'eps-box-padding[pad left edge of the output EPS bounding box by given amount]:eps-box-padding (mm)' \
      'font-export-dir[directory for exporting fonts as PostScript files]:font-export-dir:_files -/' \
      'font-ps-resdir[build a subset of PostScript resource directory for embedding fonts]:font-ps-resdir' \
      'gs-api[whether to use the Ghostscript API (read-only if not available)]:gs-api:(\#t \#f)' \
      'gs-load-fonts[load fonts via Ghostscript]:gs-load-fonts:(\#t \#f)' \
      'gs-load-lily-fonts[load only LilyPond fonts via Ghostscript]:gs-load-lily-fonts:(\#t \#f)' \
      'gs-never-embed-fonts[make Ghostscript embed only TrueType fonts and no other font format]:gs-never-embed-fonts:(\#t \#f)' \
      'gui[run LilyPond from a GUI and redirect stderr to a log file]:gui:(\#t \#f)' \
      'help[show this help]:help:(\#t \#f)' \
      'include-book-title-preview[include book titles in preview images]:include-book-title-preview:(\#t \#f)' \
      'include-eps-fonts[include fonts in separate-system EPS files]:include-eps-fonts:(\#t \#f)' \
      'include-settings[included file before the score is processed]:include-settings:_files -g "*.(#i)ly(-.)"' \
      'job-count[process in parallel, using the given number of jobs]:job-count' \
      'log-file[output log to file (.log will be appended)]:log-file' \
      'max-markup-depth[maximum depth for the markup tree]:max-markup-depth' \
      'midi-extension[set the default file extension for MIDI output]:midi-extension:(mid midi)' \
      'music-font-encodings[use font encodings and the PostScript "show" operator with music fonts]:music-font-encodings:(\#t \#f)' \
      'music-strings-to-paths[convert text strings to paths when glyphs belong to a music font]:music-strings-to-paths:(\#t \#f)' \
      'outline-bookmarks[use bookmarks in table of contents metadata]:outline-bookmarks:(\#t \#f)' \
      'paper-size[set default paper size]:paper-size:(\"a1\" \"a2\" \"a3\" \"a4\" \"a5\" \"a6\" \"b1\" \"b2\" \"b3\" \"b4\" \"b5\" \"b6\" \"c1\" \"c2\" \"c3\" \"c4\" \"c5\" \"c6\" \"f4\" \"government-legal\" \"government-letter\" \"imperial\" \"junior-legal\" \"ledger\" \"legal\" \"letter\" \"folio\" \"octavo\" \"quarto\" \"tabloid\")' \
      'pixmap-format[set GhostScript'\''s output format for pixel images]:pixmap-format:(fpng jpeg jpegcmyk jpeggray png16m pngalpha pnggray pngmonod)' \
      'png-height[image height for PNG output]:png-height (pixels)' \
      'png-width[image width for PNG output]:png-width (pixels)' \
      'point-and-click[add point & click links to PDF and SVG output]:point-and-click:(\#t \#f)' \
      'preview[create preview images also]:preview:(\#t \#f)' \
      'print-pages[print pages in the normal way]:print-pages:(\#t \#f)' \
      'profile-property-accesses[keep statistics of get_property() calls]:profile-property-accesses:(\#t \#f)' \
      'protected-scheme-parsing[continue when errors in inline Scheme are caught in the parser]:protected-scheme-parsing:(\#t \#f)' \
      'read-file-list[specify file which contains a list of input files]:read-file-list:(\#t \#f)' \
      'relative-includes[look for the included file relative to the current file]:relative-includes:(\#t \#f)' \
      'resolution[set resolution for generating PNG pixmaps to given value]:resolution (dpi)' \
      'safe[run in safer mode]:safe:(\#t \#f)' \
      'separate-log-files[use separate log files for each input]:separate-log-files:(\#t \#f)' \
      'show-available-fonts[list available font names]:show-available-fonts:(\#t \#f)' \
      'strict-infinity-checking[force a crash on encountering Inf and NaN floating point exceptions]:strict-infinity-checking:(\#t \#f)' \
      'strip-output-dir[don'\''t use directories from input files while constructing output file names]:strip-output-dir:(\#t \#f)' \
      'strokeadjust[set the PostScript '\''strokeadjust'\'' operator explicitly]:strokeadjust:(\#t \#f)' \
      'svg-woff[use woff font files in SVG backend]:svg-woff:(\#t \#f)' \
      'verbose[verbose output]:verbose:(\#t \#f)' \
      'warning-as-error[change all warning and programming_error messages into errors]:warning-as-error:(\#t \#f)'
    ;;
esac

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
