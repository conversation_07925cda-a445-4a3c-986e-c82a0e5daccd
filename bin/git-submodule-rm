#!/usr/bin/env bash
#
# Author: <PERSON> <github.com/pascal-so>
#
# This is a heavily modified version of the script from <PERSON>'s dotfiles:
#     https://github.com/myfreeweb/dotfiles
#
# The reason for the heavy modification is that the original script left
# traces of the submodule behind, specifically in `.git/modules`. This
# udpated version also improves error handling.
#
# Some useful resources:
#     https://stackoverflow.com/a/7646931
#     https://stackoverflow.com/a/16162000

set -eu

if [[ $# -eq 0 ]]; then
      zsh_debug_echo 'Usage: git submodule-rm <path ...>'
  exit 2
fi

if [[ ! -f .gitmodules ]]; then
      zsh_debug_echo 'Run this from the root of your repo which must have a .gitmodules file.'
  exit 1
fi

if [[ ! -w .gitmodules ]]; then
      zsh_debug_echo '.gitmodules file is not writable for the current user.'
  exit 1
fi

for path in "$@"; do
  submodule_path="${path%/}"

  if [[ -z "$(git config --file=.gitmodules "submodule.${submodule_path}.url")" ]]; then
        zsh_debug_echo "Submodule '$submodule_path' not found."
    continue
  fi

  if [[ ! -w "$submodule_path" ]]; then
        zsh_debug_echo "Submodule path '$submodule_path' is not writable."
    continue
  fi

  # If any of the following commands fail it's probably because the repo
  # was already in a corrupted state and we can't do anything to handle
  # that anyway so we just let the entire script crash.

  git submodule --quiet deinit --force "$submodule_path"
  rmdir "$submodule_path"
  git rm --cached -rfq "$submodule_path"

  git config -f .gitmodules --remove-section "submodule.$submodule_path"
  rm -rf ".git/modules/$submodule_path"

  git add .gitmodules
done
