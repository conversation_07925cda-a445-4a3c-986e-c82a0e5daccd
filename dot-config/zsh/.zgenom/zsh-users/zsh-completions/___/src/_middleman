#compdef middleman
# ------------------------------------------------------------------------------
# Copyright (c) 2013 <PERSON><PERSON>, MIT License
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for middleman (https://middlemanapp.com/)
#  Includes commands from middleman-blog and middleman-deploy extensions.
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Jozef Izso (https://github.com/jozefizso)
#
# ------------------------------------------------------------------------------

local ret=1 state

local -a common_ops
common_ops=(
  "--help[Display help]"
)

typeset -A opt_args
_arguments \
  ':subcommand:->subcommand' \
  $common_ops \
  '*::options:->options' && ret=0

case $state in
  subcommand)
    local -a subcommands
    subcommands=(
      "article:Create a new blog article"
      "build:Builds the static site for deployment"
      "console:Start an interactive console in the context of your Middleman application"
      "deploy:Deploy a middleman built site over rsync, ftp, sftp, or git (e.g. gh-pages on github)."
      "extension:Create Middleman extension scaffold"
      "init:Create new project"
      "server:Start the preview server"
      "upgrade:Upgrade installed bundle"
      "version:Show version"
    )

    _describe -t subcommands 'middleman subcommand' subcommands && ret=0
  ;;

  options)
    local -a args
    args=(
      $common_ops
    )

    local -a help
    help=(
      "--help[Display help information]"
    )

    case $words[1] in
      article)
        args=(
          {-d,--date}"[The date to create the post with (defaults to now)]:date"
        )
      ;;

      build)
        args=(
          "--clean[Remove orphaned files from build (Default: true)]"
          "--no-clean[Disable removing orphaned files from build]"
          {-g,--glob}"[Build a subset of the project]:glob"
          "--verbose[Print debug messages]"
          "--instrument[Print instrument messages]:instrument"
          "--profile[Generate profiling report for the build]"
        )
      ;;

      console)
        args=(
          {-e,--environment}"[The environment Middleman will run under (Default: development)]:environment"
          "--verbose[Print debug messages]"
        )
      ;;

      deploy)
        args=(
          {-b,--build-before}"[Run \`middleman build\` before the deploy step]"
        )
      ;;

      extension)
        args=(
          "--git[Default: true]"
        )
      ;;

      init)
        args=(
          {-T,--template}"[Use a project template: default, html5, mobile, smacss, empty. (Default: default)]:template"
          "--css-dir      [The path to the css files]:css_dir"
          "--js-dir       [The path to the javascript files]:js_dir"
          "--images-dir   [The path to the image files]:images_dir"
          "--rack         [Include a config.ru file]"
          "--skip-gemfile [Don't create a Gemfile]"
          "--skip-bundle  [Don't run bundle install]"
          "--skip-git     [Skip Git ignores and keeps]"
        )
      ;;

      server)
        args=(
          {-e,--environment}"[The environment Middleman will run under (Default: development)]:environment"
          {-h,--host}"[Bind to HOST address (Default: 0.0.0.0)]:host"
          {-p,--port}"[The port Middleman will listen on (Default: 4567)]:port"
          "--verbose                    [Print debug messages]"
          "--instrument                 [Print instrument messages]:instrument"
          "--disable-watcher            [Disable the file change and delete watcher process]"
          "--profile                    [Generate profiling report for server startup]"
          "--reload-paths               [Additional paths to auto-reload when files change]:reload_paths"
          "--force-polling              [Force file watcher into polling mode]"
        )
      ;;

      upgrade)
        args=()
      ;;

      version)
        args=()
      ;;
    esac

    _arguments $args && ret=0
  ;;
esac

return ret
