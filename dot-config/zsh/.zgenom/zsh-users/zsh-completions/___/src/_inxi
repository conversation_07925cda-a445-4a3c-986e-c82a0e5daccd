#compdef inxi
# ------------------------------------------------------------------------------
# Description
# -----------
#
# Completion script of inxi 3.3.21-00 (https://smxi.org/docs/inxi.htm)
#
# a full featured CLI system information tool
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# * <PERSON><PERSON><PERSON> <<EMAIL>>
#
# ------------------------------------------------------------------------------

_inxi() {
  typeset -A opt_args
  local context state line
  local curcontext="$curcontext"
  local ret=1

  local colors=($(seq 0 42))

  _arguments -C -s \
    '(-A --audio)'{-A,--audio}"[Show Audio/sound card(s) information]" \
    '(-b --basic)'{-b,--basic}"[Show basic output, short form]" \
    '(-B --battery)'{-B,--battery}"[Show system Battery information]" \
    '(-C --cpu)'{-C,--cpu}"[Show full CPU output]" \
    '(-d --disk-full --optical)'{-d,--disk-full,--optical}"[Show hard + optical drive info]" \
    '(-D --disk)'{-D,--disk}"[Show hard Disk info]" \
    '(-E --bluetooth)'{-E,--bluetooth}'[Show bluetooth device data report, if available]' \
    '--edid[Full graphics data, triggers -a -G]' \
    '(-f --flags)'{-f,--flags}"[Show all CPU flags used]" \
    '(-F --full)'{-F,--full}"[Show Full output]" \
    '(-G --graphics)'{-G,--graphics}"[Show Graphic card(s) information]" \
    '(- *)'{-h,--help}"[Show list of command-line options]" \
    '(-i --ip)'{-i,--ip}"[Show WAN IP address and local interfaces]" \
    '(-I --info)'{-I,--info}"[Show Information: processes, uptime, etc.]" \
    '(-j --swap)'{-j,--swap}'[Swap in use]' \
    '(-J --usb)'{-J,--usb}"[Show USB data for attached Hubs and Devices]" \
    '(-l --label)'{-l,--label}"[Show partition labels]" \
    '(-L --logical)'{-L,--logical}'[Logical devices]' \
    '(-m --memory)'{-m,--memory}"[Memory (RAM) data]" \
    '(--memory-modules --mm)'{--memory-modules,--mm}'[Memory (RAM) data. Exclude empty module slots]' \
    '(--memory-short --ms)'{--memory-short,--ms}'[Memory (RAM) data. Show only short Memory RAM report]' \
    '(-M --machine)'{-M,--machine}"[Show Machine data]" \
    '(-n --network-advanced)'{-n,--network-advanced}"[Show Advanced Network card information]" \
    '(-N --network)'{-N,--network}"[Show Network card(s) information]" \
    '(-o --unmounted)'{-o,--unmounted}"[Show unmounted partition information]" \
    '(-p --partitions-full)'{-p,--partitions-full}"[Show full Partition information]" \
    '(-P --partitions)'{-P,--partitions}"[Show basic Partition information]" \
    '(-r --repos)'{-r,--repos}"[Show distro repository data]" \
    '(-R --raid)'{-R,--raid}"[Show RAID data]" \
    "(- *)"--recommends"[Check inxi dependencies + recommends]" \
    '(-s --sensors)'{-s,--sensors}"[Show output from configured sensors]" \
    "--slots[Show PCI slots]" \
    '(-S --system)'{-S,--system}"[Show System information]" \
    '(-t --processes)'{-t+,--processes=}"[Show processes]:processes:((c\:'CPU only' m\:'Memory only' cm\:'CPU+memory'))" \
    '(-u --uuid)'{-u,--uuid}"[Show partition UUIDs]" \
    "(- *)"{-U,--update}"[Auto-update inxi]" \
    "(- *)"{-V,--version}"[Show version of inxi]" \
    '(-v --verbosity)'{-v+,--verbosity=}"[Script verbosity levels]:verbosity:_inxi_verbosity" \
    '(-w --weather)'{-w,--weather}"[Adds weather line]" \
    '(-W --weather-location)'{-W+,--weather-location=}"[Get weather/time for an alternate location]:weather location: " \
    "--weather-source=[Change weather data source]: :(1 2 3 4 5 6 7 8 9)" \
    "--weather-unit=[Set weather unit]:weather unit:((m\:metric i\:imperial mi\:'metric (imperial)' im\:'imperial (metric)'))" \
    "--host[Turns on hostname in System line]" \
    "--no-host[Turns off hostname in System line]" \
    {-z,--filter}"[Adds security filters]" \
    '(--za --filter-all)'{--za,--filter-all}'[Shortcut triggers, -z, --zl, --zu, --zv]' \
    '(--zl --filter-label)'{--zl,--filter-label}'[Filter out partition labels in -j, -o, -p, -P, -Sa]' \
    '(--zu --filter-uuid)'{--zu,--filter-uuid}'[Filter out partition UUIDs in -j, -o, -p, -P, -Sa]' \
    '(--zv --filter-vulnerabilities)'{--zv,--filter-vulnerabilities}'[Filters out Vulnerabilities report in -Ca]' \
    '(-Z --no-filter)'{-Z,--no-filter}"[Disable output filters]" \
    '(-c --color)'{-c+,--color=}"[Set color scheme]:color:(${colors[*]})" \
    "--indent=[Change default wide mode primary indentation width]:integer:(11 12 13 14 15 16 17 18 19 20)" \
    "--indents=[Change wrapped mode primary indentation width]:integer:(0 1 2 3 4 5 6 7 8 9 10)" \
    "--limit[Limit max IP addresses for -i]:limit (-1 removes the limit): " \
    '(--max-wrap --wrap-max)'{--max-wrap,--warp-max}='[Set maximum width where inxi autowraps line starters]' \
    "--output[Change data output type]:type:(json screen xml)" \
    "--output-file[Path to output file]:output file:->output_file" \
    "--partition-sort[Change default sort order of partition output]:order:_inxi_partition_sort" \
    '(-y --width)'{-y+,--width=}"[Set width override]:columns (80 minimum): " \
    '(-Y --height)'{-Y+,--height=}"[Output height control]:" \
    \*{-x,--extra}"[Extra data options]" \
    '(-a --admin)'{-a,--admin}"[Admin extra data options]:admin option:->admin" \
    "--alt[Bypass a downloader option]:downloader:_inxi_alt" \
    '--bt-tool[Force use of given tool for bluetooth report]' \
    '(--no-dig --dig)--dig[Overrides configuration item NO_DIG]' \
    "--display[Get display data out of X]:\:<integer>: " \
    "--dmidecode[Force use of dmidecode]" \
    "--downloader[Force inxi to use a specific downloader]:downloader:(curl fetch perl wget)" \
    '--force[Force use items]:' \
    '--hddtemp[Force use of hddtemp for disk temps]' \
    '(--no-html-wan --html-wan)--html-wan[Overrides configuration item NO_HTML_WAN]' \
    '(--dig --no-dig)--no-dig[Skip dig for WAN IP checks]' \
    '--no-doas[Skip internal program use of doas features]' \
    '(--no-html-wan --html-wan)--no-html-wan[Skip HTML IP sources for WAN IP checks]' \
    "--no-ssl[Skip SSL certificate checks]" \
    "--no-sudo[Skip internal program use of sudo features]" \
    '--rpm[Force use of disabled package manager counts for packages feature with -rx/-Ix ]' \
    '--sensors-default[Removes configuration item SENSORS_USE and SENSORS_EXCLUDE]' \
    '--sensors-exclude[Exclude supplied sensor array for -s output]' \
    '--sensors-use[Use only supplied sensor array for -s output]' \
    "--sleep[Change CPU sleep time]:sleep time (usually in decimals): " \
    "--tty[Forces internal IRC flag to off]" \
    "--usb-sys[Forces the USB data generator to use /sys as data source]" \
    "--usb-tool[Forces the USB data generator to use lsusb as data source]" \
    "--wan-ip-url[Force -i to use supplied URL as WAN ip source]:URL: " \
    "--wm[Force System item wm to use wmctrl as data source]" \
    "--dbg[Debug downloader failures]:level:(1)" \
    "--debug[On screen debugger output]:level:(1 2 3 10 11 20 21 22)" \
    "--debug-proc[Force debugger to parse /proc directory data when run as root]" \
    "--debug-proc-print[Use this to locate file that /proc debugger hangs on]" \
    "--debug-no-exit[Skip exit on error when running debugger]" \
    "--debug-no-proc[Skip /proc debugging in case of a hang]" \
    "--debug-no-sys[Skip /sys debugging in case of a hang]" \
    "--debug-sys[Force PowerPC debugger parsing of /sys as sudo/root]" \
    "--debug-proc[Force debugger to parse /proc directory data when run as root]" \
    "--debug-sys-print[Use this to locate file that /sys debugger hangs on]" \
    "--ftp[Set alternate ftp upload location]:FTP URL: " \
      && ret=0

  case "$state" in
    (admin)
      _arguments -s \
        '-C[Adds CPU family, model-id, and stepping]' \
        '-d[Adds logical and physical block size in bytes]' \
        {-p,-P}'[Adds raw partition / filesystem block size]' \
        && ret=0
      ;;

    (output_file)
      _alternative \
        "outputs:output file:((print\:'Print to stdout'))" \
        "outputs:output file:_files"
      ;;
  esac

  return ret
}

(( $+functions[_inxi_partition_sort] )) ||
_inxi_partition_sort() {
  local -a orders=(
    'dev-base:/dev partition identifier'
    'fs:partition filesystem'
    'id:mount point of partition (default)'
    'label:label of partition'
    'percent-used:percentage of partition size used'
    'size:KiB size of partition'
    'uuid:UUID of the partition'
    'used:KiB used of partition'
  )

  _describe 'sort order' orders
}

(( $+functions[_inxi_alt] )) ||
_inxi_alt() {
  local -a downloaders=(
    '40:Perl'
    '41:Curl'
    '42:Fetch'
    '43:Wget'
    '44:Curl, Fetch, and Wget'
  )

  _describe 'downloader' downloaders
}

(( $+functions[_inxi_verbosity] )) ||
_inxi_verbosity() {
  local -a levels=(
    '0:Short output, same as: inxi'
    '1:Basic verbose, -S + basic CPU'
    '2:Adds -N, -M, -B, same as: inxi -b'
    '3:Adds -C, -n, -x'
    '4:Adds -P, -D'
    '5:Adds -A, -m, -s, -l, -u'
    '6:Adds -p, -o, -d, --usb, -xx'
    '7:Adds -i, -xxx'
    '8:Adds -r, --slots, -tcm, --admin'
  )

  _describe 'script verbosity level' levels
}

_inxi "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
