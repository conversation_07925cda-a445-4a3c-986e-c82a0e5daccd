# ------------------------------------------------------------------------------
#          FILE: emotty.plugin.zsh
#   DESCRIPTION: Return an emoji for the current $TTY number.
#        AUTHOR: <PERSON> (afh[at]surryhill.net)
#       VERSION: 1.0.0
#       DEPENDS: emoji plugin
#
# There are different sets of emoji characters available, to choose a different
# set export emotty_set to the name of the set you would like to use, e.g.:
# % export emotty_set=nature
# ------------------------------------------------------------------------------

# Handle $0 according to the standard:
# https://zdharma-continuum.github.io/Zsh-100-Commits-Club/Zsh-Plugin-Standard.html
0="${${ZERO:-${0:#$ZSH_ARGZERO}}:-${(%):-%N}}"
0="${${(M)0:#/*}:-$PWD/$0}"

typeset -gAH _emotty_sets
local _emotty_plugin_dir="${0:h}"
source "$_emotty_plugin_dir/emotty_stellar_set.zsh"
source "$_emotty_plugin_dir/emotty_floral_set.zsh"
source "$_emotty_plugin_dir/emotty_zodiac_set.zsh"
source "$_emotty_plugin_dir/emotty_nature_set.zsh"
source "$_emotty_plugin_dir/emotty_emoji_set.zsh"
source "$_emotty_plugin_dir/emotty_love_set.zsh"
unset _emotty_plugin_dir

emotty_default_set=emoji

function emotty() {
  # Use emotty set defined by user, fallback to default
  local emotty=${_emotty_sets[${emotty_set:-$emotty_default_set}]}

  # Parse tty number via prompt expansion. %l equals:
  # - N      if tty = /dev/ttyN
  # - pts/N  if tty = /dev/pts/N
  local tty=${${(%):-%l}##pts/}
  # Normalize it to an emotty set index
  (( tty = (tty % ${#${=emotty}}) + 1 ))

  local character_name=${${=emotty}[tty]}
  echo "${emoji[${character_name}]}${emoji2[emoji_style]}"
}

function display_emotty() {
  local name=${1:-$emotty_set}
  echo $name
  for i in ${=_emotty_sets[$name]}; do
    printf "${emoji[$i]}${emoji2[emoji_style]}  "
  done
  print
  for i in ${=_emotty_sets[$name]}; do
    print "${emoji[$i]}${emoji2[emoji_style]}  = $i"
  done
}
