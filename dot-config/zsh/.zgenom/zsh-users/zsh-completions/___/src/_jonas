#compdef jonas
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for JOnAS 5.2 (https://jonas.ow2.org/).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * <PERSON> <<EMAIL>>
#
# ------------------------------------------------------------------------------


typeset -A opt_args
local context state line curcontext="$curcontext" ret=1

_arguments -C \
  '1:cmd:->cmds' \
  '*::arg:->args' \
&& ret=0

case "$state" in
  (cmds)
    local commands; commands=(
      'version:show version information'
      'check:check that the JOnAS environment is correctly set'
      'start:start a server instance'
      'stop:stop a server instance'
      'admin:administrate a server instance'
    )
    _describe -t commands 'command' commands && ret=0
  ;;
  (args)
    curcontext="${curcontext%:*:*}:jonas-cmd-$words[1]:"
    case $words[1] in
      (version|check)
        _message 'no more arguments' && ret=0
      ;;
      (start)
        _arguments \
          '-standby[start a minimal JOnAS server with only mandatory services]' \
          '(-bg)-fg[start the server in foreground mode]' \
          '(-fg)-bg[start the server in background mode]' \
          '-win[start the server in a new window]' \
          '(-bg)-tui[start the Apache Felix TUI (force foreground mode)]' \
          '-gui[start the Apache Felix GUI]' \
          '-dev[start a JOnAS server by using bundles present in the default maven repository instead of bundles under $JONAS_ROOT/lib/bundles]' \
          '-clean[clean the Apache Felix cache before starting a JOnAS server]' \
          '-n[set the server name, must be unique in the domain (default: jonas)]:name' \
          '-target[start another server or cluster (group of servers) in the domain]:server' \
          '-Ddomain.name=[set the name of the management domain to which the server belongs]:domain' \
        && ret=0
      ;;
      (stop)
        _arguments \
          '-standby[stop all services except the mandatory ones]' \
          '-n[set the name of the server to stop (default: jonas)]:name' \
          '-target[stop another server or cluster (group of servers) in the domain]:server' \
          '-Ddomain.name=[set the name of the management domain to which the server belongs]:domain' \
        && ret=0
      ;;
      (admin)
        _arguments \
          '(- : *)-?[print the help message]' \
          '-win[administer the server in a new window]' \
          '-n[set the name of the server to administer (default: jonas)]:name' \
          '-username[set the username when authentication is required]: :_users' \
          '-password[set the password when authentication is required]:password' \
          '-registry[set the registry URL]: :_urls' \
          '-protocol[set the protocol name]:protocol:((jrmp\:JRE\ implementation\ of\ RMI\ on\ the\ JRMP\ protocol\ \(default\) iiop\:JacORB\ implementation\ of\ RMI\ over\ the\ IIOP\ protocol irmi\:Oracle\ JRE\ independent\ implementation\ of\ RMI))' \
          '-a[deploy an application from a given filepath on the current server, or on another target in the domain if the current server is a master]:Java application archive:_files -g "*.(j|w|r|e)ar"' \
          '-r[undeploy a previously deployed application from the current server or from the specified target if the current server is a master]:Java application archive:_files -g "*.(j|w|r|e)ar"' \
          '-gc[run the garbage collector on the current JOnAS server]' \
          '-passivate[passivate all entity bean instances]' \
          '-e[list the properties of the current JOnAS server]' \
          '-j[list the registered JNDI names, as seen by the current JOnAS server]' \
          '-l[list the beans currently loaded by the current JOnAS server]' \
          '-synch[synchronize the entity bean instances on the current JOnAS server]' \
          '-debug[set the logging level for the given topic to DEBUG]:topic' \
          '-tt[change the default timeout for transactions]:timeout (seconds)' \
          '-ping[wait until the JOnAS server is available]' \
          '-timeout[maximum time to wait when -ping is used]:timeout (seconds)' \
        && ret=0
      ;;
    esac
  ;;
esac

return ret

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
