version: 2
jobs:
  build:
    machine: true
    steps:
      - checkout
      - run:
          name:
          command: |
            sudo apt-get -y update
            sudo apt-get -y install shellcheck git

      - run: git submodule sync
      - run: git submodule update --init
      - run:
          name: tests
          command: |
            export TERM=dumb && ./test/bats/bin/bats test
            shellcheck *.sh
