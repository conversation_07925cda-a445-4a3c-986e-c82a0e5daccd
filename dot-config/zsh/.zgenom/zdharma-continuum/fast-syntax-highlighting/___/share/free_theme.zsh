: ${FAST_HIGHLIGHT_STYLES[freedefault]:=none}
: ${FAST_HIGHLIGHT_STYLES[freeunknown-token]:=fg=red,bold}
: ${FAST_HIGHLIGHT_STYLES[freereserved-word]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freealias]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freesuffix-alias]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freebuiltin]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freefunction]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freecommand]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freeprecommand]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freecommandseparator]:=none}
: ${FAST_HIGHLIGHT_STYLES[freehashed-command]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freepath]:=fg=166}
: ${FAST_HIGHLIGHT_STYLES[freepath_pathseparator]:=}
: ${FAST_HIGHLIGHT_STYLES[freeglobbing]:=fg=112}
: ${FAST_HIGHLIGHT_STYLES[freeglobbing-ext]:=fg=118}
: ${FAST_HIGHLIGHT_STYLES[freehistory-expansion]:=fg=blue,bold}
: ${FAST_HIGHLIGHT_STYLES[freesingle-hyphen-option]:=fg=110}
: ${FAST_HIGHLIGHT_STYLES[freedouble-hyphen-option]:=fg=110}
: ${FAST_HIGHLIGHT_STYLES[freeback-quoted-argument]:=none}
: ${FAST_HIGHLIGHT_STYLES[freesingle-quoted-argument]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freedouble-quoted-argument]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freedollar-quoted-argument]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freeback-or-dollar-double-quoted-argument]:=fg=110}
: ${FAST_HIGHLIGHT_STYLES[freeback-dollar-quoted-argument]:=fg=110}
: ${FAST_HIGHLIGHT_STYLES[freeassign]:=none}
: ${FAST_HIGHLIGHT_STYLES[freeredirection]:=none}
: ${FAST_HIGHLIGHT_STYLES[freecomment]:=fg=black,bold}
: ${FAST_HIGHLIGHT_STYLES[freevariable]:=none}
: ${FAST_HIGHLIGHT_STYLES[freemathvar]:=fg=blue,bold}
: ${FAST_HIGHLIGHT_STYLES[freemathnum]:=fg=166}
: ${FAST_HIGHLIGHT_STYLES[freematherr]:=fg=red}
: ${FAST_HIGHLIGHT_STYLES[freeassign-array-bracket]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freefor-loop-variable]:=none}
: ${FAST_HIGHLIGHT_STYLES[freefor-loop-number]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freefor-loop-operator]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freefor-loop-separator]:=fg=109}
: ${FAST_HIGHLIGHT_STYLES[freeexec-descriptor]:=fg=yellow,bold}
: ${FAST_HIGHLIGHT_STYLES[freehere-string-tri]:=fg=yellow}
: ${FAST_HIGHLIGHT_STYLES[freehere-string-text]:=bg=19}
: ${FAST_HIGHLIGHT_STYLES[freehere-string-var]:=fg=110,bg=19}
: ${FAST_HIGHLIGHT_STYLES[freesecondary]:=zdharma}
: ${FAST_HIGHLIGHT_STYLES[freecase-input]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freecase-parentheses]:=fg=116}
: ${FAST_HIGHLIGHT_STYLES[freecase-condition]:=bg=19}
: ${FAST_HIGHLIGHT_STYLES[freecorrect-subtle]:=bg=55}
: ${FAST_HIGHLIGHT_STYLES[freeincorrect-subtle]:=bg=52}
: ${FAST_HIGHLIGHT_STYLES[freesubtle-separator]:=none}
: ${FAST_HIGHLIGHT_STYLES[freesubtle-bg]:=bg=18}
: ${FAST_HIGHLIGHT_STYLES[freepath-to-dir]:=fg=166,underline}
: ${FAST_HIGHLIGHT_STYLES[freepaired-bracket]:=bg=blue}
: ${FAST_HIGHLIGHT_STYLES[freebracket-level-1]:=fg=130}
: ${FAST_HIGHLIGHT_STYLES[freebracket-level-2]:=fg=70}
: ${FAST_HIGHLIGHT_STYLES[freebracket-level-3]:=fg=69}
: ${FAST_HIGHLIGHT_STYLES[freeglobal-alias]:=bg=19}
: ${FAST_HIGHLIGHT_STYLES[freesubcommand]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freesingle-sq-bracket]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freedouble-sq-bracket]:=fg=180}
: ${FAST_HIGHLIGHT_STYLES[freedouble-paren]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freeoptarg-string]:=fg=150}
: ${FAST_HIGHLIGHT_STYLES[freeoptarg-number]:=fg=166}
: ${FAST_HIGHLIGHT_STYLES[freerecursive-base]:=fg=183}
