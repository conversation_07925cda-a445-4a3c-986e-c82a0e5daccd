{"name": "bats", "version": "1.6.0", "description": "Bash Automated Testing System", "homepage": "https://github.com/bats-core/bats-core#readme", "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://sstephenson.us/)", "repository": "github:bats-core/bats-core", "bugs": "https://github.com/bats-core/bats-core/issues", "files": ["bin", "libexec", "lib", "man", "install.sh", "uninstall.sh"], "directories": {"bin": "bin", "doc": "docs", "man": "man", "test": "test"}, "scripts": {"test": "bin/bats test"}, "keywords": ["bats", "bash", "shell", "test", "unit"]}