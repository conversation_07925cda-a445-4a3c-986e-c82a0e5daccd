#compdef showoff
# ------------------------------------------------------------------------------
# Copyright (c) 2016 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for Showoff 0.20.3 (https://github.com/puppetlabs/showoff).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Bruno Michel (https://github.com/nono)
#  * Shohei Yoshida (https://github.com/nono)
#
# ------------------------------------------------------------------------------

_showoff_subcommands() {
  local -a commands=(
    "add:Add a new slide at the end in a given dir"
    "new:Add a new slide at the end in a given dir"
    "create:Create new showoff presentation"
    "init:Create new showoff presentation"
    "github:Puts your showoff presentation into a gh-pages branch"
    "help:Shows a list of commands or help for one command"
    "heroku:Setup your presentation to serve on Heroku"
    "info:Display information about a Showoff presentation"
    "pdf:Generate PDF version of presentation"
    "serve:Serves the showoff presentation in the specified (or current) directory"
    "skeleton:Build a showoff presentation from a showoff.json outline"
    "static:Generate static version of presentation"
    "validate:Validate the consistency of your presentation"
  )

  _describe -t commands 'showoff command' commands
}

_showoff_help() {
  local -a commands=(${(@f)"$(showoff help -c 2>/dev/null)"})
  _values 'commands' $commands
}

_showoff() {
  local curcontext="$curcontext" state line ret=1

  _arguments -C \
    '--debug[Show application backtraces on crash]' \
    '--dev[Use the next-gen development version of Showoff]' \
    '(- *)--help[Show help message]' \
    '(- *)--version[Display the program version]' \
    '1: :_showoff_subcommands' \
    '*:: :->args' \
      && ret=0

  case $state in
    (args)
      case $line[1] in
        (add|new)
          _arguments \
            '(-d --dir)'{-d,--dir}='[Slide dir (where to put a new slide file)]:directory:_files -/' \
            '(-n --name)'{-n,--name}='[Slide name (name of the new slide file)]:basename' \
            {-s,--source}='[Include code from the given file as the slide body]:file:_files' \
            '(-t --style --type)'{-t,--style,--type}='[Slide Type/Style (default: title)]:style' \
            '(-u --nonumber)'{-u,--nonumber}"[Don't number the slide, use the given name verbatim]" \
            '1:title' && ret=0
          ;;
        (create|init)
          _arguments \
            '(-d --slidedir)'{-d,--slidedir}='[Sample slide directory name (default: one)]:arg' \
            '(-n --nosamples)'{-n,--nosamples}="[Don't create sample slides]" \
            '1:dir_name' && ret=0
          ;;
        (help)
          _arguments \
            '-c[List commands one per line, to assist with shell completion]' \
            '1: :_showoff_help' && ret=0
          ;;
        (heroku)
          _arguments \
            '(-f --force)'{-f,--force}'[force overwrite of existing Gemfile, .gems and config.ru files if they exist]' \
            '(-p --password)'{-p,--password}='[add password protection to your heroku site(default: none)]' \
            '1:heroku_name' && ret=0
          ;;
        (info)
          _arguments \
            '(-f --file)'{-f,--file}='[alternate json filename]: :_files -g "*.json"' \
            '(-j --json)'{-j,--json}'[render output as json]' \
            && ret=0
          ;;
        (pdf|static)
          _arguments \
            '(-f --file --pres_file)'{-f,--file,--pres_file}='[JSON file used to describe presentation(default: showoff.json)]: :_files -g "*.json"' \
            '(-l --lang --language --locale)'{-l,--lang,--language,--locale}'[Language code to generate(default: none)]' \
            '1:name' \
            && ret=0
          ;;
        (serve)
          _arguments \
            '(-S --standalone)'{-S,--standalone}'[Run in standalone mode with no audience interaction]' \
            '(-f --file --pres_file)'{-f,--file,--pres_file}='[JSON file used to describe presentation(default: showoff.json)]: :_files -g "*.json"' \
            '--git_branch=[Branch of git repository to use(default: none)]:branch' \
            '--git_path=[Path of the presentation within the git repository(default: none)]:path' \
            '(-h --host)'{-h,--host}='[Host or IP to run on(default: 0.0.0.0)]' \
            '--nocache[Disable content caching]'\
            '--nosleep[Prevent the computer from sleeping during your presentation]' \
            '(-p --port)'{-p,--port}='[Port on which to run(default: 9090)]' \
            '(-r --review)'{-r,--review}'[Enable code review]'\
            '(-s --ssl)'{-s,--ssl}'[Run via HTTPS]' \
            '--ssl_certificate=[Path to SSL certificate]: :_files' \
            '--ssl_private_key=[Path to SSL private key]: :_files' \
            '(-u --url --git_url)'{-u,--url,--git_url}='[GIT URL to a repository containing the presentation]:url' \
            '(-v --verbose)'{-v,--verbose}'[Show verbose messaging]' \
            '(-x --execute --executecode)'{-x,--execute,--executecode}'[Enable remote code execution]' \
            '1: :_files -/' && ret=0
          ;;
        (skeleton|validate)
          _arguments \
            '(-f --file)'{-f,--file}'=[alternate json filename(default: none)]: :_files -g "*.json"' \
            && ret=0
          ;;
        *)
          (( ret )) && _message 'no more arguments'
          ;;
      esac
      ;;
  esac

  return ret
}

_showoff "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
