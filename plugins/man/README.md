# Man plugin

This plugin adds a shortcut to insert man before the previous command.

To use it, add `man` to the plugins array in your zshrc file:

```zsh
plugins=(... man)
```
# Keyboard Shortcuts
| Shortcut                          | Description                                                            |
|-----------------------------------|------------------------------------------------------------------------|
| <kbd>Esc</kbd> + man              | add man before the previous command to see the manual for this command |
