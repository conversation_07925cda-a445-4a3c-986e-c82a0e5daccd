#compdef setcap
# ------------------------------------------------------------------------------
# Copyright (c) 2011 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
# * Redistributions of source code must retain the above copyright
# notice, this list of conditions and the following disclaimer.
# * Redistributions in binary form must reproduce the above copyright
# notice, this list of conditions and the following disclaimer in the
# documentation and/or other materials provided with the distribution.
# * Neither the name of the zsh-users nor the
# names of its contributors may be used to endorse or promote products
# derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# ------------------------------------------------------------------------------
# Description
# ------------------------------------------------------------------------------
#  Completion script for libcap's setcap:
#  - https://people.redhat.com/sgrubb/libcap-ng/
#
# ZSH provides the `zsh/cap` module that does not work on most modern systems,
# in lieu of this I have written this zsh-completion modules.
#
# Written by
#  - Zephyr Pellerin (https://github.com/zv)
# ------------------------------------------------------------------------------

_setcap() {
  local curcontext=$curcontext state line expl ret=1

  _arguments -C -s \
    '-r[remove capability from file]' \
    '--license[display the license info]' \
    '-f[force setting even when the capability is invalid]' \
    '-h[show help message and exit]' \
    '-v[verify that the specified capabilities are currently associated with the file]' \
    '-n[set the file capability for use only in a user namespace with this root user ID owner]:rootuid' \
    '-q[make the program less verbose in its output]' \
    '1:capability:->capability' \
    '*:file:_files' \
    && ret=0

  case "$state" in
    (capability)
      if compset -P '*?[=+-]'; then
        local -a operators=("e:effective" "i:inheritable" "p:permitted")
        _describe -t operators "operator" operators && ret=0
      else
        _setcap_capabilities && ret=0
      fi
      ;;
  esac

  return 0
}

(( $+functions[_setcap_capabilities] )) ||
_setcap_capabilities() {
  local ret=1
  local -a capabilities=(
    'cap_audit_control:Enable and disable kernel auditing'
    'cap_audit_read:Allow reading the audit log'
    'cap_audit_write:Write records to kernel auditing log'
    'cap_block_suspend:Employ features that can block system suspend'
    'cap_bpf:Employ privileged BPF operations'
    'cap_checkpoint_restore:Facilitate checkpoint/restore'
    'cap_chown:Make arbitrary changes to file UIDs and GIDs'
    'cap_dac_override:Bypass file read, write, and execute permission checks'
    'cap_dac_read_search:Bypass file read permission checks'
    'cap_fowner:Bypass filesystem UID checks, set extended attrs'
    "cap_fsetid:Don't clear set-user-ID and set-group-ID permission bits when a file is modified"
    'cap_ipc_lock:Lock memory'
    'cap_ipc_owner:Bypass checks on SySV IPC object operations'
    'cap_kill:Bypass permission checks for sending signals'
    'cap_lease:Establish leases on arbitrary files'
    'cap_linux_immutable:Set immutability or append only'
    'cap_mac_admin:Override Mandatory Access Control'
    'cap_mac_override:Allow MAC configuration or state changes'
    'cap_mknod:Create special files using mknod(2)'
    'cap_net_admin:Perform various network-related operations'
    'cap_net_bind_service:Bind a socket to a privileged ports'
    'cap_net_broadcast:Make socket broadcasts and listen to multicast'
    'cap_net_raw:Use raw sockets'
    'cap_perfmon:Employ various performance-monitoring mechanisms'
    'cap_setgid:Manipulate process GIDs'
    'cap_setfcap:Set file capabilities'
    "cap_setpcap:Grant or remove any capability in the caller's permitted capability set to or from any other process"
    'cap_setuid:Manipulate or forge process UIDs'
    'cap_sys_admin:Perform numerous administrative tasks'
    'cap_sys_boot:Reboot'
    'cap_sys_chroot:Use chroot'
    'cap_sys_module:Load kernel module'
    'cap_sys_nice:Nice or renice processes'
    'cap_sys_pacct:Use acct(2)'
    'cap_sys_ptrace:Inspect processes with ptrace or use process_vm_writev'
    'cap_sys_rawio:Numerous device IO functions, including performing raw IO and access x86 MSRs'
    'cap_sys_resource:Set numerous resource limits'
    'cap_sys_time:Set system clock'
    'cap_sys_tty_config:Use vhangup(2)'
    'cap_syslog:Perform privileged syslog(2) operations'
    'cap_wake_alarm:Trigger something that will wake up the system'
  )

  _values -s , capability $capabilities && ret=0
  return $ret

}

_setcap "$@"

# Local variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# ex: sw=2 ts=2 et filetype=sh
