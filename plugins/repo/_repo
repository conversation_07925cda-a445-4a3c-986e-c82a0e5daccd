#compdef repo

__git_apply_whitespace_strategies ()
{
  declare -a strategies

  strategies=(
    'nowarn:turn off the trailing-whitespace warning'
    'warn:output trailing-whitespace warning, but apply patch'
    'fix:output trailing-whitespace warning and strip trailing whitespace'
    'error:output trailing-whitespace warning and refuse to apply patch'
    'error-all:same as "error", but output warnings for all files')

  _describe -t strategies 'trailing-whitespace resolution strategy' strategies $*
}


_repo()
{
  local context state state_descr line curcontext="$curcontext"
  typeset -A opt_args

  local ret=1

  _arguments -C \
    '(- 1 *)--help[show usage]'\
    '1:command:->command'\
    '*::args:->args' && ret=0

  case $state in
    (command)
      repo list  2> /dev/null > /dev/null
      if [[ $? == 0 ]]; then
        local commands;
        commands=(
          'abandon:Permanently abandon a development branch'
          'branch:View current topic branches'
          'branches:View current topic branches'
          'checkout:Checkout a branch for development'
          'cherry-pick:Cherry-pick a change.'
          'diff:Show changes between commit and working tree'
          'download:Download and checkout a change'
          'forall:execute command on several project'
          'grep:Print lines matching a pattern'
          'help:Display detailed help on a command'
          'init:Initialize repo in the current directory'
          'list:List projects and their associated directories'
          'manifest:Manifest inspection utility'
          'overview:Display overview of unmerged project branches'
          'prune:Prune (delete) already merged topics'
          'rebase:Rebase local branches on upstream branch'
          'selfupdate:Update repo to the latest version'
          'smartsync:Update working tree to the latest known good revision'
          'stage:Stage file(s) for commit'
          'start:Start a new branch for development'
          'status:Show the working tree status'
          'sync:Update working tree to the latest revision'
          'upload:Upload changes for code review'
          'version:Display the version of repo'
        )
        _describe -t commands 'command' commands && ret=0
      else
        local commands;
        commands=(
          'init:Install repo in the current working directory'
          'help:Display detailed help on a command'
        )
        _describe -t commands 'command' commands && ret=0
      fi
      ;;
    (args)
      case $words[1] in
        (branch | branches)
          # TODO : list available projects and add them in list to feed compadd with
          _arguments  : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     ': :__repo_projects' \
                      && ret=0
         ;;
        (abandon)
          # TODO : list available projects and add them in list to feed compadd with
          _arguments  : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     ':branch name:__repo_branch' \
                     ': :__repo_projects'\
                      && ret=0
          ;;
        (checkout)
          # TODO : list available projects and add them in list to feed compadd with
          _arguments  : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     ':branch name:__repo_branch' \
                     ': :__repo_projects'\
                     && ret=0
          ;;
        (init)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(-q --quiet)"{-q,--quiet}"[be quiet]" \
                     "(-u --manifest-url)"{-u,--manifest-url=}"[manifest repository location]":url:__repo_url_prompt \
                     "(-b --manifest-branch)"{-b,--manifest-branch=}"[manifest branch or revision]":branch:__repo_branch\
                     "(-m --manifest-name)"{-m,--manifest-name=}"[initial manifest file]":manifest_name:__repo_manifest_name\
                     "(--mirror)--mirror[mirror the forrest]"\
                     "(--reference)--reference=[location of mirror directory]":dir:_dirs\
                     "(--depth)--depth=[create a shallow clone with given depth; see git clone]":depth:__repo_depth_prompt\
                     "(-g --group=)"{-g,--group=}"[restrict manifest projects to ones with a specified group]":group:_group\
                     "(-p --platform=)"{-p,--platform=}"[restrict manifest projects to ones with a specified platform group(auto|all|none|linux|darwin|...)]":platform:"(auto all none linux darwin)"\
                     "(--repo-url)--repo-url=[repo repository location]":url:__repo_url_prompt\
                     "(--repo-branch)--repo-branch[repo branch or revision]":branch_or_rev:__repo__repo_branch_or_rev\
                     "(--no-repo-verify)--no-repo-verify[do not verify repo source code]"\
                     "(--config-name)--config-name[Always prompt for name/e-mail]"\
                     && ret=0
          ;;
        (start)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(--all)--all[begin branch in all projects]"\
                     "(-r --rev --revision)"{-r,--rev,--revision=}"[point branch at this revision instead of upstream]":branch_or_rev:__repo__repo_branch_or_rev\
                     "(--head)--head[abbreviation for --rev HEAD]"\
                    ':branch name:__repo_new__repo_branch_name' \
                    ':projects:__repo_projects_or_all' \
                    && ret=0
          ;;
        (rebase)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(-i --interactive)"{-i,--interactive}"[interactive rebase (single project only)]: :__repo_projects" \
                     "(-f --force-rebase)"{-f,--force-rebase}"[Pass --force-rebase to git rebase]" \
                     "(--no-ff)--no-ff=[Pass --no-ff to git rebase]"\
                     "(-q --quiet)"{-q,--quiet}"[Pass --quiet to git rebase]" \
                     "(--autosquash)--no-ff[Pass --autosquash to git rebase]"\
                     "(--whitespace=)--whitespace=[Pass --whitespace to git rebase]: :__git_apply_whitespace_strategies"\
                     "(--auto-stash)--auto-stash[Stash local modifications before starting]"\
                     && ret=0
          ;;
        (checkout)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                    ':branch name:__git_branch_names' \
                    ':projects:__repo_projects' \
                    && ret=0
          ;;
        (list)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                    && ret=0
          ;;
        (stage)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(-i --interactive)"{-i,--interactive}"[use interactive staging]" \
                    ':projects:__repo_projects' \
                    && ret=0
          ;;
        (status)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(-j --jobs)"{-j,--jobs}"[number of projects to check simultaneously]" \
                    ':projects:__repo_projects' \
                    && ret=0
          ;;
        (sync)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(--no-force-broken)--no-force-broken[stop sync if a project fails to sync (probably because of permissions)]" \
                     "(-l --local-only)"{-l,--local-only}"[only update working tree, don't fetch]" \
                     "(-n --network-only)"{-n,--network-branch}"[fetch only, don't update working tree]" \
                     "(-d --detach)"{-d,--detach}"[detach projects back to manifest revision]" \
                     "(-c --current-branch)"{-c,--current-branch}"[fetch only current branch from server]" \
                     "(-q --quiet)"{-q,--quiet}"[be more quiet]" \
                     "(-j --jobs=)"{-j,--jobs=}"[projects to fetch simultaneously (default 1) (limited to 5)]:projects to fetch simultaneously (default 1) (limited to 5)" \
                     "(-m --manifest-name=)"{-m,--manifest-name=}"[temporary manifest to use for this sync]:manifest xml file:_files -g *.xml" \
                     "(--no-clone-bundle)--no-clone-bundle[disable use of /clone.bundle on HTTP/HTTPS]" \
                     "(-s --smart-sync)"{-s,--smart-sync=}"[smart sync using manifest from a known tag]:tag:" \
                     '(--no-repo-verify)--no-repo-verify[do not verify repo source code]' \
                     ': :__repo_projects' \
                     && ret=0
          ;;
        (upload)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(-t)-t[Send local branch name to Gerrit Code Review]" \
                     "(--re= --reviewers=)"{--re=,--reviewers=}"[Request reviews from these people]:Request reviews from these people:" \
                     "(--cc=)--cc=[Also send email to these email addresses.]:email addresses:_email_addresses" \
                     "(--br=)--br=[Branch to upload.]:branch:__repo_branch" \
                     "(--cbr --current-branch)"{--cbr,--current-branch}"[Upload current git branch]" \
                     "(-d --draft)"{-d,--draft}"[If specified, upload as a draft.]" \
                     "(--verify --no-verify)--no-verify[Do not run the upload hook.]" \
                     '(--verify --no-verify)--verify[Run the upload hook without prompting]' \
                     ': :__repo_projects' \
                     && ret=0
          ;;
        (forall)
          _arguments : \
                     "(-h --help)"{-h,--help}"[Show help]" \
                     "(-v --verbose)"{-v,--verbose}"[Show command error messages]" \
                     '(-p)-p[Show project headers before output]' \
                     ': :__repo_projects_mandatory' \
                     "(-c --command -h --help -v --verbose -p)"{-c,--command}"[Command (and arguments) to execute]" \
                     && ret=0
          ;;
        *)
          ret=0
      esac
      ;;
  esac

  return $ret
}

__repo_reviewers()
{
 # _message -e url 'reviewers'
}

__repo_url_prompt()
{
  _message -e url 'url'
}

__repo_manifest_name()
{
  _message -e manifest_name 'manifest name'
}

_group()
{
  _message -e group 'group'
}

__repo_branch()
{
  #_message -e branch 'Repo branch'
  branches=($(repo branches| cut -c4- | grep '|' | cut -d' ' -f1))
  _describe -t branches 'Select repo branch' branches
}

__repo__repo_branch_or_rev()
{
  _message -e branch_or_rev 'repo branch or revision'
}

__repo_depth_prompt()
{
  _message -e depth 'depth'
}

__repo_projects()
{
  _message -e depth 'Optional option : <projects>...'
  projects=($(repo list | cut -d' ' -f1))
  _describe -t projects 'Select projects (keep empty for selecting all projects)' projects
}

__repo_projects_mandatory()
{
  projects=($(repo list | cut -d' ' -f1))
  #_describe -t projects 'Select projects to apply commands' projects
  _values -s ' ' "Select projects to apply commands" $projects
}

__repo_new__repo_branch_name()
{
  branches=($(repo branches| cut -c4- | grep '|' | cut -d' ' -f1))
  _describe "" branches
  _message -e "branch name" 'Enter new branch name or select an existing repo branch'
}

__repo_projects_or_all()
{
  #_message -e depth '[--all | <project>...]'

  projects=(--all $(repo list | cut -d' ' -f1))
  _describe -t projects 'Select projects or --all' projects
  _describe -t --all 'All projects'
}

_repo "$@"
return $?
