# License Acceptance

- [ ] This repository is Apache version 2.0 licensed (some scripts may have alternate licensing inline in their code) and by making this PR, I am contributing my changes to the repository under the terms of the Apache 2 license.

<!--- Provide a general summary of your changes in the Title above -->

## Description

<!--- Describe your changes in detail -->

## Type of changes

<!--- What types of changes does your submission introduce? Put an `x` in all the boxes that apply: [x] -->

- [ ] A helper script
- [ ] A link to an external resource like a blog post or video
- [ ] Text cleanups/updates
- [ ] Test updates
- [ ] Bug fix
- [ ] New feature
- [ ] Plugin list change

## Checklist

<!--- Go over all the following points, and put an `x` in all the boxes that apply. [x] -->
<!--- If you're unsure about any of these, don't hesitate to ask. I'm happy to help! -->

- [ ] I have read the **CONTRIBUTING** document.
- [ ] I have updated the readme if this PR changes/updates quickstart functionality.
- [ ] All new and existing tests pass.
- [ ] Any scripts added use `#!/usr/bin/env interpreter` instead of potentially platform-specific direct paths (`#!/bin/sh` is an allowed exception)
- [ ] Scripts are marked executable
- [ ] Scripts _do not_ have a language file extension unless they are meant to be sourced and not run standalone. No one should have to know if a script was written in bash, python, ruby or whatever. Not including file extensions makes it easier to rewrite the script in another language later without having to change every reference to the previous version.
- [ ] I have added a credit line to README.md for the script
- [ ] If there was no author credit in a script added in this PR, I have added one.
- [ ] I have confirmed that the link(s) in my PR are valid.
