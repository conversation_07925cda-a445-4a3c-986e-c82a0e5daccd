---
name: Check links in README

on:
  push:
    branches: [ '*' ]
  pull_request:
    branches: [ '*' ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        # Full git history is needed to get a proper list of changed files
        # within `mega-linter`
        fetch-depth: 0
    - uses: docker://dkhamsing/awesome_bot:latest
      with:
        args: /github/workspace/README.md --allow-ssl --allow 202,500,501,502,503,504,509,521 --allow-dupe --allow-redirect --request-delay 1
