# Fixed Post-Plugin Configuration
# This file is sourced after plugins are loaded
# Contains aliases, functions, options, key bindings, and tool integrations

# Set file type and editor options for this file
# vim: ft=zsh sw=4 ts=4 et nu rnu ai si

# Key bindings
bindkey "^[[3~" delete-char

# Add Composer global vendor/bin to PATH if Composer is available
if command -v composer >/dev/null 2>&1; then
    composer_home="$(composer config --global home 2>/dev/null)"
    if [[ -n "$composer_home" && -d "$composer_home/vendor/bin" ]]; then
        _path_prepend "$composer_home/vendor/bin"
    fi
    unset composer_home
fi

# Set application preferences based on available commands
if command -v nvim >/dev/null 2>&1; then
    export EDITOR="$(command -v nvim)"
fi

if command -v code >/dev/null 2>&1; then
    export VISUAL="$(command -v code)"
fi

# Platform-specific configurations
case "$(uname -s)" in
    Darwin)
        # macOS-specific settings
        defaults write -g NSWindowShouldDragOnGesture -bool true 2>/dev/null || true
        defaults write NSGlobalDomain AppleHighlightColor -string "0.615686 0.823529 0.454902" 2>/dev/null || true
        ;;
    Linux)
        # Linux-specific setup if needed
        ;;
    MINGW32_NT*|MINGW64_NT*)
        # Windows setup if needed
        ;;
esac

## Plugin Integrations

# zsh-abbr plugin (if available via zgenom)
if [[ -f "$ZGEN_DIR/olets/zsh-abbr/v6/zsh-abbr.plugin.zsh" ]]; then
    builtin source "$ZGEN_DIR/olets/zsh-abbr/v6/zsh-abbr.plugin.zsh"
fi

# Fast Syntax Highlighting theme
if command -v fast-theme >/dev/null 2>&1; then
    fast-theme q-jmnemonic 2>/dev/null || true
fi

# SSH key management
if [[ -f "${HOME}/.ssh/id_ed25519" ]] && command -v ssh-add >/dev/null 2>&1; then
    /usr/bin/ssh-add -q --apple-load-keychain --apple-use-keychain "${HOME}/.ssh/id_ed25519" 2>/dev/null || true
fi

# Starship prompt initialization
if command -v starship >/dev/null 2>&1; then
    eval "$(starship init zsh)"
fi

# Zoxide integration (smart cd replacement)
if command -v zoxide >/dev/null 2>&1; then
    eval "$(zoxide init zsh --hook pwd)"
fi

# Carapace completion system
if command -v carapace >/dev/null 2>&1; then
    export CARAPACE_BRIDGES='zsh,fish,bash,inshellisense'
    zstyle ':completion:*' format $'\e[2;37mCompleting %d\e[m'
    builtin source <(carapace _carapace)
fi

## Language and Tool Specific Configurations

# Go language setup
setup_go_environment() {
    # Set GOPATH if ~/go directory exists
    [[ -d "$HOME/go" ]] && export GOPATH="$HOME/go"

    # Find and set GOROOT
    local go_roots=(
        "/usr/local/opt/go/libexec"
        "/usr/local/opt/go"
        "/usr/local/go"
    )

    for goroot in "${go_roots[@]}"; do
        if [[ -d "$goroot" ]]; then
            export GOROOT="$goroot"
            break
        fi
    done

    # Add Go binaries to PATH
    [[ -n "$GOROOT" && -d "$GOROOT/bin" ]] && _path_prepend "$GOROOT/bin"
    [[ -n "$GOPATH" && -d "$GOPATH/bin" ]] && _path_prepend "$GOPATH/bin"
    [[ -d "$HOME/go/bin" ]] && _path_prepend "$HOME/go/bin"
}
setup_go_environment

# jj version control completion (if available)
if command -v jj >/dev/null 2>&1; then
    builtin source <(COMPLETE=zsh jj) 2>/dev/null || true
fi

# Laravel/PHP development - safely remove bob alias if it exists
# Note: Fixed the original unalias error by checking if alias exists first
if (( ${+aliases[bob]} )); then
    unalias bob
fi

# Rio terminal configuration
if command -v rio >/dev/null 2>&1; then
    export RIO_CONFIG="${XDG_CONFIG_HOME}/rio/config"
    export RIO_CACHE="${XDG_CACHE_HOME}/rio"

    # Install Rio terminfo if not present
    if ! infocmp rio &>/dev/null; then
        {
            tempfile=$(mktemp) &&
            curl -s -o "$tempfile" https://raw.githubusercontent.com/raphamorim/rio/main/misc/rio.terminfo &&
            sudo tic -xe rio "$tempfile" &&
            rm "$tempfile"
        } 2>/dev/null || true
    fi
fi

# SQLite configuration (macOS Homebrew)
if [[ "$(uname -s)" == "Darwin" ]] && [[ -d "/opt/homebrew/opt/sqlite" ]]; then
    # SQLite is keg-only in Homebrew to avoid conflicts with macOS sqlite
    export SQLITE_ROOT="/opt/homebrew/opt/sqlite"
    _path_prepend "$SQLITE_ROOT/bin"

    # Set library and include paths for compilation
    export LDFLAGS="-L$SQLITE_ROOT/lib ${LDFLAGS}"
    export CPPFLAGS="-I$SQLITE_ROOT/include ${CPPFLAGS}"
    export PKG_CONFIG_PATH="$SQLITE_ROOT/lib/pkgconfig:${PKG_CONFIG_PATH}"
fi

# Node.js version management (if nvm exists)
if [[ -d "$HOME/.nvm" ]]; then
    export NVM_DIR="$HOME/.nvm"
    # Load nvm lazily to improve startup time
    nvm() {
        unfunction nvm
        [[ -s "$NVM_DIR/nvm.sh" ]] && builtin source "$NVM_DIR/nvm.sh"
        [[ -s "$NVM_DIR/bash_completion" ]] && builtin source "$NVM_DIR/bash_completion"
        nvm "$@"
    }
fi

# Python development tools
if command -v python3 >/dev/null 2>&1; then
    # Add Python user base to PATH
    python_user_base="$(python3 -m site --user-base 2>/dev/null)/bin"
    [[ -d "$python_user_base" ]] && _path_prepend "$python_user_base"
    unset python_user_base
fi

# Rust/Cargo setup
if [[ -d "$HOME/.cargo/bin" ]]; then
    _path_prepend "$HOME/.cargo/bin"
fi

# Clean up functions used only during setup
unset -f setup_go_environment

# Performance optimization: avoid redundant PATH operations
# Export PATH only once at the end
export PATH
