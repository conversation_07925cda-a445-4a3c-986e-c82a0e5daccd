#compdef uuidparse
# ------------------------------------------------------------------------------
# Copyright (c) 2025 Github zsh-users - https://github.com/zsh-users
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for uuidparse 2.40.2 (https://github.com/util-linux/util-linux)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
# <AUTHOR> <EMAIL>
#
# ------------------------------------------------------------------------------

_uuidparse_types() {
  local -a types=(UUID VARIANT TYPE TIME)
  _values -s ',' types $types
}

_arguments \
  '(-J --json -n --noheadings -r --raw)'{-J,--json}'[use JSON output format]' \
  '(-J --json -n --noheadings)'{-n,--noheadings}'[do not print a header line]' \
  '(-o --output)'{-o,--output}'[specify which output columns to print]:type:_uuidparse_types' \
  '(-r --raw -J --json)'{-r,--raw}'[use the raw output format]' \
  '(- *)'{-h,--help}'[display help text and exit]' \
  '(- *)'{-V,--version}'[print version and exit]'

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
