#compdef http https=http
# ------------------------------------------------------------------------------
# Copyright (c) 2015 Github zsh-users - https://github.com/zsh-users
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#     * Neither the name of the zsh-users nor the
#       names of its contributors may be used to endorse or promote products
#       derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ZSH-USERS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
# ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for httpie 3.2.2  (https://httpie.io/)
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Akira Maeda <https://github.com/glidenote>
#  * Valodim <https://github.com/Valodim>
#  * Claus Klingberg <https://github.com/cjk>
#  * Shohei YOSHIDA <https://github.com/syohex>
#
# ------------------------------------------------------------------------------

_httpie_params() {
  local ret=1 expl

  # or a url
  if (( CURRENT <= NORMARG+1 )) && [[ $words[NORMARG] != *:* ]] ; then
    _httpie_urls && ret=0

    # regular param, if we already have a url
  elif (( CURRENT > NORMARG )); then

    # if the suffix is precisely : this is shorthand for a header
    if [[ -prefix ':' ]]; then
      PREFIX=
      SUFFIX=:
    fi

    # if we are in front of a : (possibly due to the PREFIX move before)
    if [[ -suffix ':' ]]; then
      # this is rather buggy with normal tab behavior :
      compstate[insert]=menu
      _wanted http_header expl 'HTTP Header' \
              compadd -s ':' -S '' -- Content-Type Cookie && return 0
    fi

    # ignore all prefix stuff
    compset -P '(#b)([^:@=]#)'
    local name=$match[1]

    if compset -P '='; then
      _message "$name data field value"
    elif compset -P '@'; then
      _files
    elif compset -P ':=@'; then
      _files
    elif compset -P ':='; then
      _message "$name raw json data"
    elif compset -P '=='; then
      _message "$name url parameter value"
    elif compset -P ':'; then
      _message "$name header content"
    else
      typeset -a ops
      ops=(
        '=:data field'
        '\::header'
        '==:request parameter'
        '@:data file field'
        '\:=:raw json field'
        '\:=@:raw json field file path'
      )
      _describe -t httpparams "parameter types" ops -Q -S ''
    fi

    ret=0

  fi

  # first arg may be a request method
  (( CURRENT == NORMARG )) &&
    _wanted http_method expl 'Request Method' \
            compadd GET POST PUT DELETE HEAD OPTIONS TRACE CONNECT PATCH LINK UNLINK && ret=0

  return $ret
}

_httpie_urls() {
  local ret=1

  if ! [[ -prefix [-+.a-z0-9]#:// ]]; then
    local expl
    compset -S '[^:/]*' && compstate[to_end]=''
    _wanted url-schemas expl 'URL schema' compadd -S '' http:// https:// && ret=0
  else
    _urls && ret=0
  fi

  return $ret

}

_httpie_printflags() {
  local ret=1

  # not sure why this is necessary, but it will complete "-pH" style without it
  [[ $IPREFIX == "-p" ]] && IPREFIX+=" "

  compset -P '(#b)([a-zA-Z]#)'

  local -a flags
  [[ $match[1] != *H* ]] && flags+=( "H:request headers" )
  [[ $match[1] != *B* ]] && flags+=( "B:request body" )
  [[ $match[1] != *h* ]] && flags+=( "h:response headers" )
  [[ $match[1] != *b* ]] && flags+=( "b:response body" )

  _describe -t printflags "print flags" flags -S '' && ret=0

  return $ret
}

_httpie_styles() {
  local -a styles=(abap algol algol_nu arduino auto autumn borland bw
                   colorful default dracula emacs friendly
                   friendly_grayscale fruity github-dark gruvbox-dark gruvbox-light
                   igor inkpot lilypond lovelace  manni material monokai
                   murphy native nord nord-darker one-dark paraiso-dark paraiso-light
                   pastie perldoc pie pie-dark pie-light rainbow_dash
                   rrt sas solarized solarized-dark solarized-light stata
                   stata-dark stata-light tango trac vim vs xcode zenburn)

  _describe -t styles 'style' styles
}

integer NORMARG

_arguments -n -C -s \
  '(-j --json -f)'{-j,--json}'[Data items from the command line are serialized as a JSON object]' \
  '(-f --form -j)'{-f,--form}'[Data items from the command line are serialized as form fields]' \
  '--multipart[Similar to --form, but always sends a multipart/form-data request]' \
  '--boundary=[Specify a custom boundary string for multipart/form-data requests]' \
  '--raw=[This option allows you to pass raw request data without extra processing]' \
  '(-x --compress)'{-x,--compress}'[Content compressed with Deflate algorithm]' \
  '--pretty=[Controls output processing]:output format:(all colors format none)' \
  '(-s --style)'{-s,--style}'=[Output coloring style]:STYLE:_httpie_styles' \
  '--unsorted[Disables all sorting while formatting output]' \
  '--sorted[Re-enables all sorting options while formatting output]' \
  '--response-charset=[Override the response encoding for terminal display purposes]:encoding' \
  '--response-mime=[Override the response mime type for coloring and formatting for the terminal]:mine' \
  '--format-options=[Controls output formatting]:format' \
  '(-p --print)'{-p,--print}'=[String specifying what the output should contain]:print flags:_httpie_printflags' \
  '(-v --verbose)'{-v,--verbose}'[Print the whole request as well as the response]' \
  '(-p -h --headers)'{-h,--headers}'[Print only the response headers]' \
  '(-p -m --meta)'{-m,--meta}'[Print only the response metadata]' \
  '(-p -b --body)'{-b,--body}'[Print only the response body]' \
  '--all[By default, only the final request/response is shown]' \
  '(-S --stream)'{-S,--stream}'[Always stream the output by line, i.e., behave like `tail -f`]' \
  '(-o --output)'{-o,--output}'=[Save output to FILE]:output file:_files' \
  '(-d --download)'{-d,--download}'=[Do not print the response body to stdout]' \
  '(-c --continue)'{-c,--continue}'[Resume an interrupted download]' \
  '(-q --quiet)'{-q,--quiet}'[Do not print to stdout or stderr, except for errors and warnings when provided once]' \
  '(--session-read-only)--session=[Create, or reuse and update a session]:session name (or path)' \
  '(--session)--session-read-only=[Create or read a session without updating it form the request/response exchange]:session name (or path)' \
  '(-a --auth)'{-a,--auth}'=[If only the username is provided (-a username)]:USER\:PASS' \
  '--auth-type=[The authentication mechanism to be used. Defaults to "basic"]:AUTH-TYPE:(basic digest bearer)' \
  '--ignore-netrc[Ignore credentials from .netrc]' \
  '--offline[Build the request and print it but do not actually send it]' \
  '--proxy=[String mapping protocol to the URL of the proxy]:PROXY' \
  '(-F --follow)'{-F,--follow}'[Allow full redirects]' \
  '--max-redirects=[A limit of redirects]:number:' \
  '--max-headers=[The maximum number of response headers to be read]:number:' \
  "--verify=[Enable or disable verification of ssl certificates]:verify certificate:(yes no)" \
  '--ssl=[The desired protocol version to use]:ssl version:(ssl2.3 tls1 tls1.1 tls1.2)' \
  '--ciphers=[A string in the OpenSSL cipher list format]:ciphers' \
  '--cert=[Specify a local cert to use as client side SSL certificate]:cert:_files' \
  '--cert-key=[Specify the private to key to use with SSL]:cert key:_files' \
  '--cert-key-pass=[The passphrase to be used to with the given private key]' \
  '--timeout=[Float describes the timeout of the request (Use socket.setdefaulttimeout() as fallback)]:timeout (seconds)' \
  '--check-status[This flag instructs HTTPie to also check the HTTP status code and exit with an error if the status indicates one]' \
  '--path-as-is[Bypass dot segment URL squashing]' \
  '--chunked[Enable streaming via chunked transfer encoding]' \
  '(-I --ignore-stdin)'{-I,--ignore-stdin}'[Do not attempt to read stdin]' \
  '(- *)--help[show help message]' \
  '(- *)--manual[show the full manual]' \
  "(- *)--version[show program's version number and exit]" \
  '--traceback[Prints exception traceback should one occur]' \
  '--default-scheme=[The default scheme to use if not specified in the URL]:scheme:' \
  '--debug[Prints exception traceback should one occur and other information useful for debugging HTTPie itself]' \
  '*:args:_httpie_params' && return 0

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
