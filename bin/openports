#!/usr/bin/env bash
#
# Use ss to get a list of open ports
#
# Copyright 2023, <PERSON> <<EMAIL>>

set -o pipefail
if [[ -n "$DEBUG" ]]; then
  # shellcheck disable=SC2086
  if [[ "$(echo $DEBUG | tr '[:upper:]' '[:lower:]')" == "verbose" ]]; then
    set -x
  fi
fi

function debug() {
  if [[ -n "$DEBUG" ]]; then
        zsh_debug_echo "$@"
  fi
}

function echo-stderr() {
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  printf '%s\n' "$1" >&2  ## Send message to stderr. Exclude >&2 if you don't want it that way.
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function has() {
  # Check if a command is in $PATH
  which "$@" > /dev/null 2>&1
}

function check-dependency() {
  if ! (builtin command -V "$1" >/dev/null 2>&1); then
    fail "missing dependency: can't find $1 in your PATH"
  fi
}

function check-dependencies() {
  debug "Checking dependencies..."
  # shellcheck disable=SC2041
  # Placeholders for whatever programs you really need
  for dep in "$@"
  do
    if ! has "$dep"; then
      fail "Can't find $dep in your $PATH"
    else
      debug "- Found $dep"
    fi
  done
}

function my-name() {
  basename "$0"
}

function usage() {
      zsh_debug_echo "Usage: $(my-name) ARG ARG"
}

check-dependencies ss

exec sudo ss -nplutO '! ( src = localhost )'  | \
  sed 's/\(udp\|tcp\).*:\([0-9][0-9]*\)/\2\t\1\t/;s/\([0-9][0-9]*\t[udtcp]*\t\)[^u]*users:(("/\1/;s/".*//;s/.*Address:Port.*/Netid\tPort\tProcess/' | \
  sort -nu
