#compdef android
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for the android command (Revision 12)
#  (http://developer.android.com/guide/developing/tools/android.html).
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * <PERSON> <<EMAIL>>
#
# ------------------------------------------------------------------------------


_android() {
  typeset -A opt_args
  local context state line curcontext="$curcontext"

  local ret=1
  
  _arguments -C -A "-*" \
    '(- : *)'{-h,--help}'[get help on a specific command]:command:_android_cmds' \
    '(-s --silent -v --verbose)'{-v,--verbose}'[verbose mode: errors, warnings and informational messages are printed]' \
    '(-v --verbose -s --silent)'{-s,--silent}'[silent mode: only errors are printed out]' \
    '1: :_android_cmds' \
    '*::arg:->args' \
  && ret=0

  case "$state" in
    (args)
      curcontext="${curcontext%:*:*}:android-cmd-$words[1]:"
      case $words[1] in
        (list)
          _arguments -C \
            '1: :_android_list_entities' \
            '*::list-arg:->list-args' \
          && ret=0
          case "$state" in
            (list-args)
              case $words[1] in
                (avd|target)
                  _arguments \
                    '(-0 --null)'{-0,--null}'[terminate lines with \0 instead of \n (e.g. for xargs -0)]' \
                    '(-c --compact)'{-c,--compact}'[compact output (suitable for scripts)]' \
                  && ret=0
                ;;
                (sdk)
                  _arguments \
                    '(-o --obsolete)'{-o,--obsolete}'[install obsolete packages]' \
                    '--proxy-host[HTTP/HTTPS proxy host (overrides settings if defined)]:proxy host:_hosts' \
                    '--proxy-port[HTTP/HTTPS proxy port (overrides settings if defined)]:proxy port number' \
                    '(-s --no-https)'{-s,--no-https}'[use HTTP instead of HTTPS (the default) for downloads]' \
                    '(-u --no-ui)'{-u,--no-ui}'[display list result on console (no GUI)]' \
                  && ret=0
                ;;
              esac
            ;;
          esac
        ;;
        (create)
          _arguments -C \
            '1: :_android_create_entities' \
            '*::create-arg:->create-args' \
          && ret=0
          case "$state" in
            (create-args)
              case $words[1] in
                (avd)
                  _arguments \
                    '(-c --sdcard)'{-c,--sdcard}'[path to a shared SD card image, or size of a new sdcard for the new AVD]:SD card image or size:_files -g "*.img"' \
                    '(-n --name)'{-n,--name}'[name of the new AVD]:name' \
                    '(-a --snapshot)'{-a,--snapshot}'[place a snapshots file in the AVD, to enable persistence]' \
                    '(-p --path)'{-p,--path}'[directory where the new AVD will be created]: :_files -/' \
                    '(-f --force)'{-f,--force}'[forces creation (overwrites an existing AVD)]' \
                    '(-s --skin)'{-s,--skin}'[skin for the new AVD]:skin' \
                    '(-t --target)'{-t,--target}'[target ID of the new AVD]: :_android_targets' \
                  && ret=0
                ;;
                (project)
                  _arguments \
                    '(-n --name)'{-n,--name}'[project name]:project name' \
                    '(-p --path)'{-p,--path}'[the new project'\''s directory]: :_files -/' \
                    '(-k --package)'{-k,--package}'[Android package name for the application]:package name' \
                    '(-a --activity)'{-a,--activity}'[name of the default Activity that is created]:activity name' \
                    '(-t --target)'{-t,--target}'[target ID of the new project]: :_android_targets' \
                  && ret=0
                ;;
                (test-project)
                  _arguments \
                    '(-n --name)'{-n,--name}'[project name]:project name' \
                    '(-p --path)'{-p,--path}'[the new project'\''s directory]: :_files -/' \
                    '(-m --main)'{-m,--main}'[path to directory of the app under test, relative to the test project directory]:path' \
                  && ret=0
                ;;
                (lib-project)
                  _arguments \
                    '(-n --name)'{-n,--name}'[project name]:project name' \
                    '(-p --path)'{-p,--path}'[the new project'\''s directory]: :_files -/' \
                    '(-k --package)'{-k,--package}'[Android package name for the application]:package name' \
                    '(-t --target)'{-t,--target}'[target ID of the new project]: :_android_targets' \
                  && ret=0
                ;;
              esac
            ;;
          esac
        ;;
        (update)
          _arguments -C \
            '1: :_android_update_entities' \
            '*::update-arg:->update-args' \
          && ret=0
          case "$state" in
            (update-args)
              case $words[1] in
                (avd)
                  _arguments \
                    '(-n --name)'{-n,--name}'[name of the AVD to update]: :_android_avd_names' \
                  && ret=0
                ;;
                (project)
                  _arguments \
                    '(-l --library)'{-l,--library}'[directory of an Android library to add, relative to this project'\''s directory]: :_files -/' \
                    '(-p --path)'{-p,--path}'[the project'\''s directory]: :_files -/' \
                    '(-n --name)'{-n,--name}'[project name]:name' \
                    '(-t --target)'{-t,--target}'[target ID to set for the project]: :_android_targets' \
                    '(-s --subprojects)'{-s,--subprojects}'[also updates any projects in sub-folders, such as test projects]' \
                  && ret=0
                ;;
                (test-project)
                  _arguments \
                    '(-p --path)'{-p,--path}'[the project'\''s directory]: :_files -/' \
                    '(-m --main)'{-m,--main}'[directory of the app under test, relative to the test project directory]:path' \
                  && ret=0
                ;;
                (lib-project)
                  _arguments \
                    '(-p --path)'{-p,--path}'[the project'\''s directory]: :_files -/' \
                    '(-t --target)'{-t,--target}'[target ID to set for the project]: :_android_targets' \
                  && ret=0
                ;;
                (sdk)
                  _arguments \
                    '(-o --obsolete)'{-o,--obsolete}'[install obsolete packages]' \
                    '--proxy-host[HTTP/HTTPS proxy host (overrides settings if defined)]:proxy host:_hosts' \
                    '--proxy-port[HTTP/HTTPS proxy port (overrides settings if defined)]:proxy port number' \
                    '(-s --no-https)'{-s,--no-https}'[use HTTP instead of HTTPS (the default) for downloads]' \
                    '(-u --no-ui)'{-u,--no-ui}'[update from command-line (no GUI)]' \
                    '(-f --force)'{-f,--force}'[force replacement of a package or its parts, even if something has been modified]' \
                    '(-t --filter)'{-t,--filter}'[a filter that limits the update to the specified types of packages]: :_android_sdk_update_filters -s ,' \
                    '(-n --dry-mode)'{-n,--dry-mode}'[simulate the update but does not download or install anything]' \
                  && ret=0
                ;;
              esac
            ;;
          esac
        ;;
        (move)
          _arguments -C \
            '1: :_android_move_entities' \
            '*::move-arg:->move-args' \
          && ret=0
          case "$state" in
            (move-args)
              case $words[1] in
                (avd)
                  _arguments \
                    '(-n --name)'{-n,--name}'[name of the AVD to move or rename]: :_android_avd_names' \
                    '(-p --path)'{-p,--path}'[path to the AVD'\''s new directory]: :_files -/' \
                    '(-r --rename)'{-r,--rename}'[new name of the AVD]:name' \
                  && ret=0
                ;;
              esac
            ;;
          esac
        ;;
        (delete)
          _arguments -C \
            '1: :_android_delete_entities' \
            '*::delete-arg:->delete-args' \
          && ret=0
          case "$state" in
            (delete-args)
              case $words[1] in
                (avd)
                  _arguments \
                    '(-n --name)'{-n,--name}'[name of the AVD to delete]: :_android_avd_names' \
                  && ret=0
                ;;
              esac
            ;;
          esac
        ;;
        (display)
          _arguments \
            '1: :_android_display_entities' \
          && ret=0
        ;;
      esac
    ;;
  esac

  return ret
}

(( $+functions[_android_cmds] )) ||
_android_cmds() {
  local commands; commands=(
    'list:list existing targets or virtual devices'
    'create:create new virtual devices or projects'
    'update:update a virtual device, project, SDK or adb'
    'move:move a virtual device'
    'delete:delete a virtual device'
    'avd:displays the AVD Manager window'
    'sdk:displays the SDK Manager window'
    'display:display manager windows'
  )
  _describe -t commands 'command' commands "$@"
}

(( $+functions[_android_list_entities] )) ||
_android_list_entities() {
  local entities; entities=(
    'avd:list existing Android Virtual Devices'
    'target:list existing targets'
    'sdk:list remote SDK repository'
  )
  _describe -t entities 'entity' entities "$@"
}

(( $+functions[_android_create_entities] )) ||
_android_create_entities() {
  local entities; entities=(
    'avd:create a new Android Virtual Device'
    'project:create a new Android project'
    'test-project:create a new Android project for a test package'
    'lib-project:create a new Android library project'
  )
  _describe -t entities 'entity' entities "$@"
}

(( $+functions[_android_update_entities] )) ||
_android_update_entities() {
  local entities; entities=(
    'avd:update an Android Virtual Device to match the folders of a new SDK'
    'project:update an Android project'
    'test-project:update the Android project for a test package'
    'lib-project:update an Android library project'
    'adb:update adb to support the USB devices declared in the SDK add-ons'
    'sdk:update the SDK by suggesting new platforms to install if available'
  )
  _describe -t entities 'entity' entities "$@"
}

(( $+functions[_android_move_entities] )) ||
_android_move_entities() {
  local entities; entities=(
    'avd:move or rename an Android Virtual Device'
  )
  _describe -t entities 'entity' commands "$@"
}

(( $+functions[_android_delete_entities] )) ||
_android_delete_entities() {
  local entities; entities=(
    'avd:delete an Android Virtual Device'
  )
  _describe -t entities 'entity' entities "$@"
}

(( $+functions[_android_display_entities] )) ||
_android_display_entities() {
  local entities; entities=(
    'sdk:display the SDK Manager window'
    'avd:display the AVD Manager window'
  )
  _describe -t entities 'entity' entities "$@"
}

(( $+functions[_android_targets] )) ||
_android_targets() {
  local targets; targets=(${(f)"$(_call_program targets $service list target --compact)"//:/\\:})
  _describe -t targets 'target' targets "$@"
}

(( $+functions[_android_avd_names] )) ||
_android_avd_names() {
  local avd_names; avd_names=(${(f)"$(_call_program targets $service list avd --compact)"//:/\\:})
  _describe -t avd-names 'AVD name' avd_names "$@"
}

(( $+functions[_android_sdk_update_filters] )) ||
_android_sdk_update_filters() {
  local filters; filters=(platform tool platform-tool doc sample extra)
  _values $@ 'filter' "${filters[@]}"
}

_android "$@"

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
