#compdef pylint
#
# this is zsh completion function file.
# generated by genzshcomp(ver: 0.5.1)
#

typeset -A opt_args
local context state line

_arguments -s -S \
  "--help[show this help message and exit]:" \
  "-h[show this help message and exit]:" \
  "--version[show program's version number and exit]:" \
  "--long-help[more verbose help.]" \
  "--rcfile[Specify a configuration file.]::<file>:_files" \
  "--errors-only[In error mode, checkers without error messages are disabled and for others, only the ERROR messages are displayed, and no reports are done by default]" \
  "-E[In error mode, checkers without error messages are disabled and for others, only the ERROR messages are displayed, and no reports are done by default]" \
  "--ignore[Add files or directories to the blacklist. They should be base names, not paths. \[current: CVS\]]::<file>[,<file>...]:_files" \
  "--help-msg[Display a help message for the given message id and exit. The value may be a comma separated list of message ids.]::<msg-id>:_files" \
  "--generate-rcfile[Generate a sample configuration file according to the current configuration. You can put other options before this one to get them in the generated configuration.]" \
  "--enable[Enable the message, report, category or checker with the given id(s). You can either give multiple identifier separated by comma (,) or put this option multiple time.]::<msg ids>:_files" \
  "-e[Enable the message, report, category or checker with the given id(s). You can either give multiple identifier separated by comma (,) or put this option multiple time.]::<msg ids>:_files" \
  "--disable[Disable the message, report, category or checker with the given id(s). You can either give multiple identifier separated by comma (,) or put this option multiple time (only on the command line, not in the configuration file where it should appear only once).]::<msg ids>:_files" \
  "-d[Disable the message, report, category or checker with the given id(s). You can either give multiple identifier separated by comma (,) or put this option multiple time (only on the command line, not in the configuration file where it should appear only once).]::<msg ids>:_files" \
  "--output-format[Set the output format. Available formats are text, parseable, colorized, msvs (visual studio) and html \[current: text\]]::<format>:_files" \
  "-f[Set the output format. Available formats are text, parseable, colorized, msvs (visual studio) and html \[current: text\]]::<format>:_files" \
  "--include-ids[Include message's id in output \[current: no\]]::<y_or_n>:_files" \
  "-i[Include message's id in output \[current: no\]]::<y_or_n>:_files" \
  "--reports[Tells whether to display a full report or only the messages \[current: yes\]]::<y_or_n>:_files" \
  "-r[Tells whether to display a full report or only the messages \[current: yes\]]::<y_or_n>:_files" \
  "*::args:_files"
