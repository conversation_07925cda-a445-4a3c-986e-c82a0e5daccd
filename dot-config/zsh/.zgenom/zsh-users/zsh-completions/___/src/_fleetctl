#compdef fleetctl
# ------------------------------------------------------------------------------
# Copyright (c) 2009-2015 Rob<PERSON> and contributors (see
# https://github.com/ohmyzsh/ohmyzsh/graphs/contributors)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
# ------------------------------------------------------------------------------
# Description
# -----------
#
#  Completion script for fleetctl (https://github.com/coreos/fleet).
#
#
# ------------------------------------------------------------------------------
# Authors
# -------
#
#  * Remi Paulmier (https://github.com/shtouff)
#
# ------------------------------------------------------------------------------

# fleetctl zsh completion

local -a _1st_arguments
_1st_arguments=(
    'cat:Output the contents of a submitted unit'
    'destroy:Destroy one or more units in the cluster'
    'fd-forward:Proxy stdin and stdout to a unix domain socket'
    'help:Show a list of commands or help for one command'
    'journal:Print the journal of a unit in the cluster to stdout'
    'list-machines:Enumerate the current hosts in the cluster'
    'list-unit-files:List the units that exist in the cluster.'
    'list-units:List the current state of units in the cluster'
    'load:Schedule one or more units in the cluster, first submitting them if necessary.'
    'ssh:Open interactive shell on a machine in the cluster'
    'start:Instruct systemd to start one or more units in the cluster, first submitting and loading if necessary.'
    'status:Output the status of one or more units in the cluster'
    'stop:Instruct systemd to stop one or more units in the cluster.'
    'submit:Upload one or more units to the cluster without starting them'
    'unload:Unschedule one or more units in the cluster.'
    'version:Print the version and exit'
)

__task_list ()
{
    local expl
    declare -a tasks

    tasks=(cat destroy fd-forward help journal list-machines list-unit-files \
      list-units load ssh start status stop submit unload version)

    _wanted tasks expl 'help' compadd $tasks
}

__unit_list ()
{
    _wanted application expl 'command' compadd $(command fleetctl list-units | \
      tail -n +2  | awk '{print $1}')
}

local expl

local curcontext="$curcontext" state line
local -A opt_args

_arguments -C \
    ':command:->command' \
    '*::options:->options'

case $state in
  (command)
      _describe -t commands "gem subcommand" _1st_arguments
      return
  ;;

  (options)
    case $line[1] in
      (help)
         _arguments ':feature:__task_list'
      ;;

      (destroy|journal|start|status|stop|unload|cat)
          _arguments '*:feature:__unit_list'
      ;;

      (load|submit)
          _arguments '*:file:_files -g *.service'
      ;;

      (ssh)
          _arguments '*:host:_hosts'
      ;;

      (*)
          _arguments '*:file:_files'
      ;;
    esac
  ;;
esac

# Local Variables:
# mode: Shell-Script
# sh-indentation: 2
# indent-tabs-mode: nil
# sh-basic-offset: 2
# End:
# vim: ft=zsh sw=2 ts=2 et
