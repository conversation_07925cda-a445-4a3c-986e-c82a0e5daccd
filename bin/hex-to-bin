#!/usr/bin/env bash
#
# hex-to-bin
#
# Converts a hexstring to a binary stream
#
# Copyright 2023, <PERSON> <<EMAIL>>

set -o pipefail
if [[ -n "$DEBUG" ]]; then
  set -x
fi

function echo-stderr() {
      zsh_debug_echo "$@" 1  ## Send message to stderr.
}

function fail() {
  echo-stderr "$@"
  exit "${2-1}"  ## Return a code specified by $2 or 1 by default.
}

function has() {
  # Check if a command is in $PATH
  which "$@" > /dev/null 2>&1
}

# hexstring to binary stream
if has xxd; then
      zsh_debug_echo -n "$@" | xxd -p -r
  exit $?
else
  fail "Can't find 'xxd' in PATH"
fi
