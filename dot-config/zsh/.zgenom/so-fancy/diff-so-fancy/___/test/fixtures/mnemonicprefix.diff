[1;33mdiff --git i/diff-so-fancy w/diff-so-fancy[m
[1;33mindex 2323d9b..a280985 100755[m
[1;33m--- i/diff-so-fancy[m
[1;33m+++ w/diff-so-fancy[m
[1;35m@@ -48,10 +48,6 @@[m [mcolorize_context_line () {[m
   $SED -E "s/@@$reset_color $reset_color(.*$)/@@ $dim_magenta\1/g"[m
 }[m
 [m
[1;31m-mark_empty_lines () {[m
[1;31m-  $SED -E "s/^$color_code_regex[\+\-]$reset_color\s*$/$invert_color\1 $reset_escape/g"[m
[1;31m-}[m
[1;31m-[m
 strip_leading_symbols () {[m
   # strip the + and -[m
   $SED -E "s/^($color_code_regex)[\+\-]/\1 /g"[m
[1;35m@@ -83,7 +79,6 @@[m [mcat $input \[m
   | $diff_highlight \[m
   | format_diff_header \[m
   | colorize_context_line \[m
[1;31m-  | mark_empty_lines \[m
   | strip_leading_symbols \[m
   | strip_first_column \[m
   | print_header_clean[m
[1;33mdiff --git i/libs/header_clean/header_clean.pl w/libs/header_clean/header_clean.pl[m
[1;33mindex 23df5e7..54b3da1 100755[m
[1;33m--- i/libs/header_clean/header_clean.pl[m
[1;33m+++ w/libs/header_clean/header_clean.pl[m
[1;35m@@ -9,6 +9,8 @@[m [mmy $remove_file_delete_header = 1;[m
 my $clean_permission_changes  = 1;[m
 my $change_hunk_indicators    = 1;[m
 [m
[1;32m+[m[1;32muse Data::Dump::Color;[m
[1;32m+[m
 #################################################################################[m
 [m
 my $ansi_sequence_regex = qr/(\e\[([0-9]{1,3}(;[0-9]{1,3}){0,3})[mK])?/;[m
[1;35m@@ -29,12 +31,12 @@[m [mfor (my $i = 0; $i <= $#input; $i++) {[m
 	########################################[m
 	# Find the first file: --- a/README.md #[m
 	########################################[m
[1;31m-	} elsif ($line =~ /^$ansi_sequence_regex--- (a\/)?(.+?)(\e|$)/) {[m
[1;32m+[m	[1;32m} elsif ($line =~ /^$ansi_sequence_regex--- ([abiwco]\/)?(.+?)(\e|$)/) {[m
 		$file_1 = $5;[m
 [m
 		# Find the second file on the next line: +++ b/README.md[m
 		my $next = $input[++$i];[m
[1;31m-		$next    =~ /^$ansi_sequence_regex\+\+\+ (b\/)?(.+?)(\e|$)/;[m
[1;32m+[m		[1;32m$next    =~ /^$ansi_sequence_regex\+\+\+ ([abiwco]\/)?(.+?)(\e|$)/;[m
 		if ($1) {[m
 			print $1; # Print out whatever color we're using[m
 		}[m
[1;33mdiff --git i/test/header_clean.bats w/test/header_clean.bats[m
[1;33mindex 4a3e7ee..a8385a5 100644[m
[1;33m--- i/test/header_clean.bats[m
[1;33m+++ w/test/header_clean.bats[m
[1;35m@@ -50,3 +50,8 @@[m [moutput=$( load_fixture "file-moves" | $diff_so_fancy )[m
 	assert_output --partial '@ setup-a-new-machine.sh:33 @'[m
 	assert_output --partial '@ setup-a-new-machine.sh:218 @'[m
 }[m
[1;32m+[m
[1;32m+[m[1;32m@test "mnemonicprefix" {[m
[1;32m+[m	[1;32moutput=$( load_fixture "mnemonicprefix" | $diff_so_fancy )[m
[1;32m+[m	[1;32massert_output --partial '@ setup-a-new-machine.sh:33 @'[m
[1;32m+[m[1;32m}[m
[38;5;11mdiff --git c/hello.txt i/hello.txt[m
[38;5;11mdeleted file mode 100644[m
[38;5;11mindex 0c767bc..0000000[m
[38;5;11m--- c/hello.txt[m
[38;5;11m+++ /dev/null[m
[36m@@ -1 +0,0 @@[m
[38;5;10m-[m[38;5;10mHI THERE[m
